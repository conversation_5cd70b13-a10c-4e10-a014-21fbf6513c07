{"name": "ec-ui", "version": "0.1.0", "private": true, "scripts": {"serve-dev": "vue-cli-service serve --mode serve-dev", "serve-dat": "vue-cli-service serve --mode serve-dat", "serve-uat": "vue-cli-service serve --mode serve-uat", "serve-prd": "vue-cli-service serve --mode serve-prd", "build-dat": "vue-cli-service build --mode build-dat", "build-uat": "vue-cli-service build --mode build-uat", "build-prd": "vue-cli-service build --mode build-prd", "build-report": "vue-cli-service build --mode build-prd --report"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue/composition-api": "^1.7.2", "animate.css": "^4.1.1", "axios": "^0.21.0", "compressorjs": "^1.1.1", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "jsencrypt": "^3.3.2", "markdown-it-vue": "^1.1.7", "moment": "^2.29.1", "sa-sdk-javascript": "^1.23.3", "trtc-sdk-v5": "^5.10.1", "vant": "^2.13.2", "vconsole": "^3.14.6", "video.js": "^7.20.3", "vue": "2.6.11", "vue-clipboard2": "^0.3.3", "vue-demi": "^0.14.10", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vuescroll": "^4.17.3", "vuex": "^3.4.0", "vxe-table": "^3.6.8", "xe-utils": "^3.5.7"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.9", "@vue/cli-plugin-vuex": "^4.5.9", "@vue/cli-service": "~4.5.0", "autoprefixer": "^9.8.6", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "compression-webpack-plugin": "^3.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "filemanager-webpack-plugin": "^3.0.0", "less": "^3.5.0", "less-loader": "^7.1.0", "moment-locales-webpack-plugin": "^1.2.0", "postcss-pxtorem": "^5.1.1", "prerender-spa-plugin": "^3.4.0", "vue-template-compiler": "2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not ie<=8", "ios >=8", "android >=4.0"]}