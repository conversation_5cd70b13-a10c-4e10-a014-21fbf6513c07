user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

	server {
		listen       8080;
        #     5001;
		server_name  localhost;
		# 获取当前多页面的文件名
       	if ( $uri ~ /([^/]*.html)/ ){
            set  $productPage $1;
        }

        # 产品页面
        location ~ /[^/]*.html/ {
            root   html;
            # 设置多页面首页
            index  $productPage;
   			try_files $uri $uri/ /$productPage; 
        }
        gzip on; # 开启Gzip
        gzip_static on; # 开启静态文件压缩
        gzip_min_length 1k; # 不压缩临界值，大于1K的才压缩
        gzip_buffers 4 16k;
        gzip_http_version 1.1;
        gzip_comp_level 2;
        gzip_types text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/xml+rss; # 进行压缩的文件类型
        gzip_vary on;
        gzip_proxied expired no-cache no-store private auth;
        gzip_disable "MSIE [1-6]\.";

		location / {
		    add_header Access-Control-Allow-Origin *;
			root   /usr/share/nginx/html;
			try_files $uri /index.html;
			index  index.html index.htm;
		}


		error_page   500 502 503 504  /50x.html;
		location = /50x.html {
			root   /usr/share/nginx/html;
		}

	}
}
