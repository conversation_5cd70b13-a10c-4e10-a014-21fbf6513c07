<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-09-30 14:48:51
 * @LastEditors  : <PERSON><PERSON><PERSON>
 * @LastEditTime : 2023-07-20 17:14:58
 * @Description  :
-->
<template>
	<div id="app" class="app-content" data-server-rendered="true">
		<router-view v-if="isRouterAlive &&!$route.meta.keepAlive"></router-view>
		<keep-alive>
			<router-view v-if="isRouterAlive &&$route.meta.keepAlive"></router-view>
		</keep-alive>
	

	</div>
</template>

<script>

export default {
	name: 'App',
	components: {},
	
	props: {},
	data() {
		return {
			isRouterAlive: true
		}
	},
	watch: {
        $route(to, from) {
			console.log('微信静默授权', this.$CONFIG);
			console.log('to', to);
			console.log('from',from);
        }
    },
	provide () {
		return {
			reload: this.reload
		}
	},
	mounted() {
		//if (this.isWeChatBrowser()) {
		//	this.$wxJsSdk.wxShareInit({});
		//}
		setTimeout(() => {
			document.getElementById('loading').style.display = 'none';
		}, 1000)
	},
	methods: {
		reload () {  //   调用该方法可刷新组件
		// alert('11')
			this.isRouterAlive = false
			this.$nextTick(() => {    //$nextTick() 方法传入一个回调函数，在下一次渲染页面时调用该回调
				this.isRouterAlive = true
			})
		},
	}
}
</script>

<style lang='less'>
	@import "./style/icon/iconfont.less";
	@import "./style/mixins.less";
	.van-toast{
		max-width: 50% !important;
	}
</style>
