/*
 * <AUTHOR> wa<PERSON><PERSON><PERSON>
 * @Date         : 2021-11-11 16:50:08
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-09-03 18:25:21
 * @Description  :
 */
import wxJsSdk from '@/utils/wx.js';
import Vue from 'vue'
import VueRouter from 'vue-router'
import home from '../views/home'
import { PLATFORM_CODE } from '@/constant'


// 判断是否是微信端
Vue.prototype.$isWeChatBrowser = function () {
	var ua = navigator.userAgent.toLowerCase();
	// 判断当前浏览器是否是微信
	return ua.match(/MicroMessenger/i) == "micromessenger" ? true : false;
}
Vue.prototype.$isIos = function () {
	// var ua = navigator.userAgent.toLowerCase();
	let isIos = false;
	if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
		//Ios
		isIos = true;
	} else if (/(Android)/i.test(navigator.userAgent)) {
		//Android终端
		isIos = false;
		console.log('Android')
	}
	return isIos
}
Vue.use(VueRouter)
const routes = [
	//主页预加载
	{
		path: '/',
		name: 'home',
		meta: {
			index: 1,
			title: '',
			//client: 'wx',
		},
		component: home
	},
	{
		path: '/baoquan',
		name: 'Baoquan',
		component: () => import('@/views/serves/Baoquan.vue')
	},
	{
		path: '/lipei',
		name: 'Lipei',
		component: () => import('@/views/serves/Lipei.vue')
	},
	{
		path: '/shbfw',
		name: 'Shbfw',
		component: () => import('@/views/serves/Shbfw.vue')
	},
	{
		path: '/lipeiDecode',
		name: 'LipeiDecode',
		component: () => import('@/views/serves/LipeiDecode.vue')
	},
	{
		path: '/ocrIdcard',
		name: 'OcrIdcard',
		component: () => import('@/views/serves/OcrIdcard.vue')
	},
	{
		path: '/ocrBank',
		name: 'OcrBank',
		component: () => import('@/views/serves/OcrBank.vue')
	},
	{
		path: '/login',
		name: 'login',
		component: () => import('@/views/app/Login.vue')
	},
	{
		path: '/404',
		name: '404',
		component: () => import('@/views/404')
	},
	{
		path: '/:pathMatch(.*)',
		redirect: '/404'
	},
];


const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err)
}

const router = new VueRouter({
	mode: 'hash',
	base: process.env.VUE_APP_PATH_ROUTER_BASE,
	routes
});

// 不校验登录
let ignoreLoginList = ['login', 'OcrIdcard','OcrBank'];
router.beforeEach((to, from, next) => {
	console.log("from=====", from);
	console.log("to=====", to);
	
	if (to.meta.title) {
		document.title = to.meta.title;
	}
	
	if (ignoreLoginList.indexOf(to.name) === -1) {
		let user = localStorage.getItem('UserInfo');
	
		if(!user) {
		    router.push({
		        path: '/login'
		    });
		    return;
		}
	}

	next()
});

console.log(routes, 'routes>>>')

export default router
