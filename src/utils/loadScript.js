//可回溯apiUrl  prd
window.ecConfig = {
	apiUrl: 'https://wx.e-soochowlife.com/semit/ecback/h5/pushData'
};

//动态加载外部js
//@param RESOURCE_LIST 外部地址集合["address"]
export function loadVoLteResourceList(RESOURCE_LIST, success) {
	return new Promise(r => {
		RESOURCE_LIST.reduce((res, el) => res.then(() => loadScript(el)), Promise.resolve()).then(() => {
			r()
		}).catch((error) => {
			console.error('前置js资源加载失败:', error.name, error.message)
			return Promise.reject(error)
		})
	})
}

export function loadScript(url) {
	return new Promise((resolve, reject) => {
		const script = document.createElement('script')

		script.onload = () => resolve()

		script.onerror = () => reject(new Error(`Load script from ${url} failed`))

		script.src = url
		const head =
			document.head || document.getElementsByTagName('head')[0]
			; (document.body || head).appendChild(script)
	})
}


//可以传入多个地址
loadVoLteResourceList([
	"https://sync-web.cloud-ins.cn/sdk/source-audio-auto/tx-sync.min.js",
	"https://wx.e-soochowlife.com/semith5/libs/pdfjs/pdf.js",
	"https://wx.e-soochowlife.com/semith5/libs/pakojs/1.0.6/pako.min.js",
	"https://wx.e-soochowlife.com/semith5/libs/pakojs/pakoUtil.js",
	"https://wx.e-soochowlife.com/semith5/libs/ecback/ecback-sdk-h5.js",
	"https://wx.e-soochowlife.com/semith5/libs/jweixin/jweixin-1.6.0.js"
])