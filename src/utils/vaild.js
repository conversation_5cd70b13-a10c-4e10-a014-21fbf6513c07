/**
 * 检验手机号码
 */
const isPhone = (number) => {
        var reg = /^[1][3-9][0-9]{9}$/;

        if (!number) { return; }
        if (reg.test(number)) {
            return true;
        }
        if (!reg.test(number)) {
            return false;
        }
    },

    /**
 *
 * 姓名校验
 */
    isName = (name) => {
        let reg = /^[\u4e00-\u9fa5\s\.]{2,20}$|^[A-Za-z\s\.]{1,40}$/;

        if (!name) { return; }
        if (reg.test(name)) {
            return true;
        }
        if (!reg.test(name)) {
            return false;
        }
    },
    /**
     *
     * 护照校验 8-9位
     */

    isPassport = (code) => {
        let val = code.replace(/\s*/g, ''),
            reg = /^[A-Za-z0-9]{8,9}$/;

        if (reg.test(val)) {
            return true;
        }
        if (!reg.test(val)) {
            return false;
        }
    },

    /**
     *
     * 港澳居民来往内地通行证
     */
    isHongKongMacao = (code) => {
        let reg = /^[A-Za-z0-9]{7,20}$/;

        if (reg.test(code)) {
            return true;
        }
        if (!reg.test(code)) {
            return false;
        }

    },

    /**
     *
     * 台湾居民来往大陆通行证
     */
    isTaiwan = (code) => {
        let reg = /^[A-Za-z0-9]{7,20}$/;

        if (reg.test(code)) {
            return true;
        }
        if (!reg.test(code)) {
            return false;
        }
    },

    /**
     *
     * 出生证
     */
    isBeBornId = (code) => {
        let reg = /^[A-Za-z0-9]{5,21}$/;

        if (!reg.test(code)) {
            return false;
        }
        return true;

    },

    /**
     *
     * 港澳台居住证
     */
    isPermitId = (code) => {
        let reg = /^8[123]0000(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;

        if(!reg.test(code)) {
            return false;
        }
        return true;


    },

    /**
     * 校验邮箱
     */
    isEmail = (number) => {
        var reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;

        if (!number) { return; }
        if (reg.test(number)) {
            return true;
        }
        if (!reg.test(number)) {
            return false;
        }
    },

    /**
    * 校验身份证
    *
    */

    isIdcard = (str) => {
        var tip = '';
        let code = str.replace(/\s*/g, ''),
            city = { 11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林', 23: '黑龙江 ', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东', 41: '河南', 42: '湖北 ', 43: '湖南', 44: '广东', 45: '广西', 46: '海南', 50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏 ', 61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外 ' },
            pass = true;

        if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
            tip = '身份证号格式错误';
            pass = false;
        } else if (!city[code.substr(0, 2)]) {
            tip = '地址编码错误';
            pass = false;
        } else if (code.length === 18) {
        //18位身份证需要验证最后一位校验位
            code = code.split('');
            //∑(ai×Wi)(mod 11)
            //加权因子
            let factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
                //校验位
                parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2],
                sum = 0,
                ai = 0,
                wi = 0;

            for (let i = 0; i < 17; i++) {
                ai = code[i];
                wi = factor[i];
                sum += ai * wi;
            }
            //const last = parity[sum % 11];
            //最后一位不区分大小写

            if (code[17] === 'x') { code[17] = code[17].toUpperCase(); }
            if (parity[sum % 11] !== code[17]) {
                tip = '校验位错误';
                pass = false;
            }
        }
        console.log(tip);
        return pass;
    };


/**
* 根据出生日期算出年龄
*
*/
export function getAge(strBirthday) {
    let returnAge,
        strBirthdayArr = strBirthday.split('-'),
        birthYear = strBirthdayArr[0],
        birthMonth = strBirthdayArr[1],
        birthDay = strBirthdayArr[2],

        d = new Date(),
        nowYear = d.getFullYear(),
        nowMonth = d.getMonth() + 1,
        nowDay = d.getDate();

    if (nowYear === birthYear) {
        returnAge = 0;//同年 则为0岁
    } else {
        let ageDiff = nowYear - birthYear; //年之差

        if (ageDiff > 0) {
            if (nowMonth === birthMonth) {
                let dayDiff = nowDay - birthDay;//日之差

                if (dayDiff < 0) {
                    returnAge = ageDiff - 1;
                } else {
                    returnAge = ageDiff;
                }
            } else {
                let monthDiff = nowMonth - birthMonth;//月之差

                if (monthDiff < 0) {
                    returnAge = ageDiff - 1;
                } else {
                    returnAge = ageDiff;
                }
            }
        } else {
            returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
        }
    }

    return returnAge;//返回周岁年龄
}


/**
* 根据身份证号码获取年龄
*
*/
export function idnoGetAge(identityCard) {
    var len = String(identityCard).length,
        strBirthday = '',
        //时间字符串里，必须是“/”
        birthDate = new Date(strBirthday),
        nowDateTime = new Date(),
        age = nowDateTime.getFullYear() - birthDate.getFullYear();

    if (len === 0) {
        return 0;
    }
    if (len !== 15 && len !== 18){
        //身份证号码只能为15位或18位其它不合法
        return 0;
    }


    if (len === 18){
        //处理18位的身份证号码从号码中得到生日和性别代码
        strBirthday = identityCard.substr(6, 4) + '/' + identityCard.substr(10, 2) + '/' + identityCard.substr(12, 2);
    }
    if (len === 15) {
        strBirthday = '19' + identityCard.substr(6, 2) + '/' + identityCard.substr(8, 2) + '/' + identityCard.substr(10, 2);
    }

    //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1

    if (nowDateTime.getMonth() < birthDate.getMonth() || nowDateTime.getMonth() === birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate()) {
        age--;
    }
    return age;
}


/**
* 根据身份证号码获取出生日期
*
*/
export function getBirthday(code) {
    let birthday = '',
        idCard = code.replace(/\s*/g, '');

    if (idCard !== null && idCard !== '') {
        if (idCard.length === 15) {
            birthday = '19' + idCard.substr(6, 6);
        } else if (idCard.length === 18) {
            birthday = idCard.substr(6, 8);
        }
        birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-');
    }
    return birthday;
}

/**
* 判断是否是微信浏览器
*/
export function checkWeChatBrowser() {
    const ua = navigator.userAgent.toLowerCase();

    if (ua.match(/MicroMessenger/i) === 'micromessenger') {
        return true; // 微信浏览器
    }

    return false; // 非微信浏览器

}

export {
    isPhone,
    isName,
    isPassport,
    isHongKongMacao,
    isTaiwan,
    isBeBornId,
    isPermitId,
    isEmail,
    isIdcard
};