/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-11-20 17:50:49
 * @LastEditors  : JiangC<PERSON>
 * @LastEditTime : 2023-05-31 11:33:47
 * @Description  : http请求封装，axios配置 
 */
import Vue from 'vue'
import axios from 'axios'
import config from '@/env.config.js'
import router from '../router/router.index'

const newAxios = axios.create({
	baseURL: config.baseURL,
	timeout: 50000,
	headers: {
		'Content-Type': 'application/json'
	}
});

const err = (response) => {
	console.log(response)
	if (response.data) {
		console.log('response.data: ', response.data);
		switch (response.data.code) {
			case '403':
				console.error({
					code: `${response.data.code}`,
					message: '拒绝访问!',
					errApi: response.config.url,
				})
				break
			case 404:
				console.error({
					code: `${response.data.code}`,
					message: '很抱歉，资源未找到!',
					errApi: response.config.url,
				})
				break
			case 504:
				console.error({
					code: `${response.data.code}`,
					message: `网络超时!`,
					errApi: response.config.url,
				})
				break
			case 401:
				setTimeout(() => {
					localStorage.removeItem("Token");
					localStorage.removeItem("UserInfo");

					router.push({
						name: "login",
						query: {},
					});
				}, 2000)
				break;
			case 501:
				console.log(response.data.msg);
				Vue.prototype.$toast('登录信息失效，请重新登录')
				setTimeout(() => {
					localStorage.removeItem("Token");
					localStorage.removeItem("UserInfo");

					router.push({
						name: "login",
						query: {},
					});
				}, 2000)

				break
			default:
				//Vue.prototype.$dialog.alert({
				//	title: `${response.data.code}`,
				//	message: `${response.data.msg}`
				//});
				console.error({
					code: `${response.data.code}`,
					message: `${response.data.msg}`,
					errApi: response.config.url,
				})
		}
	}
	return Promise.reject(response.data)
};

//http 请求 拦截器
newAxios.interceptors.request.use(
	conf => {
		//Vue.prototype.$toast.loading({
		//	duration: 0, // 持续展示 toast
		//	message: '加载中...',
		//	forbidClick: true,
		//});
		// 在发送请求之前做些什么

		const token = localStorage.getItem('Token');
		if (token) {
			conf.headers['X-Access-Token'] = token;
		}

		if (conf.data instanceof FormData) {
			console.log('multi');
			conf.headers['Content-Type'] = null;
		}
		return conf;
	},
	error => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 响应拦截器
newAxios.interceptors.response.use((response) => {
	if (response.data.code == '200' || response.data.code == '0000') {
		if (typeof(response.data.data) == "string") {
			return response.data;
		} else {
			return {
				...response.data,
				msg: response.data.msg || response.data.message
			};
		}

	} else {
		return err(response);
	}
}, err)

export {
	newAxios as axios
};