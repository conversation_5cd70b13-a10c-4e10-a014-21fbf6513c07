/*
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2023-05-08 13:53:48
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-08-28 10:41:55
 * @Description  : 
 */

import Vue from 'vue'

// 保存到缓存中
const $dict = {
	dictCode: {
		relationOption1: '00:本人,33:配偶,32:子女,31:父母',
		relationOption2: '00:本人,33:配偶,32:子女',
		isSocialInsOption: '1:有（含新农合）,0:无社保',
		isNewCitizenOption: '1:是,0:否',
		isHighRiskOccupations: '1:高危职业,0:非高危职业',
		payIntv1: '1:按月交费,0:一次性交清',
		shourTermPayIntv: '1:按月交费,12:一次性交清',
		payIntv2:'12:按年交费',
		idStartDateOption: '0:长期,1:非长期',
		grpNameOption:'0:是,1:否',
		//启明星2号
		isSocialInsOption2: '1:有社保,0:无社保',
		relationOption3: '32:子女',
		idStartDateOption2:'9999-01-01:长期,B:非长期'
		
	},
	isStrNull(value) {
		return !(!value || value === undefined || value === null || value === 'null' || value === 'undefined')
	},
	getDictCode(dictKey) {
		const dict_data = localStorage.getItem('dictCode');
		return !this.isStrNull(dict_data) ? null : JSON.parse(dict_data)[dictKey];
	},
	setDictCode(dictKey, data) {
		let dict_data = localStorage.getItem('dictCode');
		if (!this.isStrNull(dict_data)) {
			dict_data = new Object()
		} else {
			dict_data = JSON.parse(localStorage.getItem('dictCode'));
		}
		dict_data[dictKey] = data;
		localStorage.setItem('dictCode', JSON.stringify(dict_data));
	},
	
	// 问题类型
	questionType: {
		text: '1', //文字
		pic: '2',   // 图片
		file: '3', // 文件
	}
}

// localStorage.setItem('dictCode', JSON.stringify($dict.dictCode))

Vue.prototype.$dict = $dict;