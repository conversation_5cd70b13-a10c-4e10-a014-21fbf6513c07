/*************************** 正则工具类 ***************************/
export const RegexUtils = {
	/*
	用途：检查输入手机号码是否正确
	输入：
	s：字符串
	返回：
	如果通过验证返回true,否则返回false
	*/

	checkMobile(s) {
		let reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
		// 使用弱校验
		// let reg = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[25679]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
		let re = new RegExp(reg);
		if (re.test(s)) {
			return true;
		} else {
			return false;
		}
	},

	checkPassword(s) {
		let reg = /^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).{8,}$/;
		let re = new RegExp(reg);
		if (re.test(s)) {
			return true;
		} else {
			return false;
		}
	},

	/**
	 * 检查输入的身份证号是否正确
	 * 输入:cardNoId  字符串
	 *  返回:true 或 flase; true表示格式正确
	 */
	checkCard(cardNoId) {
		if (cardNoId != '' && cardNoId != null && cardNoId != undefined) {
			var city = {
				11: "北京",
				12: "天津",
				13: "河北",
				14: "山西",
				15: "内蒙古",
				21: "辽宁",
				22: "吉林",
				23: "黑龙江 ",
				31: "上海",
				32: "江苏",
				33: "浙江",
				34: "安徽",
				35: "福建",
				36: "江西",
				37: "山东",
				41: "河南",
				42: "湖北 ",
				43: "湖南",
				44: "广东",
				45: "广西",
				46: "海南",
				50: "重庆",
				51: "四川",
				52: "贵州",
				53: "云南",
				54: "西藏 ",
				61: "陕西",
				62: "甘肃",
				63: "青海",
				64: "宁夏",
				65: "新疆",
				71: "台湾",
				81: "香港",
				82: "澳门",
				91: "国外 "
			};
			if (!cardNoId || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(cardNoId)) {
				return false;
			} else if (!city[cardNoId.substr(0, 2)]) {
				return false;
			} else {
				//18位身份证需要验证最后一位校验位
				if (cardNoId.length == 18) {
					cardNoId = cardNoId.split('');
					//∑(ai×Wi)(mod 11)
					//加权因子
					var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
					//校验位
					var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
					var sum = 0;
					var ai = 0;
					var wi = 0;
					for (var i = 0; i < 17; i++) {
						ai = cardNoId[i];
						wi = factor[i];
						sum += ai * wi;
					}
					var last = parity[sum % 11];
					if (last != cardNoId[17].toUpperCase()) {
						return false;
					} else {
						return true;

					}
				}else{
					return false
				}
			}
		} else {
			return false;
		}
	},

	/**
	 * 检查输入的字符是否具有特殊字符
	 * 输入:str  字符串
	 * 返回:true 或 flase; true表示包含特殊字符
	 * 主要用于注册信息的时候验证
	 */
	checkQuote(str) {
		let items = new Array("~", "`", "!", "@", "#", "$", "%", "^", "&", "*", "{", "}", "[", "]", "(", ")");
		items.push(":", ";", "'", "|", "\\", "<", ">", "?", "/", "<<", ">>", "||", "//");
		items.push("admin", "administrators", "administrator", "管理员", "系统管理员");
		items.push("select", "delete", "update", "insert", "create", "drop", "alter", "trancate");
		str = str.toLowerCase();
		for (let i = 0; i < items.length; i++) {
			if (str.indexOf(items[i]) >= 0) {
				return true;
			}
		}
		return false;
	},

	/*
	用途：检查输入的电话号码格式是否正确
	输入：
	strPhone：字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	checkPhone(strPhone) {
		let phoneRegWithArea = /^[0][1-9]{2,3}-[0-9]{5,10}$/;
		let phoneRegNoArea = /^[1-9]{1}[0-9]{5,8}$/;
		//let prompt = "您输入的电话号码不正确!"
		if (strPhone.length > 9) {
			if (phoneRegWithArea.test(strPhone)) {
				return true;
			} else {
				//alert( prompt );
				return false;
			}
		} else {
			if (phoneRegNoArea.test(strPhone)) {
				return true;
			} else {
				//alert(prompt);
				return false;
			}

		}
	},
	/*
	用途：检查输入字符串是否为空或者全部都是空格
	输入：str
	返回：
	如果全是空返回true,否则返回false
	*/
	isNull(str) {
		if (str == "") return true;
		let regu = "^[ ]+$";
		let re = new RegExp(regu);
		return re.test(str);
	},

	/*
	用途：检查输入对象的值是否符合整数格式
	输入：str 输入的字符串
	返回：如果通过验证返回true,否则返回false
	*/
	isInteger(str) {
		let regu = /^[-]{0,1}[0-9]{1,}$/;
		return regu.test(str);
	},


	/*
	用途：检查输入字符串是否符合正整数格式
	输入：
	s：字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	isNumber(s) {
		let regu = "^[0-9]+$";
		let re = new RegExp(regu);
		if (s.search(re) != -1) {
			return true;
		} else {
			return false;
		}
	},
	/*
	用途：检查输入对象的值是否符合E-Mail格式
	输入：str 输入的字符串
	返回：如果通过验证返回true,否则返回false
	*/
	isEmail(str) {
		let myReg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
		if (myReg.test(str)) return true;
		return false;
	},
	/*
	用途：检查输入字符串是否只由英文字母和数字组成
	输入：
	s：字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	isNumberOrLetter(s) { //判断是否是数字或字母
		let regu = "^[0-9a-zA-Z]+$";
		let re = new RegExp(regu);
		if (re.test(s)) {
			return true;
		} else {
			return false;
		}
	},
	/*
	用途：检查输入字符串是否只由汉字、字母、数字组成
	输入：
	value：字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	isChinaOrNumbOrLett(s) { //判断是否是汉字、字母、数字组成
		let regu = "^[0-9a-zA-Z\u4e00-\u9fa5]+$";
		let re = new RegExp(regu);
		if (re.test(s)) {
			return true;
		} else {
			return false;
		}
	},
	/*
	用途：判断是否是日期
	输入：date：日期；fmt：日期格式
	返回：如果通过验证返回true,否则返回false
	*/
	isDate(date, fmt) {
		if (fmt == null) fmt = "yyyyMMdd";
		let yIndex = fmt.indexOf("yyyy");
		if (yIndex == -1) return false;
		let year = date.substring(yIndex, yIndex + 4);
		let mIndex = fmt.indexOf("MM");
		if (mIndex == -1) return false;
		let month = date.substring(mIndex, mIndex + 2);
		let dIndex = fmt.indexOf("dd");
		if (dIndex == -1) return false;
		let day = date.substring(dIndex, dIndex + 2);
		if (!RegexUtils.isNumber(year) || year > "2100" || year < "1900") return false;
		if (!RegexUtils.isNumber(month) || month > "12" || month < "01") return false;
		if (day > RegexUtils.getMaxDay(year, month) || day < "01") return false;
		return true;
	},
	getMaxDay(year, month) {
		if (month == 4 || month == 6 || month == 9 || month == 11)
			return "30";
		if (month == 2)
			if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0)
				return "29";
			else
				return "28";
		return "31";
	},
	/*
	用途：字符1是否以字符串2结束
	输入：str1：字符串；str2：被包含的字符串
	返回：如果通过验证返回true,否则返回false
	*/
	isLastMatch(str1, str2) {
		let index = str1.lastIndexOf(str2);
		if (str1.length == index + str2.length) return true;
		return false;
	},

	/*
	用途：字符1是否以字符串2开始
	输入：str1：字符串；str2：被包含的字符串
	返回：如果通过验证返回true,否则返回false
	*/
	isFirstMatch(str1, str2) {
		let index = str1.indexOf(str2);
		if (index == 0) return true;
		return false;
	},
	/*
	用途：字符1是包含字符串2
	输入：str1：字符串；str2：被包含的字符串
	返回：如果通过验证返回true,否则返回false
	*/
	isMatch(str1, str2) {
		let index = str1.indexOf(str2);
		if (index == -1) return false;
		return true;
	},

	/*
	用途：检查输入的起止日期是否正确，规则为两个日期的格式正确，
	且结束如期>=起始日期
	输入：
	startDate：起始日期，字符串
	endDate：结束如期，字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	checkTwoDate(startDate, endDate) {
		if (!RegexUtils.isDate(startDate)) {
			alert("起始日期不正确!");
			return false;
		} else if (!RegexUtils.isDate(endDate)) {
			alert("终止日期不正确!");
			return false;
		} else if (startDate > endDate) {
			alert("起始日期不能大于终止日期!");
			return false;
		}
		return true;
	},
	/*
	用途：检查输入的Email信箱格式是否正确
	输入：
	strEmail：字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	checkEmail(strEmail) {
		//let emailReg = /^[_a-z0-9]+@([_a-z0-9]+\.)+[a-z0-9]{2,3}$/;
		let emailReg = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
		if (emailReg.test(strEmail)) {
			return true;
		} else {
			alert("您输入的Email地址格式不正确！");
			return false;
		}
	},

	checkPsw(strPsw) {
		let pswReg =/^(?![A-z0-9]+$)(?=.[^%&',;=?$\x22])(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).{8,}$/;
		if (pswReg.test(strPsw)) {
			return true;
		} else {
			// alert("您输入的密码格式不正确！");
			return false;
		}
	},

	checkBigLetter(strPsw) {
		let pswReg = /[A-Z]+/;
		if (pswReg.test(strPsw)) {
			return true;
		} else {
			// alert("您输入的密码格式不正确！");
			return false;
		}
	},


	/*
	用途：检查输入的起止日期是否正确，规则为两个日期的格式正确或都为空
	且结束日期>=起始日期
	输入：
	startDate：起始日期，字符串
	endDate： 结束日期，字符串
	返回：
	如果通过验证返回true,否则返回false
	*/
	checkPeriod(startDate, endDate) {
		if (!RegexUtils.checkDate(startDate)) {
			alert("起始日期不正确!");
			return false;
		} else if (!RegexUtils.checkDate(endDate)) {
			alert("终止日期不正确!");
			return false;
		} else if (startDate > endDate) {
			alert("起始日期不能大于终止日期!");
			return false;
		}
		return true;
	},
	/**
	用途：判断字符串是否只包含中文
	输入：
	str： 需要验证的字符串
	返回：
	如果通过验证返回true,否则返回false
	 */
	isChinese(str) {
		let regu = "^[\u4e00-\u9fa5·]+$";
		let re = new RegExp(regu);
		if (re.test(str)) {
			return true;
		} else {
			return false;
		}
	},
   
    ChineseOrE(value){
        let regu = '^[a-zA-Z]{2,50}$|^[\u4E00-\u9FA5]{2,20}$';
		let re = new RegExp(regu);
		if (re.test(value)) {
			return true;
		} else {
			return false;
		}
    },
	/**
	用途：姓名+身份证真实性校验
	输入：
	name： 姓名
	idno: 身份证号
	返回：
	 异步函数 promise
	 */
	nameAndIdnotruth(name, idno) {
		return new Promise((resolve) => {
			resolve({
				name,
				idno
			})
		});
	},
	/**
	 用途：bmi校验
	 输入：
	 weight： 身高
	 height: 体重
	 age：年龄，非必填
	 返回：
	 如果符合bmi校验返回true，否则返回false
	 */
	checkBMI(weight, height, age) {
		const maxSome = (weight / ((height / 100) * (height / 100))) >= 33;
		const minSome = weight / ((height / 100) * (height / 100)) < 16;
		if (age >= 18 && (maxSome || minSome)) {
			return false
		}
		return true
	},
    
	//证件校验
	checkNoid(type) {
		return function (str) {
            if(type==='1'){
                const isGangAO=(str)=>{
					return /^[a-zA-Z]{1}[0-9a-zA-Z]{6,19}$/.test(str) && /\d+/.test(str)
				}
                   return isGangAO(str)
            }else{
                var regObj = {
                    '1': /^[a-zA-Z]{1}[0-9a-zA-Z]{6,}$/,
                    'G': /^[HM]\d{8,}$/,
                    'T': /^\d{8,}$/,
                    'I':/^[a-zA-Z]{3}[0-9]{12,}$/,
                }
                var reg = regObj[type] || /w/;
                return reg.test(str);
            }
		}
	},
    /**
 * 只包含长度范围内的正则
 * type:'限制的类型'
 */
    limtByType(min,max,str,type='CN') {
        let regu =''
        switch (type) {
            case "CN":
                regu="^[\u4E00-\u9FA5\uf900-\ufa2d·s]{"+min+","+max+"}$";
                break;
            case "CNL"://中文和字母
                regu="^[a-zA-Z0-9]{"+min+","+max+"}$";
                break;
            default:
                break;
        }
        let re = new RegExp(regu);
        if (re.test(str)) {
            return true;
        } else {
            return false;
        }
    },
}



