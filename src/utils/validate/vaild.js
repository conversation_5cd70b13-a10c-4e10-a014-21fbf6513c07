import * as _RegexUtils from './regex'
const {
	RegexUtils
} = _RegexUtils

const verifyRules = {
	// 姓名
	checkName: (person) => [{
		required: true,
		message: '请输入' + person + '的姓名'
	}, {
		validator: RegexUtils.isChinese,
		message: '请输入' + person + '的真实姓名'
	}, {
		validator: (val) => {
			if (val && val.length < 2) {
				return false
			}
			return true
		},
		message: person + '姓名长度不得少于两个中文字符'
	}, {
		validator: (val) => {

			if (val && val.length > 20) {
				return false
			}
			return true
		},
		message: person + '姓名长度不得大于二十个中文字符'
	}],
	//身份证号
	identityId: (person) => [{
		required: true,
		message: '请输入' + person + '的身份证号'
	}, {
		validator: RegexUtils.checkCard,
		message: '请输入' + person + '正确的身份证号'
	}],
	//证件号
	identityId_type: (person) => [{
		required: true,
		message: '请输入' + person + '的证件号'
	}, {
		validator: RegexUtils.checkCard,
		message: '请输入' + person + '正确的证件号'
	}],
	// 手机号码
	mobile: (person) => [{
		required: true,
		message: '请输入' + person + '的手机号码'
	}, {
		validator: RegexUtils.checkMobile,
		message: '请输入' + person + '正确的手机号码'
	}],
	mail: (person) => [{
		required: true,
		message: '请输入' + person + '的邮箱'
	}, {
		validator: RegexUtils.isEmail,
		message: '请输入' + person + '正确的邮箱'
	}],
	//性别
	sex: [{
		required: true,
		message: '请输入性别'
	}, {
		validator: val => {
			if (val.length > 1 || !['男', '女'].includes(val)) {
				return false
			}
			return true

		},
		message: '性别只能输入男和女',
	}],
	//出生日期
	birthday: [{
		required: true,
		message: '请输入出生日期'
	}, {
		validator: val => {
			let Regex = /^\d{4}-\d{2}-\d{2}$/g
			return Regex.test(val)
		},
		message: '请输入出生日期格式必须为yyyy-MM-dd',
	}],
	//终止日期
	expirationdate: [
		{
			required: true,
			message: '请输入证件止期'
		},
		{
			validator: val => {
				let Regex = /^\d{4}-\d{2}-\d{2}$/g
				return Regex.test(val)
			},
			message: '证件止期格式必须为yyyy-MM-dd',
		},
		{
			validator: val => {
				let a = new Date().getTime()
				let b = new Date(val).getTime()
				if (b < a) {
					return false
				}
				return true
			},
			message: '证件止期必须晚于当前日期',
		}
	],
	//国籍
	nationality: (idtype) => [{
		required: true,
		message: '请选择国籍'
	}, {
		validator: val => {
			if (idtype == '护照' && val == '中国') {
				return false
			}
		},
		message: '选择护照，国籍不能选择中国'
	}],
	//关系
	relationship: [
		{
			required: true,
			message: '请填写被保险人与受益人关系'
		},
		{
			validator: val => {
				let arr = ['法定受益人', '父母', '配偶', '子女']
				return arr.includes(val)
			},
			message: '只能为法定受益人、父母、配偶、子女',
		}
	],
	height: [
		{
			required: true,
			message: '请输入正确的身高'
		},
		{
			validator: val => {
				let regex = /^([1-9]|[1-9]\d|[1-9]\d{0,2})$/;
				return regex.test(val)
			},
			message: '请输入1-999的正整数',
		}
	],
	weight: [
		{
			required: true,
			message: '请输入正确的体重'
		},
		{
			validator: val => {
				let regex = /^([1-9]|[1-9]\d|[1-9]\d{0,2})$/;
				return regex.test(val)
			},
			message: '请输入1-999的正整数',
		}
	],
	area: [{
		required: true,
		message: '请选择地址'
	}],
	terminalDate: [
		{
			required: true,
			message: '请选择证件止期'
		}],
	identityType: [
		{
			required: true,
			message: '请选择个人税收居民身份声明'
		}
	],
	Occupation: [
		{
			required: true,
			message: '请选择职业'
		}
	],
	certificate: function (type) {
		return [{
			required: true,
			message: '请输入证件号码'
		}, {
			validator: RegexUtils.checkNoid(type),
			message: '请输入正确证件号码'
		}]
	},
	belorder: [
		{
			required: true,
			message: '受益顺序',
		},
		{
			validator: val => {
				let arr = ['1', '2', '3'];
				return arr.includes(val)
			},
			message: '受益顺序只能填1 2 3',
		}
	],
	benePer: [
		{
			required: true,
			message: '请输入受益人比例',
		},
		{
			validator: val => {
				let regex = /^([1-9]|[1-9]\d|100)$/;
				return regex.test(val)
			},
			message: '请输入正确的受益人比例',
		}
	],
	address: [{
		required: true,
		message: '请输入详细地址'
	},
	{
		validator: val => {
			console.log(6363636)
			var sum = 0;
			var strRegex = /^[^"',，。!@#$%^&*()！@#￥%……（）]*$/;
			var re = new RegExp(strRegex);
			if (re.test(val)) {
				for (var i = 0; i < val.length; i++) {
					var c = val.charAt(i);
					if (c.match(/[^x00-xff]/ig) != null) {
						sum += 2;
					} else {
						sum += 1;
					}
				}
				var che = (val).replace(/\d/g, "");
				che = (che).replace(/[A-Za-z]/g, "");
				che = (che).replace(/\s/g, "");
				var numGz = /[0-9]/;
				var numtest = numGz.test(val);
				var hanz = /([\u2E80-\u9FFF]){5}/;
				var hanzR = hanz.test(che);
				if (sum < 5 || !numtest || !hanzR) {

					return false;
				}
				return true;
			}
			return false;
		},
		message: '详细地址不能少于5个汉字，且必须有一位数字'
	}, {
		validator: val => {
			console.log(sum)
			var sum = 0;
			var contain1 = ['路', '弄'];
			var contain2 = ['村', '组'];
			var contain3 = ['室', '号'];
			for (var i = 0; i < val.length; i++) {
				if (contain1.includes(val[i])) {
					sum += 1;
				} else if (contain2.includes(val[i])) {
					sum += 1;
				} else if (contain3.includes(val[i])) {
					sum += 1;
				}
			}
			if (sum != 3) {
				return false
			}
		},
		message: '详细地址必须包含有路、弄、村、组、室、号'
	}
	],
	surname: [{
		required: true,
		message: '请输入姓'
	},
	{
		validator: val => {
			let regex = /^[a-zA-Z]+$/;
			return regex.test(val)
		},
		message: '请输入英文或拼音',
	}],
	name: [{
		required: true,
		message: '请输入名'
	},
	{
		validator: val => {
			if (val) {
				let regex = /^[a-zA-Z]+$/;
				return regex.test(val)
			}
			return true
		},
		message: '请输入英文或拼音',
	}],
	liveDetailEng: [{
		required: false,
		message: '请输入名'
	},
	{
		validator: val => {
			if (val) {
				let regex = /^[a-zA-Z0-9,]+$/;
				return regex.test(val)
			}
			return true
		},
		message: '请输入英文或拼音',
	}, {
		validator: val => {
			let some = true
			if (val && val.length < 10) {
				some = false
			}
			return some
		},
		message: '至少10位英文字符',
	}],
	birthDetailEng: [{
		required: false,
		message: '请输入名'
	},
	{
		validator: val => {
			if (val) {
				let regex = /^[a-zA-Z0-9,]{10,}$/;
				return regex.test(val)
			}
			return true
		},
		message: '请输入至少10位英文字符的英文或拼音',
	}, {
		validator: val => {
			let some = true
			if (val && val.length < 10) {
				some = false
			}
			return some
		},
		message: '至少10位英文字符',
	}],
	required: [{
		required: true,
		message: '请输入必填项'
	}],
	idNo1: [{
		required: true,
		message: '请输入纳税识别号码'
	},
	{
		validator: val => {
			if (val) {
				let regex = /^[\u4e00-\u9fa5_a-zA-Z0-9]+[,，]{1}[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
				return regex.test(val)
			}
			return true
		},
		message: '以逗号隔开分开录入',
	}],
	idNo11: [
		{
			validator: val => {
				if (val) {
					let regex = /^[\u4e00-\u9fa5_a-zA-Z0-9]+[,，]{1}[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
					return regex.test(val)
				}
				return true
			},
			message: '以逗号隔开分开录入',
		}],
	checkFeeYearRules: function (period, feeYear, age, min, productId) {
		//一层为保费期间
		min = min ? min : 20;//大麦2021最小18，甜蜜家最小20
		var obj = {
			'10Y': {
				'0C': {
					min,
					max: 60,
				},
				'5Y': {
					min,
					max: 60
				},
				'10Y': {
					min,
					max: 60,
				}
			},
			'20Y': {
				'0C': {
					min,
					max: 50,
				},
				'5Y': {
					min,
					max: 50,
				},
				'10Y': {
					min,
					max: 50,
				},
				'20Y': {
					min,
					max: 50
				}
			},
			'30Y': {
				'0C': {
					min,
					max: 40,
				},
				'5Y': {
					min,
					max: 40,
				},
				'10Y': {
					min,
					max: 40,
				},
				'20Y': {
					min,
					max: 40,
				},
				'30Y': {
					min,
					max: 40,
				}
			},
			'60A': {
				'0C': {
					min,
					max: 50,
				},
				'5Y': {
					min,
					max: 50,
				},
				'10Y': {
					min,
					max: 50,
				},
				'20Y': {
					min,
					max: 40,
				},
				'30Y': {
					min,
					max: 30,
				},
				'60A': {
					min,
					max: 55,
				}
			},
			'65A': {
				'0C': {
					min,
					max: 55,
				},
				'5Y': {
					min,
					max: 55,
				},
				'10Y': {
					min,
					max: 55,
				},
				'20Y': {
					min,
					max: 45,
				},
				'30Y': {
					min,
					max: 35,
				},
				'60A': {
					min,
					max: 55,
				},
				'65A': {
					min,
					max: 55,
				}
			},
			'70A': {
				'0C': {
					min,
					max: 60,
				},
				'5Y': {
					min,
					max: 60,
				},
				'10Y': {
					min,
					max: 60,
				},
				'20Y': {
					min,
					max: 50,
				},
				'30Y': {
					min,
					max: 40,
				},
				'60A': {
					min,
					max: 55,
				},
				'65A': {
					min,
					max: 60,
				},
				'70A': {
					min,
					max: 60,
				}
			},
			'105A': {
				'0C': {
					min,
					max: 75,
				},
				'5Y': {
					min,
					max: 70,
				},
				'10Y': {
					min,
					max: 65,
				},
				'20Y': {
					min,
					max: 55,
				},
				'30Y': {
					min,
					max: 45,
				},
			}
		}
		// 正青春2021投保规则
		if (productId == '056601047') {
			obj = {
				'20Y': {
					'20Y': {
						min,
						max: 40
					}
				},
				'30Y': {
					'20Y': {
						min,
						max: 40
					},
					'30Y': {
						min,
						max: 40
					}
				},
				'60A': {
					'20Y': {
						min,
						max: 40
					},
					'30Y': {
						min,
						max: 30
					}
				},
				'65A': {
					'20Y': {
						min,
						max: 40
					},
					'30Y': {
						min,
						max: 35
					}
				},
				'70A': {
					'20Y': {
						min,
						max: 40
					},
					'30Y': {
						min,
						max: 40
					}
				},
			}
		}
		var rule = obj[period][feeYear];
		if (!rule) {
			return false;
		}
		if (rule.min > age || rule.max < age) {
			return false;
		}
		return true;
	},
	//校验被保人职业限定最大保额是否大于等于所选保额
	checkOccupation: function (amnt, occupationCode, person, filter) {
		let personText = person === 'bbr' ? '被保人' : '投保人';
		var obj = {
			"2099907": 500000,
			"3020102": 300000,
			"3020103": 300000,
			"3020104": 300000,
			"3020105": 300000,
			"3020106": 300000,
			"3020108": 300000,
			"3020110": 500000,
			"4071203": 500000,
			"8000001": 300000,
			"8000002": 300000,
			"8000003": 300000
		}

		if (filter && filter.length > 0) {
			let keys = Object.keys(obj)
			filter.forEach(item => {
				if (keys.includes(item)) {
					delete obj[item]
				}
			})
		}

		var max = obj[occupationCode];

		if (!max) {
			return { success: true, message: '' };
		}

		if (max < amnt) {
			var message = personText + '所选职业为特殊职业，所选计划不得大于' + (Number(max) / 10000) + '万，请返回重新选择投保计划';
			return { success: false, message: message };
		}

		return { success: true, message: '' };

	}
}


export {
	verifyRules
}