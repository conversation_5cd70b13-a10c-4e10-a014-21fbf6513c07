/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-03-11 16:59:13
 * @LastEditors  : a大师兄
 * @LastEditTime : 2023-06-15 20:51:12
 * @Description  : API
 */
import { axios } from './axios'

const httpAction = (params) => axios(params)

// get 方法
const getAction = (url, params) => axios({
	url: url,
	method: 'get',
	params: { ...params, _t: new Date().getTime(), }
});

// post
const postAction = (url, params, header) => axios({
	url: url,
	method: 'post',
	data: params,
	headers: {
		...header
	}
});

// post
const postActionHeaders = (url, params) => axios({
	url: url,
	method: 'post',
	data: params,
	headers: {
		'token': localStorage.getItem("wxToken"),
	}

});
const getActionHeaders = (url, params) => axios({
	url: url,
	method: 'get',
	params: params,
	headers: {
		'token': localStorage.getItem("wxToken"),
	}

});

export {
	httpAction,
	getAction,
	postAction,
	postActionHeaders,
	getActionHeaders
};