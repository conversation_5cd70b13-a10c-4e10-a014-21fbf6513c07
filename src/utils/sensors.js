/*
 * <AUTHOR> a大师兄
 * @Date         : 2022-07-27 16:32:30
 * @LastEditors  : a大师兄
 * @LastEditTime : 2022-08-23 13:44:12
 * @Description  : 神策埋点--
 */
import Vue from 'vue'
import config from '@/env.config.js'
import sensors from 'sa-sdk-javascript'

// 事件对应关系
const sensors_event = {
	click: '$WebClick',
}

function nSensors() {

	if (!config.isSensors) {
		return {
			record: function(eventName, data, callback) {
				console.log('行为数据开关-关闭');
				callback && callback()
			},
			loginById: function(id, callback) {
				callback && callback();
			},
			getDeviceId: function(callback) {
				callback && callback();
			}
		}
	}
	sensors.init({
		server_url: config.mdApiurl,
		// 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
		is_track_single_page: true,
		use_client_time: true,
		send_type: 'ajax',
		show_log: true,
		heatmap: {
			track_attr: ['homeClick', 'hotrep'],
			//是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
			clickmap: 'default',
			//是否开启触达图，not_collect 表示关闭，不会自动采集 $WebStay 事件，可以设置 'default' 表示开启。
			scroll_notice_map: 'default',
			// 是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
			// 默认只有点击 a input button textarea 四种元素时，才会触发 $WebClick 元素点击事件
		}
	});

	// 注册公共属性
	sensors.registerPage({
		platform: 'h5',
		mobile: localStorage.getItem("mobile") || '',
		openId: localStorage.getItem("openId") || '',
		channelCode: localStorage.getItem("mdChannelCode") || ''
	});
	// 设置之后，SDK 就会自动收集页面浏览事件，以及设置初始来源。
	sensors.quick('autoTrack')
	sensors.quick("autoTrackSinglePage")
	//页面加载时长
	sensors.use('PageLoad');
	//如果想加额外的属性，可以如下方式（添加 platform 属性为 h5）
	//sensors.quick('autoTrack', {
	//	platform: 'h5'
	//})

	//sensors
	//this.$sensors.record(this.$sensors_event.click, { element_name: `home-banner-${index}` });
	sensors.record = (eventName, data, callback) => {
		sensors.track(eventName, {
			...data
		}, () => {
			callback && callback();
		});
	}
	
	sensors.loginById = (id, callback)=> {
		sensors.login(id);
		callback && callback();
	}
	
	sensors.getDeviceId = (callback)=>{
		let d = sensors.quick('getAnonymousID');
		console.log(d)
		callback && callback(d);
	}
	
	console.log('行为数据开关-开启')
	return sensors;
}


Vue.prototype.$sensors = nSensors();
Vue.prototype.$sensors_event = sensors_event;
