/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-11-12 14:20:05
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-09-14 18:42:25
 * @Description  : 微信相关
 */
//import wxWebView from "weixin-webview-jssdk";
import Vue from 'vue'
import config from '@/env.config.js'
// import router from '../router/router.index'
// 调用分享案例
//this.$wxJsSdk.wxShareInit({
//    title: '分享标题（通常是动态的）',
//    desc: '分享描述（通常是动态的）'
//});

const apiUrl = {
    getSignature: '/basic/share/getSignature',
    getOpenId: '/basic/api/customerwx/getOnlyOpenId',
    getUserInfoByCode: '/proposal/szd/getUserInfoByCode',
    getUserInfoByOpenId: '/proposal/szd/getUserInfoByOpenId'
}

//存储各个链接的签名信息
const signMap = new Map();

const geturlparam = () => {
    var url = window.location.href;
    // \w+ 表示匹配至少一个(数字、字母及下划线), [\u4e00-\u9fa5]+ 表示匹配至少一个中文字符
    let pattern = /(\w+|[\u4e00-\u9fa5]+)=(\w+|[\u4e00-\u9fa5]+)/ig;
    let result = {};
    url.replace(pattern, ($, $1, $2) => {
        result[$1] = $2;
    })
    return result
};

//存储临时的分享信息
let wxShareConfig = {
    title: '分享标题（通常是动态的）',
    desc:'',
    imgUrl: '',
    link: '',
    isShare: false,
    success: function () { console.log("分享回调") },
    jsApiList: [
        'showMenuItems',
        'hideAllNonBaseMenuItem',
        'updateAppMessageShareData',
        'updateTimelineShareData',
        'onMenuShareTimeline',
        'onMenuShareAppMessage'
    ]
};




const wxJsSdk = {
    weAppNavigateTo(url) {
        window.wx.miniProgram.navigateTo({
            url: url,
        })
    },
    weAppReLaunch(url) {
        window.wx.miniProgram.reLaunch({
            url: url,
        })
    },
    
    wxPostMessage(param) {
        console.log('给小程序发送消息')
        window.wx.miniProgram.postMessage({ data: param });
    },
    //各个页面加载后，调用此方法，传入的参数config是对象

    wxShareInit(config = {}) {
        wxShareConfig = { ...wxShareConfig, ...config };

        if (!wxShareConfig.isShare) {
            let onBridgeReady = function() {
                window.WeixinJSBridge.call('hideOptionMenu');
            }
            if (typeof window.WeixinJSBridge === "undefined") {
                if (document.addEventListener) {
                    document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
                } else if (document.attachEvent) {
                    document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
                    document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
                }
            } else {
                onBridgeReady();//520ym.net
            }
            return
        }
        //微信中二次分享的处理，截取到有效的分享链接
        var authUrl = window.location.href;
        //判断是否已经签名了
        //console.log('signMap', signMap.get('authUrl'));
        if (signMap.has('authUrl')) this._wxConfigJSSDK(signMap.get('authUrl'), wxShareConfig); else this._wxShareAuth(authUrl);

    },

    //从服务器获取某分享链接的签名信息，并存储起来
    _wxShareAuth(authUrl) {
        //此处我使用的是自己封装的网络请求方法
        const promise = Vue.prototype.$http.postAction(apiUrl.getSignature, { url: authUrl });
        promise.then(res => {
            //此处请根据各自的服务器返回数据文档进行操作
            //分享链接授权签名信息
            const sign = res;
            signMap.set('authUrl', sign);
            this._wxConfigJSSDK(sign);
        });
        promise.catch((err) => {
            console.log(err.response);
        })
    },

    //将签名信息更新到微信的SDK中
    _wxConfigJSSDK(shareSign) {
        window.wx.config({
            beta: true,
            debug: false,
            appId: shareSign.appId,
            timestamp: shareSign.timestamp,
            nonceStr: shareSign.noncestr,
            signature: shareSign.signature,
            jsApiList: wxShareConfig.jsApiList,
            // openTagList: ["wx-open-launch-weapp"]
        });
        // config信息验证后会执行ready方法;
        window.wx.ready(function () {
            // 隐藏所有非基础按钮接口
            window.wx.hideAllNonBaseMenuItem();
            // 只显示朋友圈跟发送好友
            if (wxShareConfig.isShare) {
                window.wx.showMenuItems({
                    menuList: [
                        "menuItem:share:timeline",
                        "menuItem:share:appMessage"
                    ]
                });

                window.wx.updateAppMessageShareData({
                    ...wxShareConfig,
                    success: function () {
                        Vue.prototype.$sensors.record(this.$sensors_event.click, { element_name: `share` });
                        console.log("分享成功");
                    },
                    fail: function () {
                        console.log("分享失败");
                    },
                    cancel: function () {
                        console.log("取消分享");
                    }
                })
                window.wx.updateTimelineShareData({
                    ...wxShareConfig,
                })
                // 调用老版本接口支持分享回调 ----分享给朋友
                window.wx.onMenuShareTimeline({
                    ...wxShareConfig,
                    success: function () {
                        Vue.prototype.$sensors.record(this.$sensors_event.click, { element_name: `share` });
                        console.log("分享成功-----");
                    },
                    cancel: function () {
                        console.log("取消分享---");
                    }
                });
                // 调用老版本接口支持分享回调 ----分享朋友圈
                window.wx.onMenuShareAppMessage({
                    ...wxShareConfig,
                    success: function () {
                        Vue.prototype.$sensors.record(this.$sensors_event.click, { element_name: `share` });
                        wxShareConfig.success();
                        console.log("分享成功----");
                    },
                    cancel: function () {
                        wxShareConfig.success();
                        console.log("取消分享-----");
                    }
                });
            }
            console.log(shareSign)

        });
        // config信息验证失败会执行error函数，
        window.wx.error(function (res) {
            console.log("分享失败: error", res);
        });

    },
    codeGetOpenId() {
        return new Promise((resolve, reject) => {
            let openId = localStorage.getItem('openId') || '';
            if (openId && openId !== 'null' && openId !== 'undefined' && openId !== null) {
                reject({ openId, message: 'localStorage已存在openId' })
                return;
            }
            let obj = geturlparam();
            if (!obj.code) {
                let redirect = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${config.appId}&redirect_uri=${encodeURIComponent(window.location.href)}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`;
                window.location.replace(redirect);
                console.log('%c\n' + redirect, 'color:green')
                reject({ message: '微信授权code为空' })
                return;
            }
            console.log('微信授权code为', obj.code)
            //此处我使用的是自己封装的网络请求方法
            const promise = Vue.prototype.$http.getAction(apiUrl.getOpenId, { code: obj.code });
            promise.then(res => {
                localStorage.setItem('openId', res.openId);
                resolve({ ...res, message:'openId保存成功>>从localStorage获取'})
            });
            promise.catch((err) => {
                console.log(err.response);
                reject(err)
            })
        });
    },
    isNull(str){
        if (str == "") return true;
        let regu = "^[ ]+$";
        let re = new RegExp(regu);
        return re.test(str);
    },
    getAppntInfo(obj) {
        let getDefaultValues = {
            occupation: "仅为中国税收居民",
            idExpDateType: "",
            nationality: "CHN",
            taxType: "1",
            isGuardianFlag: "1",
            marriage: "",
            phone: "",
            homeAddressCHN: "",
            homeAddressEN: "",
            placeOfBirthCHN: "",
            placeOfBirthEN: "",
            rgtAddress: "",
            zoneType: "",
            address: "",
            province: "",
            city: "",
            email: "",
            county: "",
            attaches: [],
            name: "",
            isSocialIns: "",
            idType: "",
            idTypeText: "",
            idNo: "",
            sSFlag: "",
            weight: "",
            stature: "",
            mobile:'',
        }
        let appnt = Object.assign({}, getDefaultValues, { name: obj.name, idType: obj.idType + '', idNo: obj.idCard, mobile: obj.mobile })
        localStorage.setItem('szdh5_appnt', JSON.stringify(appnt))
        localStorage.setItem("mobile", obj.mobile)
    },
    getSzdOpenid() {
        const { getAction } = Vue.prototype.$http;
        const succseeObj = { message: 'openId保存成功>> 从localStorage获取' },
            openId = localStorage.getItem('openId') || '',
            scope = 'snsapi_userinfo',
            appName = '东吴青松卫中老年防癌疾病保险A款（互联网）',
            appIcon = 'https://ecproductprd.soochowlife.net:4456/upload/intergration/406A20/v1/list.jpg';
        
        const getByCode = (success,error)=> {
            return new Promise(() => {
                window.SZDApi.user.oauth(
                    //'c22616fcd7054788',
                    config.szdAppid,
                    scope,
                    appName,
                    appIcon,
                    (result) => {
                        console.log('苏周到获取code--oauth--result', result);
                        if (!result.data) {
                            error({ message: 'code获取失败' })
                            return
                        }
                        const promise = getAction(
                            apiUrl.getUserInfoByCode,
                            { code: result.data.code }
                        );
                        promise.then(res => {
                            localStorage.setItem('openId', res.openid);
                            this.getAppntInfo(res)
                            success(succseeObj)
                        }).catch(err => {
                            error(err)
                        })
                    }
                )
                
            })
        }
        return new Promise((resolve, reject) => {
            console.log('苏周到授权=---openid', openId, this.isNull(openId));
            if (!this.isNull(openId)) {
                window.SZDApi.user.getUserInfo((result) => {
                    console.log('苏周到获取用户信息---getUserInfo-result', result);
                    let id = result.data.id || '',
                        isId = this.isNull(id);
                    if (isId || id !== openId) {
                        //当前openid与缓存中的openid不一致，重新获取openid
                        getByCode(resolve, reject)
                    } else {
                        //根据openid获取用户信息
                        const getopenid = getAction(
                            apiUrl.getUserInfoByOpenId,
                            { openId: openId }
                        );
                        getopenid.then((res) => {
                            console.log('根据openid获取用户信息---getUserInfoByOpenId-res', res);
                            this.getAppntInfo(res)
                            resolve(succseeObj)
                            return;
                        }).catch((err) => {
                            //如果code为1111，则重新获取code
                            if (err.code == '1111') {
                                getByCode(resolve, reject)
                            } else {
                                reject(err)
                            }
                        })
                    }
                })
            } else {
                getByCode(resolve, reject)
            }
        })
    },
    
    sdzShareInit(szdConfig) {
        let { title, content, url, image } = szdConfig
        window.SZDApi.share.all(
            title,
            content,
            url,
            image,
            (result) => {
                console.log('SZDApi.share.all callback response', result)
            }
        )
        //SZDApi.share.wechat(
        //    title,
        //    content,
        //    url,
        //    image,
        //    (result) => {
        //        console.log('SZDApi.share.all callback response', result)
        //    }
        //)  
    },
    getH5UUId() {
        return new Promise((resolve) => {
            let obj = geturlparam();
            let openId = localStorage.getItem('openId') || '';

            if (obj.openId) {
                localStorage.setItem('openId', obj.openId);
                resolve({ openId: obj.openId, message: 'localStorage已存在openId' })
                return obj.openId
            }

            if (openId && openId !== 'null' && openId !== 'undefined' && openId !== null) {
                resolve({ openId, message: 'localStorage已存在openId' })
                return;
            }

            var originStr = 'xxxxxx-xxxxxx-4xxxxx-xxxxxx',
                originChar = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
                len = originChar.length;
            let uuid = originStr.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
            uuid += originChar.charAt(Math.floor(Math.random() * len));
            openId = uuid.replace(/-/g, '')+new Date().getTime();

            localStorage.setItem('openId', openId);
            resolve({ openId, message: 'localStorage已存在openId' })
        })

    },
}
//导出工具类
export default wxJsSdk;

