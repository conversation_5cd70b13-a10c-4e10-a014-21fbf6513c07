/* --------------------------------  全局过滤器 --------------------------------------- */

import Vue from 'vue'

/**
 * 匹配电话号码
 * 15012341234 => 150****1234
 */
Vue.filter('filter_mobile', function (value) {
    if (!value) return '';
    let str = value;
    str = str.toString().replace(/^(\d{3})(\d{4})(\d{4})/g, '$1****$3')
    return str;
})

Vue.filter('filter_isSocialIns', function (value) {
    if (!value) return '--';
    let str = '--';
    switch (value) {
        case '1':
            str = '有社保'
            break;
        case '0':
            str = '无社保'
            break;
        default:
            break;
    }
    return str;
})

Vue.filter('filter_relation', function (value) {
    if (!value) return '--';
    let str = '--';
    switch (value) {
        case '00':
            str = '本人'
            break;
        case '33':
            str = '配偶'
            break;
        case '32':
            str = '子女'
            break;
        case '31':
            str = '父母'
            break;
        default:
            break;
    }
    return str;
})

Vue.filter('filter_premium', function (value) {
    if (!value) return '--';
    let str = value;
    str = Number(str) <= 0 ? '--' : value
    return str;
})

Vue.filter('filter_money', function (value) {
    if (!value) return '---';
    let str = value;
    str = Number(str) <= 0 ? '--' : (Number(value) / 100).toFixed(2);
    return str;
})
Vue.filter('filter_wan_yuan', function (value) {
    if (!value) return '---';
    let str = value;
    str = Number(str) <= 0 ? '--' : (Number(value) / 1000000);
    return str;
})

/**
 * 匹配身份证号码
 * 110101199003075592 => 1101**********5592
 */
Vue.filter('filter_idNo', function (value) {
    if (!value) return '';
    let str = value;
    str = str.toString().replace(/^(.{4})(?:\d+)(.{4})$/g, '$1****$3')
    return str;
})
