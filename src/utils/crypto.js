/**
 * rsa 加解密
 * 
 *
 */
import { JSEncrypt } from './jsencrypt.js'

const pubKey =
	"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCWgCm2Pn/PyAB+z97vtp3jLOHshtfyn9oDIjt+2l+NEzWCds/Zfad+iI9cyvONpW9sPrczo0fyN6zD+Stx58fPisQnJcr40ArvhnsPRivRv8hWX8Q30XQ9/zkQq9QWrMwXAZIEm6k/WOjVtSurCTI6I+Vn/nwkz7q4xPH1mDaHGwIDAQAB"
const priKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJaAKbY+f8/IAH7P3u+2neMs4eyG1/Kf2gMiO37aX40TNYJ2z9l9p36Ij1zK842lb2w+tzOjR/I3rMP5K3Hnx8+KxCclyvjQCu+Gew9GK9G/yFZfxDfRdD3/ORCr1BaszBcBkgSbqT9Y6NW1K6sJMjoj5Wf+fCTPurjE8fWYNocbAgMBAAECgYAJ12Ci46xc7HGn2TT1TOY/LDirzFeDrJNHgC4Oyf3UjkyB9v35dV+GBS0Gvti1Svuhwuoc7NDwFFbuS7VdEfB9VqRlU/dHReIFXBJ2snNiiTmeVGQa2yotNu3ElZukGF5RWoTaiDl6XBdf2ybZweyg5iGUJtodCjxcpYvCsTESLQJBAMRUnbiJBn/DalJNanmwzhjjbQKPIXtg/74awDvo0Cbh2ZD3rRUAlvFXYvDCJGOTEQh8cxF+AzAultk91njdA2cCQQDEPcc7dgW3390CuihIk+zTemYA2+LbduP8istpbJesoRwG8mPjArSOiDjhlMAoF7gSCGBGq8LrmPua+brjROItAkBKXBoYqJ1zipocbgHj8Vk+B/VjwL5Xspdqc0tX5YfhEi2LQKOnjr+czRJGSeHqbOtIoYobl/SxLRQpV8iBooQFAkEAoW9U64RutdHSRYEQzIvsIhipuwEogXQvnzwqk+KCb7/lh411QHWkRTCo4mH1JcZFZYhAc0KvShMCwH26EWZETQJAUwaqCBu+28KD2aznmwmojulPUfbY+YtLfPfF4aACVM1K3l8IV7MibAsTrDy13sHEk46CDWbt0m05KQseB7+Vxw=="
const rsaEncrypt = (text) => {
	let encryptor = new JSEncrypt() // 创建加密对象实例
	encryptor.setPublicKey(pubKey) //设置公钥
	let rsaPassWord = encryptor.encryptLong(text) // 对内容进行加密
	return rsaPassWord;
}

const rsaDecrypt = (encrypted) => {
	let decrypt = new JSEncrypt() //创建解密对象实例

	decrypt.setPrivateKey(priKey) //设置秘钥
	let uncrypted = decrypt.decryptLong(encrypted) //解密之前拿公钥加密的内容
	return uncrypted;
}

export {
	rsaEncrypt,
	rsaDecrypt
}