<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-20 09:07:26
 * @LastEditors  : a大师兄
 * @LastEditTime : 2022-09-26 15:48:26
 * @Description  :自定义计划卡片
-->
<template>
	<div>
		<van-tabs sticky scrollspy class="custom-vant-tab">
			<van-tab title-class="custom-tab-title" title="产品介绍">
				<!-- 产品介绍 -->
				<div class="divider"></div>
				<div>
					<van-image
						width="100%"
						alt="图片"
						:src="require('@/assets/modular/product/<EMAIL>')"></van-image>
				</div>
				<div class="spacing"></div>
			</van-tab>
			<van-tab title-class="custom-tab-title" title="产品介绍">
				<!-- 理赔说明 -->
				<claim-settlement-service />

			</van-tab>
		</van-tabs>
	</div>
</template>

<script>
export default {
	name: "customTabs",
	components: {},
	props: {
		tabList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			active: 0,
			radio: "1",
			message: "",
		};
	},
	mounted() {},
	methods: {},
};
</script>

<style lang='less'>
.app-content {
	.custom-plan-tabs {
		.van-tabs__nav--line {
			background-color: #fff;
		}
		.van-tabs__line {
			width: 1.5rem;
			height: 3px;
			border: 0px solid @theme-background-color1;
			border-radius: 5px;
			border-top: none;
			background-color: @theme-background-color1;
		}
		padding: 12px 12px;
		border-radius: 10px;
		overflow: hidden;
		background-color: #fff;
		.van-tabs--line .van-tabs__wrap {
			height: auto;
		}
		.plan-items {
			margin: @padding-md 12px;
			margin-top: 40px;
		}
		.plan-textarea {
			background-color: @pages-background-color;
			.van-cell {
				background-color: @pages-background-color;
			}
		}
		.van-tabs__line {
			bottom: 0;
		}
		.plan-tabs-img {
			width: 80%;
			max-width: 32px;
			margin: 0 auto;
		}
		.van-tab__text--ellipsis {
			display: block;
		}
	}
}
</style>
