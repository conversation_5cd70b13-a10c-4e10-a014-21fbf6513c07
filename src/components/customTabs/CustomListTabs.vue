<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-20 09:07:26
 * @LastEditors  : yang<PERSON><PERSON><PERSON>
 * @LastEditTime : 2021-11-23 14:24:56
 * @Description  :自定义tabs
-->
<template>
	<div v-if="tabList.length > 0">
		<van-tabs v-model="active" :sticky="sticky" :scrollspy="scrollspy" class="custom-vant-tab" :class="[customTabBg?'customTabBg':'']" @click="tabClick">
			<van-tab title-class="custom-tab-title" :title="item.value" :key="index" v-for="(item,index) in tabList">
			</van-tab>
		</van-tabs>
	</div>
</template>

<script>
export default {
	name: "customTabs",
	components: {},
	props: {
		tabList: {
			type: Array,
			default: () => [],
		},
		customTabBg: {
			type: Boolean,
			default: false,
		},
		sticky: {
			type: Boolean,
			default: false,
		},
		scrollspy: {
			type: <PERSON><PERSON>an,
			default: false,
		},
	},
	data() {
		return {
			showwTabList: [],
			active: 0,
		};
	},
	mounted() {},
	methods: {
		//arrayFilter(array){
		//	if(array[0]){
		//		array.filter(items=>{
		//			return items.key=='00'
		//		})
		//	}
		//},
		tabClick(active) {
			//console.log(active);
			var activeKey = "0" + active;
			//this.showwTabList=this.tabList.filter(item=>{
			//return	item.key==activeKey
			//})
			console.log(activeKey);
			this.$emit("tabClick", activeKey);
		},
	},
};
</script>

<style scoped lang='less'>
.van-tab{
	font-family: 'SourceHanSansCN', 'SourceHanSansCN-Regular';
	font-weight: 400;
	text-align: center;
	color: #666666;
}
.custom-vant-tab {
	.van-tab--active {
		font-size: 20px;
	}
	/deep/ .custom-tab-title + .van-tabs__line {
		font-family: "iconfont" !important;
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		width: auto;
		height: auto;
		//border: 2px solid @theme-background-color1;
		//border-radius: 0 0 50% 50%/0 0 100% 100% ;
		border-top: none;
		background-color: transparent;
		color: @theme-background-color1;
		bottom: 12px;
	}
	/deep/ .custom-tab-title + .van-tabs__line:before {
		content: "\e616";
	}
}
.customTabBg {
	.van-tabs__nav--line {
		background-color: @body-background-color;
	}
}
</style>
