/*
* @Author: <PERSON><PERSON><PERSON><PERSON>
* @Date: 2023-07-17 13:18:08
* @Last Modified by: wang<PERSON><PERSON>
* @Last Modified time: 2023-10-07 17:59:07
*/
<template>
	<div>
		<div class="pdfHtml">
			<!--<p class="title" style="text-align:center;">
				<strong>反保险欺诈提示 <van-icon name="warning" color="#BD3124" size="20" /></strong>
			</p>-->
			<p class="title">&nbsp;</p>
			<p class="text-indent">诚信是保险合同的基本原则,涉嫌保险欺诈将承担以下责任:</p>
			<p class="text-indent">
				<span >【刑事责任】</span>进行保险诈骗犯罪活动,可能会受到拘役、有期徒刑,并处罚金或者没收财产的刑事处罚。保险事故的鉴定人证明人故意提供虚假的证明文件,为他人诈骗提供条件的,以保险诈骗罪的共犯论处。</p>
			<p class="text-indent">
				<span>【行政责任】</span>进行保险诈骗活动,尚不构成犯罪的,可能会受到15日以下拘留、5000 元以下罚款的行政处罚;保险事故的鉴定人、证明人故意提供虚假的证明文件,
				为他人诈骗提供条件的,也会受到相应的行政处罚。</p>
			<p class="text-indent">
				<span>【民事责任】</span>故意或因重大过失未履行如实告知义务, 保险公司不承担赔偿或给付保险金的责任。若参与或实施保险诈骗行为，理赔相关信息将被纳入保险行业共享范围。</p>
			
		</div>
		<div style="box-sizing: border-box;">
			<slot></slot>
		</div>
		
	</div>
	
</template>

<script>
	export default {
		name: 'ShbPdf1',

		data() {
			return {

			};
		},

		mounted() {

		},

		methods: {

		},
	};
</script>

<style lang="less" scoped>
	@import "./index.less";

	.red {
		color: #FF0000;
	}
</style>