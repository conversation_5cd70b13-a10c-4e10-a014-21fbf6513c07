/*
* @Author: <PERSON><PERSON><PERSON><PERSON>
* @Date: 2023-07-17 13:18:08
* @Last Modified by: wang<PERSON><PERSON>
* @Last Modified time: 2023-10-07 17:59:07
*/
<template>
	<div>
		<div class="pdfHtml">
			<p class="title" style="text-align:center;">
				<strong>声明及授权</strong>
			</p>
			<p class="title">&nbsp;</p>
			<p class="text-indent">1.本人提交给贵公司的所有资料与证明文件均属实，且申请书上所填写内容真实无误。</p>
			<p class="text-indent">2.本人同意承担因账号提供错误导致转账不成功或因委托他人代领赔付金没有收到的责任，贵公司不承担责任。</p>
			<p class="text-indent">
				3.因本次保险服务需要，本人同意并授权贵公司可通过医疗机构、行政和司法机构、其他组织及个人查询、采集、存储、登记、核验与投保人、被保险人及受益人有关的必要信息或资料；本人授权贵公司及其合作机构，依据行业监管及为实现服务之需要，可对本人相关信息进行合理的使用与传递，但应严格履行保密义务，并采取必要措施保证信息安全。（此授权书的复印件亦具有同等效力）
			</p>
		</div>
		<slot></slot>
	</div>
</template>

<script>
	export default {
		name: 'ShbPdf2',

		data() {
			return {

			};
		},

		mounted() {

		},

		methods: {

		},
	};
</script>

<style lang="less" scoped>
	@import "./index.less";
</style>