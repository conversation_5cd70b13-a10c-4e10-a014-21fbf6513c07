/*
* @Author: wa<PERSON><PERSON><PERSON>
* @Date: 2023-07-17 13:18:08
* @Last Modified by: wang<PERSON><PERSON>
* @Last Modified time: 2023-10-07 17:59:07
*/
<template>
	<div>
		<div class="pdfHtml">
			<p class="title" style="text-align:center;">
				<strong>授权书</strong>
			</p>
			<p class="title">&nbsp;</p>
			<p class="text-indent">
				1.为提供【苏州惠民保保险相关服务】的需要，本人同意并授权东吴人寿保险股份有限公司可将本人及出险人的【姓名、证件类型、证件号】提供给合法知悉本人及出险人信息的机构用于查询与本人及出险人医疗健康、医疗保障有关的信息。查询信息的时间范围为2025年1月1日至{{endDate}}。</p>
			<p class="text-indent">
				2.本人同意东吴人寿保险股份有限公司收集本人及出险人的【姓名、证件类型、证件号、投保、承保、理赔、医疗票据、银行账户以及医疗健康、医疗保障相关】信息进行加工分析、合理处理后用于为本人提供保险理赔相关服务。
			</p>
			<p class="text-indent">3.本人知悉并同意通过本服务查询到的医疗保障相关信息用于保险公司在理赔业务环节中参考使用，不单独作为理赔结论的认定依据，以医疗机构实际相关信息为准。</p>
			<p class="text-indent">4.本人提交给东吴人寿保险股份有限公司的所有资料与证明文件均属实，且申请书上所填写内容真实无误。</p>
		</div>
		<slot></slot>
	</div>
</template>

<script>
	import {formatDate} from "@/utils/date.js"
	
	export default {
		name: 'ShbPdf3',

		data() {
			return {
				endDate: '2025年12月31日'
			};
		},

		mounted() {
			const currentDate = new Date();
			const targetDate = new Date('2025-12-31 23:59:59');
			
			if (currentDate < targetDate) {
			  this.endDate = formatDate(currentDate, 'yyyy年MM月dd日');
			} 
		},

		methods: {

		},
	};
</script>

<style lang="less" scoped>
	@import "./index.less";
</style>