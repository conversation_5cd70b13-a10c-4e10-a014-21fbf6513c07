.pdfHtml{
	div,
	table,
	p {
		font-weight: initial;
		line-height: 38px;
		padding: 0 15px;
		// background-color: #ffffff;
		color:#2F3133
	}
	.bold{
		font-weight: bold;
	}
	.text-indent {
		text-indent: 2em;
	}

	.table-haed-bg {
		background-color: #4270C7;
		color: #ffffff;
	}
	.star_table {
		border: 1px solid #000;
		border-collapse: collapse;
		margin: 0 auto;
	}
    .sign-style {
        width: 1.25rem;
		height: 1.25rem;
		line-height: 1.25rem;
		color: white;
		background: #000000;
		text-align: center;
		border-radius: 0.625rem;
		margin-left: 5px;
		padding: 0;
    }
	.center{
        text-align: center;
    }
	.right{
		text-align: right;
	}
	.table-one {
        width: 15%;
    }
	.star-title {
        font-size: 18px;
        font-weight: 500;
    }
	.weight {
		font-weight: 500;
	}
	.one-col {
		width: 10%;
	}
	.line {
		border-bottom: 1px solid #000;
	}
	.title {
		font-size: 18px;
	}

	.margin-top{
		margin: 10px 0;
	}
	.label {
		margin-top: 20px;
		font-size: 16px;
	}

	.table {
		border: 1px solid #000;
		margin: 0 auto;
		border-collapse: collapse;
		font-size:12px;
		width: 100%;
	}

	.table tr td {
		border: 1px solid #000;

	}

	.widthT {
		width: 20%;
	}
	.widthTr {
		width: 13%;
		text-align: center;
	}
	.widthXuhao {
		width: 8%;
		text-align: center;
	}
	.head{
		text-align: center;
	}
	.trBackgroundC {
		background-color: #D8D8D8;
	}

	.pdfBackgroundC {
		//background-color: #D8D8D8;
	}
	.red {
		//color: #FF0000;
	}
	.bgc-yellow {
		//background-color:#FFFF00;
	}

}

.pdfHtml-content{
	.table{
		margin: 10px;
	}
}


.pdf-file {
	padding: 15px 20px;
	text-align: center;
	div,
	p,
	table {
		font-weight: initial;
		line-height: 24px;
		padding: 0;
		background-color: #fff;
	}
	p {
		padding: 0 !important;
	}
	.title {
		font-size: 18px;
	}
	.text-indent {
		text-indent: 2em;
	}
	.text-title {
		font-weight: bold;
	}
	.text-state {
		font-size: 16px;
		font-weight: bold;
	}
	.table {
		border: 1px solid #000;
		margin: 0 auto;
		border-collapse: collapse;
		font-size: 12px;
		width: 100%;
	}
	.table-one {
		width: 20%;
	}
	.table-two {
		width: 40%;
	}
	.table tr td {
		border: 1px solid #000;
		text-align: left;
	}
	.table-last {
		font-weight: bold;
	}
	.box {
		width: 100%;
		height: 80px;
		background-color: #fff;
	}
	.blue {
		color: #82b0e0;
	}

	.dir-style {
		width: 100%;
		height: auto;
		border: 3px solid #000000;
		padding: 0.125rem;
	}
	.sign-style {
		width: 1.25rem;
		height: 1.25rem;
		line-height: 1.25rem;
		color: white;
		background: #000000;
		text-align: center;
		border-radius: 10px;
		margin-right: 1.25rem;
	}
	.center {
		text-align: center;
	}
	.rigth{
		text-align: right;
	}
	.star-title {
		font-size: 0.87rem;
		font-weight: bold;
	}
	.content {
		font-size: 0.75rem;
	}
}