<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2023-01-03 14:51:09
 * @LastEditors  : <PERSON><PERSON><PERSON>
 * @LastEditTime : 2024-07-31 13:14:27
 * @Description  : 输入框
-->
<template>
	<div>
		<van-popup v-model="showPop" position="top" get-container="body" @closed="cancel">
			<div style="padding: 10px;">
				<van-field sy autofocus v-model="message" :border="false" rows="5" type="textarea" maxlength="1000"
					placeholder="请输入你想询问的问题…" show-word-limit clearable />
				<div class="bottom-div">
					<van-button class="cancel-btn" type="default" size="small" @click="cancel">取消</van-button>
					<van-button class="confirm-btn" type="info" color="linear-gradient(to right, #6087FF, #436FF6);"
						size="small" @click="confirm">发送</van-button>
				</div>
			</div>
		</van-popup>
	</div>
</template>

<script>
	export default {
		name: "InputView",
		components: {},
		props: {},
		data() {
			return {
				showPop: false,
				message: '',
				popHeight: 180,
			};
		},
		mounted() {

		},
		methods: {
			show() {
				this.showPop = true;
			},
			confirm() {
				if(!this.message) {
					this.$toast("请输入你想询问的问题");
					return;
				}
				this.$emit("confirm", {
					data: this.message
				})

				this.cancel();
			},
			cancel() {
				this.showPop = false;
				this.message = '';
			},
		},
	};
</script>

<style scoped lang='less'>
	/deep/ .van-field__word-limit {
		text-align: left;
	}

	.bottom-div {
		display: flex;
		justify-content: right;
		padding: 0 10px;
		margin-top: -30px;

		.cancel-btn {
			width: 80px;
			margin-right: 10px;
		}

		.confirm-btn {
			width: 80px;
		}
	}
</style>