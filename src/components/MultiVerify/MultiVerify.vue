<template>
	<div :class="['', { show_: show }]">
		<!-- 验证码容器 -->
		<components v-if="componentType" :is="componentType" :type="verifyType" :figure="figure" :arith="arith"
			:width="width" :height="height" :fontSize="fontSize" :codeLength="codeLength" :mode="mode"
			:vOffset="vOffset" :vSpace="vSpace" :explain="explain" :imgUrl="imgUrl" :imgName="imgName"
			:showButton="showButton" :imgSize="imgSize" :blockSize="blockSize" :barSize="barSize"
			:defaultNum="defaultNum" :checkNum="checkNum" ref="instance"></components>
		<!-- 确定按钮容器 -->
		<div @click="checkCode" v-show="showButton" style="width:0; height:0;">
			<slot name="check">
				<button class="verify-btn">确定</button>
			</slot>
		</div>
	</div>
</template>
<script type="text/babel">
	/**
     * Verify 验证码组件
     * @description 分发验证码使用
     * */
    import VerifyCode from './VerifyCode'
    import VerifySlide from './VerifySlide'
    import VerifyPoints from './VerifyPoints'

    export default {
        name: 'MultiVerify',
        props: {
			// 是否出现，由父级控制
			show: {
			    type: Boolean,
			    default: true
			},
            type: {
                type: String | Number,
                require: false,
                default: 'picture'
            },
            figure: {
                type: Number
            },
            arith: {
                type: Number
            },
            width: {
                type: String
            },
            height: {
                type: String
            },
            fontSize: {
                type: String
            },
            codeLength: {
                type: Number
            },
            mode: {
                type: String
            },
            vOffset: {
                type: Number
            },
            vSpace: {
                type: Number
            },
            explain: {
                type: String
            },
            imgUrl: {
                type: String
            },
            imgName: {
                type: Array
            },
            imgSize: {
                type: Object
            },
            blockSize: {
                type: Object
            },
            barSize: {
                type: Object
            },
            //默认的文字数量
            defaultNum: {
                type: Number
            },
            //校对的文字数量
            checkNum: {
                type: Number
            },
            showButton: {
                type: Boolean,
                default: true
            }
        },
        data() {
            return {
                // 内部类型
                verifyType: undefined,
                // 所用组件类型
                componentType: undefined
            }
        },
        methods: {
            /**
             * checkCode
             * @description 判断验证码
             * */
            checkCode() {
                if (this.instance.checkCode) {
                    this.instance.checkCode();
                }
            },
            /**
             * refresh
             * @description 刷新
             * */
            refresh() {
                if (this.instance.refresh) {
                    this.instance.refresh();
                }
            }
        },
        computed: {
            instance() {
                return this.$refs.instance || {};
            }
        },
        watch: {
			show(newV) {
			    // 每次出现都应该重新初始化
			    if (newV) {
			        this.refresh();
			    }
			},
            type: {
                immediate: true,
                handler(type) {
                    switch (type.toString()) {
                        case 'picture':
                            this.verifyType = '1';
                            this.componentType = 'VerifyCode';
                            break
                        case '1':
                            this.verifyType = '1';
                            this.componentType = 'VerifyCode';
                            break
                        case 'compute':
                            this.verifyType = '2';
                            this.componentType = 'VerifyCode';
                            break
                        case '2':
                            this.verifyType = '2';
                            this.componentType = 'VerifyCode';
                            break
                        case 'slide':
                            this.verifyType = '1';
                            this.componentType = 'VerifySlide';
                            break
                        case '3':
                            this.verifyType = '1';
                            this.componentType = 'VerifySlide';
                            break
                        case 'puzzle':
                            this.verifyType = '2';
                            this.componentType = 'VerifySlide';
                            break
                        case '4':
                            this.verifyType = '2';
                            this.componentType = 'VerifySlide';
                            break
                        case 'pick':
                            this.verifyType = '';
                            this.componentType = 'VerifyPoints';
                            break
                        case '5':
                            this.verifyType = '';
                            this.componentType = 'VerifyPoints';
                            break
                        default:
                            this.verifyType = undefined;
                            this.componentType = undefined;
                            console.error('Unsupported Type:' + type);
                    }
                }
            }
        },
        components: {
            VerifyCode,
            VerifySlide,
            VerifyPoints
        },
    }
</script>
<style scoped >
	@import url("index.css");
	
	.verify-container {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		/* background-color: rgba(0, 0, 0, 0.3); */
		z-index: 999;
		opacity: 0;
		pointer-events: none;
		transition: opacity 200ms;
	}
	.show_ {
		opacity: 1;
		pointer-events: auto;
	}
</style>