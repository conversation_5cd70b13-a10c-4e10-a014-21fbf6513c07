<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-28 11:02:19
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2022-12-19 09:52:18
 * @Description  : 附件
-->
<template>
	<div style="height: 90px;display: flex;justify-content: space-around;align-items: center;">
		<div class="text-c">
			<van-uploader accept="image/*" :after-read="e => afterRead(e, this.$dict.questionType.pic)" max-count="1">
				<template slot="default">
					<div class="img">
						<van-icon name="photo-o" size="24" />
					</div>
				</template>
				<template slot="preview-cover"></template>
			</van-uploader>
			<div class="title">照片</div>
		</div>
		<div class="text-c">
			<van-uploader capture="camera" :after-read="e => afterRead(e, this.$dict.questionType.pic)" max-count="1">
				<template slot="default">
					<div class="img">
						<van-icon name="video-o" size="24" />
					</div>
				</template>
			</van-uploader>
			<div class="title">拍照</div>
		</div>
		<div class="text-c">
			<van-uploader :after-read="e => afterRead(e, this.$dict.questionType.file)"
				accept=".pdf, .doc, .docx, .xls, .xlsx, .txt">
				<template slot="default">
					<div class="img">
						<van-icon name="link-o" size="24" />
					</div>
				</template>
			</van-uploader>
			<div class="title">附件</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "AttachmentView",
		components: {},
		props: {
			defaultDate: {
				type: [Array],
				default: () => [],
			},
		},
		data() {
			return {
				fileList: []
			};
		},
		watch: {

		},
		mounted() {},
		methods: {
			afterRead(e, type) {
				let files = (Array.isArray(e) ? e : [e]).map(item=> {
					return {
						...item,
						src: item.content
					}
				});
				
				this.$emit("confirm", {
					data: files,
					type: type
				})
			}
		},
	};
</script>

<style lang='less' scoped>
	.text-c {
		text-align: center;
	}

	.img {
		width: 80px;
		height: 55px;
		background-color: #F6F8FA;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 10px;
	}

	.img:active {
		background-color: #f0f0f0;
		color: #333;
	}


	.title {
		height: 25px;
		line-height: 25px;
		font-size: 14px;
		color: #9c9c9c;
		font-weight: 400;
	}
</style>