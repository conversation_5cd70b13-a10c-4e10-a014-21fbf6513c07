<template>
  <div>
    <van-dialog
      v-model="show"
      :title="modalTitle"
      show-cancel-button
    >
      <div
        class="adiv"
        :style="styleVar"
        v-if="pdfLoading"
      >
        <van-loading
          size="24px"
          vertical
          v-if="pdfLoading"
        >预览内容正在加载中，请稍等...</van-loading>
      </div>

      <div style="color: #606266">
        <iframe
          style="border-top: none"
          class="PDF-iframe"
          :v-show="showPDFModal"
          :id="iframeID"
          :width="iframeWidth"
          :height="iframeHeight"
        >
        </iframe>
      </div>
    </van-dialog>
  </div>
</template>

<script>

  export default {
    name: "PDFModal",
    props: {
      modalTitle: {
        type: String,
        default: 'PDF查看'
      },
      showPDFModal: {
        type: Boolean,
        default: true
      },
      iframeID: {
        type: String,
        default: 'iframeID'
      },
      iframeWidth: {
        type: String,
        default: '700'
      },
      iframeHeight: {
        type: String,
        default: '95%'
      },
      PDFurl: {
        type: String,
        default: ''
      },


    },
    computed: {
      styleVar() {
        return {
          '--width': this.iframeWidth+"px",
        }
      }
    },
    data() {
      return{
        pdfLoading: true,
        show:true,
      }
    },

    mounted() {
      this.loadPDF();
    },
    methods: {

      loadPDF() {
        console.log("res:")
        let res = 'http://localhost:3000/ChatGPT.pdf'
        let iframeID = document.querySelector("#" + this.iframeID);
        this.pdfLoading = false;
        console.log("res:", res)
        // 重点
        //let blobPDF = new Blob([res], {
        //  type: `application/pdf;charset-UTF-8` // word文档为msword,pdf文档为pdf
        //});
        // blob类型
        //iframeID.src = "/libs/pdfjs/web/viewer.html?file=" + window.URL.createObjectURL(res);
        // url类型
        iframeID.src = "/libs/pdfjs/web/viewer.html?file=" + res;
        
        //getActionPDF(this.PDFurl).then((res) => {
         
        //}).finally(()=>{
        //  this.pdfLoading = false
        //})

      }
    }

  }
</script>

<style scoped>
 .PDF-iframe{
  width: 100%;
  min-height: 500px;
 }
  .adiv{
    text-align: center;
    position:absolute;
    z-index:10;
    width:var(--width);
    height: 100%;
  }
  .aspin{
    height: 100%;
    line-height:50%;
  }
</style>