<template>
	<div>
		<div id="other">
			<div
				id="comment_dialog"
				style=" display:none;position: fixed;overflow: hidden;"
			>
				<div id="leftView">
					<p
						id="comment_title"
						style="padding: 15px 10px 5px 10px;"
					></p>
					<div
						id="signImage"
						class="signImagecss"
					></div>
				</div>
				<div
					id="tmpcanvascss"
					class="tmpcanvascss"
				>
					<div id="signTitle"></div><canvas id="comment_canvas"></canvas>
				</div>
				<div
					id="comment_btnContainerInner"
					class="comment_btncontainer"
					style="clear: both;margin-top: 20px;display: -webkit-flex;display: flex;justify-content:center;"
				><input
						id="comment_ok"
						type="button"
						class="button orange"
						value="确 定"
					><input
						id="comment_back"
						type="button"
						class="button orange"
						value="后退"
					><input
						id="comment_cancel"
						type="button"
						class="button orange"
						value="取 消"
					></div>
			</div>
			<div
				id="single_dialog"
				style=" display:none;"
			>
				<div id="leftView" style="width: 300px;height: 140px;">
					<p
						id="anysign_title"
						style="color: #333333;"
					></p>
					<div style="position: relative;" class="write-box">
						<div
							id="single_signImage"
							class="signImagecss"
							style="overflow: hidden;width: 295px;height: 140px;"
						></div>
					</div>
				</div>
				<div
					id="single_tmpcanvascss"
					class="single_tmpcanvascss"
					style="float: right;clear: both; width: 260px; height: 260px;"
				>
					<div id="single_signTitle" style="width: 260px;height: 260px;font-size: 155px;line-height: 260px;float: right; clear: none;"></div>
					<canvas id="single_anysignCanvas" style="background-size: 260px 260px"></canvas>
				</div>
				<div
					id="btnContainerInner"
					class="btncontainer"
					style="clear: both;display: -webkit-flex;display: flex;justify-content: space-between; padding: 12px 15px;flex-wrap: wrap;width:65%;float:right; margin-top: 20px;"
				>
					<div
						id="btnOK"
						class="button_new"
					>确定</div>
					<div
						id="btnCancel"
						class="button_new"
						style="float:right;"
					>取消</div>
					<div
						id="btnClear"
						class="button_new"
						style=""
					>清空</div>
					<div
						id="btnBack"
						class="button_new"
						style="float:right;"
					>后退</div>
				</div>
			</div>
			<div
				id="dialog"
				style=" display:none;"
			>
				<div
					id="anysign_title"
					style="color:#333333;"
					width="100%"
					height="10%"
				>请投保人<span style="font-size:20pt;">李明</span>签名</div>
				<div
					id="container"
					onmousedown="return false;"
				><canvas
						id="anysignCanvas"
						width="2"
					></canvas></div>
				<div
					id="single_scrollbar"
					style="text-align: center;  vertical-align:middle; "
					width="100%"
				><span id="single_scroll_text">*滑动操作：</span><input
						id="single_scrollbar_up"
						type="button"
						class="button orange"
						value="左移"
					/><input
						id="single_scrollbar_down"
						type="button"
						class="button orange"
						value="右移"
					/></div>
				<div
					id="btnContainerOuter"
					width="100%"
				>
					<div
						id="btnContainerInner"
						style="text-align: center;   font-size:5pt;"
						width="100%"
					><input
							id="btnOK"
							type="button"
							class="button orange"
							value="确 定"
							onclick="sign_confirm();"
						/>'<input
							id="btnClear"
							type="button"
							class="button orange"
							value="清 屏"
							onclick="javascript:clear_canvas();"
						><input
							id="btnCancel"
							type="button"
							class="button orange"
							value="取 消"
							onclick="cancelSign();"
						></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'Sign',
		props: {},
		data() {
			return {
			};
		},
		components: {

		},
		computed: {

		},
		mounted() {
		
		},
		methods: {
			signInit(name, callback) {
				console.log('签名初始化....')
				let orderNo = Math.floor(Date.now() / 1000).toString();
				const signConfig = {
					accompanyId: "BQSQPC_1",
					keyword: '申请人签名：',
					personName: name,
					titleName: '',
					orderNo: orderNo,
					// OCR 识别api 域名 
					OCR_API_BASE: 'www',
					num: 21,
					signSuccess: (signRes) => {
						signRes.orderNo = orderNo;
						callback && callback(signRes)
					}
				}
				window.testPopupDialog(signConfig)
			},
		}
	};
</script>
<style scoped lang='less'>
	#leftView{
		width: 300px;
    	height: 160px
	}
	.write-box{
		width: 295px;
		height: 140px;
		background: url('../assets/modular/home/<USER>');
		background-size: cover;
	}
	.single_tmpcanvascss{
		border: 0;
	}
	.button_new {
	width: 155px;
	height: 44px;
	border-radius: 12px;
	border: 1px solid #D2DBF6;
	text-align: center;
	line-height: 42px;
	display: inline;
	float: left;
	font-size: 28px;
	text-decoration: none;
	color: #2F3133;
	margin-bottom: 5px;
	margin-top: 15px;
}

#btnOK {
	background-color: #2C6BF9;
	color: #fff;
	border: 1px solid #2C6BF9;

}
</style>