<template>
	<div class="content">
		<div class="radio-btn-group" :class="{'no-click': disable<PERSON>heck}">
			<div class="radio-title">{{title}}</div>
			<div class="radio" v-for="(item, index) in list" :key="index" :disabled="disableCheck"
				:style="{'margin-left': textMargin + 'px', 'margin-right': textMargin + 'px', 'height': height+'px', 'line-height': height+'px'}">
				<input :type="multiple ? 'checkbox' : 'radio'" :name="name" :value="item" :checked="item.checked" 
					@change="change" :id="item.title + item.value + name" v-model="selectedValues" />
				<label :for="item.title + item.value + name"
					:style="{'padding': `${textPaddingTop + 'px'} ${textPadding + 'px'}` }">
					<template v-if="judgeSelect(item)">
						<van-icon class="radio-icon" color="#567FFC"  name="checked" />
						<span style="color: #567FFC;">{{item.title}}</span> 
					</template>
					<template v-else>
						<van-icon class="radio-icon" color="#979797"  name="circle" />
						<span style="color: #979797;">{{item.title}}</span> 
					</template>
				</label>
			</div>
		</div>
		<div v-if="showBtn || !disable" style="text-align: right;margin-top: 5px;">
			<van-button type="info" color="linear-gradient(to right, #6087FF, #436FF6);" style="width: 70px;" round size="small" @click="confirm">确认</van-button>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'RadioBoxView',
		props: {
			title: {
				type: String,
				default: "",
			},
			multiple: {
				type: Boolean,
				default: false
			},
			list: {
				type: Array,
				default: () => [],
			},
			textPadding: {
				type: Number,
				default: 15
			},
			textMargin: {
				type: Number,
				default: 10
			},
			textPaddingTop: {
				type: Number,
				default: 6
			},
			height: {
				type: Number,
				default: 32
			},
			disable: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				name: '',
				checked: '',
				selectedValues: [],
				showBtn: false,
				disableCheck: false,
			}
		},
		created() {
			this.name = this.guid();
		},
		watch: {
			disable: {
				handler(val) {
					if (val !=null) {
						this.disableCheck = val;
					}
				},
				immediate: true,
				deep: false, // 可以深度检测到 person 对象的属性值的变化
			},
		},
		computed: {
		},
		methods: {
			judgeSelect(e) {
				return this.selectedValues.filter(item => item.value == e.value).length > 0;
			},
			change() {
				let items = Array.isArray(this.selectedValues) ? this.selectedValues : [this.selectedValues];
				this.selectedValues = items;
				this.showBtn = this.selectedValues.length > 0;
				this.$emit("onChange", {
					data: items
				});
			},
			confirm() {
				if(this.selectedValues.length == 0) {
					this.$toast('请选择');
					return
				}
				this.showBtn = false;
				this.disableCheck = true;
				this.$emit("onConfirm", {
					data: this.selectedValues
				});
			},
			guid() {
				return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
					var r = Math.random() * 16 | 0,
						v = c == 'x' ? r : (r & 0x3 | 0x8);
					return v.toString(16);
				});
			},
			reset() {
				this.selectedValues = [];
			}
		}
	}
</script>

<style scoped lang='less'>
	.content {
		padding: 10px;
	}

	.radio-btn-group {
		display: flex;
		flex-wrap: wrap;
		justify-content: start;
		align-items: center;
	}

	.radio-title {
		color: #231916;
		font-weight: 500;
	}

	.radio-btn-group .radio {
		margin: 2px 10px;
		height: 44px;
		line-height: 44px;
	}

	.radio-btn-group .radio label {
		background: #F9F9F9;
		/* border: 1px solid #E6E6E6; */
		padding: 10px 15px;
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #DFE3E7;
		cursor: pointer;
		color: #6B6B6B;
		font-size: 14px;
		-webkit-transition: box-shadow 400ms ease;
		transition: box-shadow 400ms ease;
	}

	.radio-btn-group .radio label:hover {
		box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
	}

	.radio-btn-group .radio input[type="radio"] {
		display: none;
	}

	.radio-btn-group .radio input[type="checkbox"] {
		display: none;
	}

	.radio-btn-group .radio input[type="radio"]:checked+label {
		background: #FFFFFF;
		border: 1px solid #4F79FA;
	}

	.radio-btn-group .radio input[type="checkbox"]:checked+label {
		background: #FFFFFF;
		border: 1px solid #4F79FA;
	}
	
	.radio-icon {
		margin-left: -5px;
		margin-right: 10px;
	}

	.show {
		font-weight: 400;
		color: #444;
	}

	.show span {
		background: #f5f5f5;
		color: #F44336;
		border-radius: 3px;
		padding: .25rem .5rem;
		font-size: 1.25rem;
		border: 1px solid #f1f1f1;
	}
	
	.radio-plain {
		background: #FFFFFF;
		border-radius: 22px !important;
		border: 1px solid #4F79FA;
	}
	
	.no-click {
	  pointer-events: none;
	}

</style>