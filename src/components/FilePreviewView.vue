<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2023-01-03 14:51:09
 * @LastEditors  : <PERSON><PERSON><PERSON>
 * @LastEditTime : 2024-07-31 13:14:27
 * @Description  : 输入框
-->
<template>
	<div>
		<van-popup v-model="showPop" style="height:90%;" closeable close-icon="close" position="bottom" get-container="#app"
			@closed="cancel">
			<div style="padding: 10px;">
				<vue-office-docx v-if="type == 'docx'" :src="src" style="height: 100vh" @rendered="renderedHandler"
					@error="errorHandler" />
				<vue-office-excel v-else-if="type == 'xlsx'" :src="src" style="height: 100vh"
					@rendered="renderedHandler" @error="errorHandler" />
				<vue-office-pdf v-else-if="type == 'pdf'" :src="src" style="height: 100vh" @rendered="renderedHandler"
					@error="errorHandler" />
				<p v-else-if="type == 'txt'">{{src}}</p>
				<van-image v-else :src="src" style="height: 100vh"></van-image>
			</div>
		</van-popup>
	</div>
</template>

<script>
	//引入VueOfficeDocx组件
	import VueOfficeDocx from '@vue-office/docx'
	//引入相关样式
	import '@vue-office/docx/lib/index.css'
	//引入VueOfficeExcel组件
	import VueOfficeExcel from '@vue-office/excel'
	//引入相关样式
	import '@vue-office/excel/lib/index.css'
	//引入VueOfficePdf组件
	import VueOfficePdf from '@vue-office/pdf'

	export default {
		name: "FilePreviewView",
		components: {
			VueOfficeDocx,
			VueOfficeExcel,
			VueOfficePdf
		},
		props: {},
		data() {
			return {
				showPop: false,
				src: null,
				type: '',
			};
		},
		mounted() {

		},
		methods: {
			show(file) {
				this.showPop = true;
				this.type = file.ext;
				this.src = file.src || file.url;
				
				if (this.type == 'txt' && (file.file instanceof File)) {
					// txt类型
					this.readText(file);
				}
			},
			readText(file) {
				const reader = new FileReader()
				reader.onload = ()=> {
					console.log('reader.result', reader.result)
					this.src = reader.result;
				}
				reader.readAsText(file.file);
			},
			cancel() {
				this.showPop = false;
				this.type = '';
				this.src = null;
			},
			renderedHandler() {
				console.log("渲染完成")
			},
			errorHandler() {
				console.log("渲染失败")
			}
		},
	};
</script>

<style scoped lang='less'>
	
</style>