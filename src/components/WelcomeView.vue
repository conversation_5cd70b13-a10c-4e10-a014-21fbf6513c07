<template>
	<div>
		<div class="content" v-if="firstIn">
			<div class="title">Hi ~ 我是东吴小智，欢迎使用智能理赔服务！</div>
			<div class="tip flex-center"><van-image :src="require('@/assets/icon/ico_pin.svg')" width="14px" height="20px" /> 温馨提示：</div>
			<div>1、该功能只支持拥有苏州医保的出险人本人申请理赔医疗责任。</div> 
			<div>2、若您不符合上述条件，请前往“东吴人寿微服务”微信公众号或者“东吴人寿”App，依次点击【理赔服务】>【苏惠保】，上传理赔影像材料。</div>
			<div class="flex-center">如有疑问，随时联系我哦！<van-image :src="require('@/assets/icon/ico_smile.svg')" width="20px" height="20px" /></div>
		</div>
		<div v-else>
			<div class="title">Hi ~ 欢迎回到智能理赔界面。</div>
			<div class="title flex-center">我是东吴小智，有什么可以帮您！<van-image :src="require('@/assets/icon/ico_smile.svg')" width="20px" height="20px" /></div>
		</div>
	</div>
</template>

<script>
	export default {
		name: '',
		props: {
			firstIn: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
			};
		},
		components: {

		},
		computed: {

		},
		mounted() {
		
		},
		methods: {
			
		}
	};
</script>
<style scoped lang='less'>
	.content {
		.title {
			font-size: 16px;
			font-weight: bold;
			
		}
		
		.tip {
			font-size: 15px;
			font-weight: 600;
			
		}
	}
	
	.flex-center {
		display: flex;
		justify-content: start;
		align-items: center;
	}
	
</style>