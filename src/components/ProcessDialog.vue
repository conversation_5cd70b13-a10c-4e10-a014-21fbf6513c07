<template>
  <div class="process-dialog">
    <div class="process-content">
      <div class="title">苏惠保理赔进度：</div>
      <div class="message">
        <img class="loading-img" src="@/assets/images/409.gif" />
        <div class="message-content">{{ currentMessage }}</div>
      </div>
      <div class="btn-content" v-if="showDetail" @click="showDetailClick">
        <div class="btn">点击查看医保明细数据</div>
      </div>
    </div>
    <Popup
      v-if="detail"
      :show.sync="show"
      :insuName="insuName"
      :selectItem="selectItem"
      :detail="detail"
    ></Popup>
  </div>
</template>

<script>
import Popup from "./Popup.vue";
export default {
  components: { Popup },
  props: {
    total: {
      type: Number,
      default: 0,
    },
    claimNo: {
      type: String,
      default: "",
    },
    insuName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      infoList: [],
      currentIndex: 0,
      show: false,
      timer: null,
      showDetail: false,
      selectItem: {},
      detail: null,
      url: {
        claimAfterDetailPreInquiry: "intelligent/claim/claimAfterDetailPreInquiry",
      },
    };
  },

  computed: {
    currentMessage() {
      if (this.infoList.length > 0) {
        return this.infoList[this.currentIndex];
      } else {
        return "";
      }
    },
  },
  created() {
    this.selectItem.claimNo = this.claimNo;
    let list = [
      "正在提交报案并获取报案信息...",
      "正在提交用户保单信息...",
      "正在进行线上案件预审...",
      "正在拉取您的医保明细数据...",
      "正在进行理赔金额核算...",
      "正在进行自动理赔判断...",
    ];
    if (this.total < 3000) {
      this.infoList = [...list, "已完成自动理赔并成功结案"];
    } else {
      this.infoList = [...list, "正在进行线上审核...", "已完成线上审批流程"];
    }

    // console.log(this.infoList);
    this.startMessageRotation();
  },
  methods: {
    startMessageRotation() {
      const param = { claimNo: this.claimNo, name: this.insuName };

      this.timer = setInterval(() => {
        if (this.currentIndex < this.infoList.length - 1) {
          this.currentIndex++;
          if (this.currentIndex === this.infoList.length - 1) {
            this.$http
              .getAction(this.url.claimAfterDetailPreInquiry, param)
              .then((res) => {
                console.log(res);
                if (res.code == 200) {
                  if (res.data.data.length > 0) {
                    this.showDetail = true;
                    this.detail = res.data;
                  }
                }
              });

            // this.showDetail = true;
          }
        } else {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 3000); // 每3秒执行一次
    },
    showDetailClick() {
      this.show = true;
    },
  },
};
</script>

<style scoped lang="less">
.process-dialog {
  width: 1097px;
  .process-content {
    // width: 95%;
    margin: 0 auto;
    margin-top: 16px;
    background: #fff;
    color: #2f3133;
    opacity: 0.95;
    border-radius: 0 20px 20px 20px;
    padding: 22px 33px;
    padding-bottom: 41px;
    font-size: 28px !important;
    font-style: normal;
    position: relative;

    .message {
      margin-top: 20px;
      display: flex;
      align-items: center;
      .loading-img {
        width: 118px;
        height: 118px;
        margin-left: 178px;
        margin-right: 26px;
      }
      .message-content {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 42px;
        color: #4671f7;
      }
    }
    .btn-content {
      margin-top: 41px;
      // margin-bottom: 19px;
      display: flex;
      justify-content: center;
      .btn {
        width: 417px;
        height: 88px;
        background: linear-gradient(156deg, #7bbcf4 0%, #305bef 100%);
        border-radius: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 28px;
      }
    }
  }
}
</style>
