<template>
	<div class="content">
		<div class="radio-btn-group">
			<div class="radio-title">{{title}}</div>
			<div class="radio" v-for="(item, index) in list" :key="index"
				:style="{'margin-left': textMargin + 'px', 'margin-right': textMargin + 'px', 'height': height+'px', 'line-height': height+'px'}">
				<input :type="multiple ? 'checkbox' : 'radio'" :name="name" :value="item" :checked="item.checked"
					@change="change" :id="item.title + item.value + name" v-model="selectedValues" />
				<label :for="item.title + item.value + name"
					:style="{'padding': `${textPaddingTop + 'px'} ${textPadding + 'px'}` }">{{item.title}}</label>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'RadioButtonView',
		props: {
			title: {
				type: String,
				default: "",
			},
			multiple: {
				type: Boolean,
				default: false
			},
			list: {
				type: Array,
				default: () => [],
			},
			textPadding: {
				type: Number,
				default: 10
			},
			textMargin: {
				type: Number,
				default: 5
			},
			textPaddingTop: {
				type: Number,
				default: 5
			},
			height: {
				type: Number,
				default: 34
			},
			defaultSelectFirst: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				name: '',
				checked: '',
				selectedValues: []
			}
		},
		watch: {
			list: {
				handler(val) {
					if (val != null && val.length > 0 && this.defaultSelectFirst == true) {
						this.$nextTick(() => {
							let item = val[0];
							let sid = item.title + item.value + this.name;
							document.getElementById(sid).checked = true;
							this.selectedValues = [item];
							this.change();
						})
					}
				},
				immediate: true,
				deep: false
			},
		},
		created() {
			this.name = this.guid();
		},
		methods: {
			change() {
				let items = Array.isArray(this.selectedValues) ? this.selectedValues : [this.selectedValues];

				this.$emit("onChange", {
					data: items
				});
			},
			guid() {
				return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
					var r = Math.random() * 16 | 0,
						v = c == 'x' ? r : (r & 0x3 | 0x8);
					return v.toString(16);
				});
			},
			reset() {
				this.selectedValues = [];
			}
		}
	}
</script>

<style scoped lang='less'>
	.content {
		width: 100%;
		height: 40px;
	}

	.radio-btn-group {
		display: flex;
		flex-wrap: nowrap;
		justify-content: start;
		align-items: center;
		overflow-x: auto;
		height: 100%;
	}

	.radio-title {
		color: #231916;
		font-weight: 500;
	}

	.radio-btn-group .radio {
		flex-shrink: 0;
		margin: 0 10px;
		height: 40px;
		line-height: 40px;
	}

	.radio-btn-group .radio label {
		background: #F9F9F9;
		/* border: 1px solid #E6E6E6; */
		padding: 5px 10px;
		border-radius: 5px;

		cursor: pointer;
		color: #333333;
		font-size: 13px;
		-webkit-transition: box-shadow 400ms ease;
		transition: box-shadow 400ms ease;
	}

	.radio-btn-group .radio label:hover {
		box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
	}

	.radio-btn-group .radio input[type="radio"] {
		display: none;
	}

	.radio-btn-group .radio input[type="checkbox"] {
		display: none;
	}

	.radio-btn-group .radio input[type="radio"]:checked+label {
		background: #4D78F9;
		color: #fff;
		border-color: #FFFFFF;
	}

	.radio-btn-group .radio input[type="checkbox"]:checked+label {
		background: #4D78F9;
		color: #fff;
		border-color: #FFFFFF;
	}


	.show {
		font-weight: 400;
		color: #444;
	}

	.show span {
		background: #f5f5f5;
		color: #F44336;
		border-radius: 3px;
		padding: .25rem .5rem;
		font-size: 1.25rem;
		border: 1px solid #f1f1f1;
	}
</style>