<template>
	<div>
		<div class="title">尊敬的{{detail.name}}先生/女士：</div>
		<div class="title">您好！</div>
		<div style="margin-top: 8px;">{{replyText.first}}</div>
		<div style="margin-top: 8px;">
			{{questionType=='selfPay'?detail.selfPayProcess:detail.selfFundedProcess}}
		</div>
		<div style="margin-top: 8px;">{{replyText.second}}</div>
		<table class="table">
			<thead>
				<td style="font-weight: bold;">时间</td>
				<td style="font-weight: bold;">医疗机构</td>
				<td style="font-weight: bold;">费用类别</td>
				<td style="font-weight: bold;">金额（元）</td>
			</thead>
			<tbody v-for="item in tableData" :key="item.id">
				<tr
					v-if="item['feeType']&&item['feeType'].length>0"
					:style="{borderBottom:item['feeType']&&item['feeType'].length==1 ?'1px solid #dfe2e5':'',}"
				>
					<td :rowspan="item.rowspan">{{item.sETLTIME}}</td>
					<td :rowspan="item.rowspan">{{item.fIXMEDINSNAME}}</td>
					<td>{{feeText[item['feeType'][0]]}}</td>
					<td>{{item[item['feeType'][0]]}}</td>
				</tr>
				<template v-if="item['feeType'].length>1">
					<tr
						v-for="(fee,idx) in item['feeType']"
						:key="fee"
						v-if="idx!=0"
						:style="{borderBottom:idx==item['feeType'].length-1 ?'1px solid #dfe2e5':'',}"
					>
							<td>{{feeText[item['feeType'][idx]]}}</td>
							<td>{{item[item['feeType'][idx]]}}</td>
					</tr>
				</template>
			</tbody>
		</table>
		<div style="margin-top: 8px;">{{questionType=='selfPay'?detail.selfPay :detail.selfFunded}}</div>	
	</div>
</template>

<script>
export default {
	name: 'Name',
	components: { },
	mixins: [],
	props: {
		message:{
			type:Object,
			default:()=>{}
		},
	},
	data() {
		return {
			detail:{},
			selfPay:{
				first:'根据您的保险理赔申请，我们对个人自付费用进行了详细计算，构成如下:',
				second:'（注:总自付费用包含在苏惠保指定医疗机构内产生的门诊或住院费用。）',
				feeType1:'个人自付额',
				feeType2:'账户自付额',
				feeType3:'共济自付额',
			},
			selfFunded:{
				first:'根据您的保险理赔申请，我们对个人自费费用进行了详细计算，构成如下:',
				second:'（注:总自费费用包含在苏惠保指定医疗机构内产生的门诊或住院费用。）',
				feeType1:'个人自费额',
				feeType2:'个人合规自费额',
				feeType3:'账户自费额',
				feeType4:'账户合规自费额',
				feeType5:'共济自费额',
				feeType6:'共济合规自费额',
				feeType7:'责任免除额',
			},
			tableText:{
				'个人自付额':'sELFPAYCASHAMT',
				'账户自付额':'aCCTZFUPAY',
				'共济自付额':'aCCTMULAIDZFPAY',
				'个人自费额':'oWNPAYCASHAMT',
				'账户自费额':'aCCTZFEPAY',
				'共济自费额':'aCCTMULAIDZFEPAY',
				'个人合规自费额':'oWNPAYINSCPAMT',
				'账户合规自费额':'aCCTHGPAY',
				'共济合规自费额':'aCCTMULAIDHGPAY',
				'责任免除额':'eXCLUSIONMONEY',
				
			},
			feeText:{
				'sELFPAYCASHAMT':'个人自付额',
				'aCCTZFUPAY':'账户自付额',
				'aCCTMULAIDZFPAY':'共济自付额',
				'oWNPAYCASHAMT':'个人自费额',
				'aCCTZFEPAY':'账户自费额',
				'aCCTMULAIDZFEPAY':'共济自费额',
				'oWNPAYINSCPAMT':'个人合规自费额',
				'aCCTHGPAY':'账户合规自费额',
				'aCCTMULAIDHGPAY':'共济合规自费额',
				'eXCLUSIONMONEY':'责任免除额',
				
			},

		}
	},
	watch:{

	},
	computed:{
		questionType(){
			let type = this.message.question=="如何得出个人自付金额?"?'selfPay':'selfFunded'
			return type
		},
		replyText(){
			let text = this.message.question=="如何得出个人自付金额?"?this.selfPay:this.selfFunded
			return text
		},
		tableData(){
			let selfpay = ['sELFPAYCASHAMT','aCCTZFUPAY','aCCTMULAIDZFPAY']
			let selfFunded = ['oWNPAYCASHAMT','aCCTZFEPAY','aCCTMULAIDZFEPAY','oWNPAYINSCPAMT','aCCTHGPAY','aCCTMULAIDHGPAY','eXCLUSIONMONEY']
			let self = this.message.question=="如何得出个人自付金额?" ? selfpay :selfFunded;
			let data=this.message.workFlow.data.data
			data.map(item=>{
				let value = self.filter(arr=>{
					return item[arr]!=''&& Number(item[arr])!=0
				})
				console.log('去掉0----',value)
				item['feeType'] = value  
				item['rowspan'] = value.length  
			})
			console.log('看看最终的结果 数据',data[0])
			return data
		}

	},
	// 挂载完成（可以访问DOM元素）
	mounted() {
		console.log('推荐问题',this.message)
		this.detail = this.message.workFlow.data
	},
	// 方法
	methods: {

	}
}
</script>

<style lang='less' scoped>
.title{
	font-size: 16px;
	font-weight: 500;
	color: #000000;
}
.table{
	text-align: center;
	border-spacing: 0;
    border-collapse: collapse;
	margin-bottom:16px ;
	font-size: 11px;
	td{
		padding: 0.375rem 0.8125rem;
		width: 25%;
	}
}
</style>
