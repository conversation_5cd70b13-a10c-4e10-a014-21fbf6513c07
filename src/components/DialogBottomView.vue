<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-28 10:21:53
 * @LastEditors  : a大师兄
 * @LastEditTime : 2022-09-13 17:42:57
 * @Description  : 对话框下方组件
-->

<template>
	<div style="margin: 2px 0px;">
		<div class="flex">
			<div>
				<!-- <van-button style="margin-right: 10px;" type="default" icon="records-o" size="small" @click="createClick">创建文档</van-button> -->
				<van-button :icon="require('@/assets/icon/ico_copy.png')" style="width: 28px;" size="small" @click="copyClick"></van-button>
			</div>
			<div class="flex" style="width:50px;margin-left: 40px;">
				<van-image :src="require(goodFlag == 1 ? '@/assets/icon/ico_hao_on.png' : '@/assets/icon/ico_hao.png')"
					width="14" height="14" @click="goodClick" />
				<van-image :src="require(goodFlag == 2 ? '@/assets/icon/ico_cha_on.png' : '@/assets/icon/ico_cha.png')"
					width="14" height="14" @click="badClick" />
			</div>
		</div>
		<feedback-view ref="feedbackRef"  @confirm="feebackConfirm" @cancel="feebackCancel"></feedback-view>
	</div>
</template>

<script>
	import FeedbackView from "@/components/FeedbackView.vue"
	export default {
		name: "DialogBottomView",
		components: {
			FeedbackView
		},
		props: {
			message: {
				type: Object,
				default: () => {},
			}
		},
		data() {
			return {
				goodFlag: 0, // 0 不点，1：点好，2：点坏
				url: {
					rating: 'paybackFormal/talk/rating', // 评价
				},
			};
		},
		mounted() {},
		methods: {
			createClick() {
				this.$emit("onCreate", {})
			},
			copyClick() {
				navigator.clipboard.writeText(this.message.answer).then(() => {
					this.$toast("复制成功");
					this.$emit("onCopy", {})
				}).catch(err => {
					this.$toast("复制失败："+ err);
				});
			},
			goodClick() {
				this.$emit("onGood", {})
				if (this.goodFlag != 1) {
					this.$refs.feedbackRef.show(1, {...this.message});
				} else {
					let p = {
						isGood: 0,
						messageId: this.message.messageId,
						sessionId: this.message.sessionId,
					}
					this.submitRate(p);
				}
			},
			badClick() {
				this.$emit("onBad", {})
				if (this.goodFlag != 2) {
					this.$refs.feedbackRef.show(2, {...this.message});
				} else {
					let p = {
						isGood: 0,
						messageId: this.message.messageId,
						sessionId: this.message.sessionId,
					}
					this.submitRate(p);
				}
			},
			submitRate(param) {
				this.$http.postAction(this.url.rating, param).then(res => {
					if (res.code == '200') {
						this.goodFlag = param.isGood;
						if(this.goodFlag != 0) {
							this.$toast('反馈成功')
						}
						
					} else {
						this.$toast(res.msg);
					}
				}).catch((res) => {
					if(res) {
						this.$toast(res.msg)
					} else {
						this.$toast('网络错误，请重试。')
					}
					
				});
			},
			feebackConfirm(e) {
				let askTypeList = e.list.map(item => {
					return item.value;
				});
				let errorDescList = e.errorList.map(item => {
					return item.value;
				});
				
				let p = {
					isGood: e.type,
					type: askTypeList,
					errorDescList: errorDescList,
					desc: e.desc,
					messageId: this.message.messageId,
					sessionId: this.message.sessionId,
				}
				this.submitRate(p);
			},
			feebackCancel(e) {
				// this.goodFlag = 0;
			}
		},
	};
</script>

<style scoped lang='less'>
	.flex {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
</style>