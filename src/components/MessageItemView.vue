<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-28 10:21:53
 * @LastEditors  : a大师兄
 * @LastEditTime : 2025-06-06 07:16:57
 * @Description  : 消息
-->

<template>
  <div class="">
    <div v-for="(session, dialogIndex) in dialogList" :key="session.sessionId">
      <div v-if="isLastestDialog(dialogIndex) && dialogIndex > 0">
        <van-divider>以下是新对话</van-divider>
      </div>
      <template v-if="isLastestDialog(dialogIndex) && title == 'shbServe'">
        <div class="recommend-q margin-t">
          <div class="title">推荐问题：</div>
          <div style="display: flex">
            <div
              v-for="(item, index) in recommendQuestion"
              :key="index"
              @click="recommendClick(item)"
            >
              <span
                class="dialog-bg recommend-gray margin-t"
                style="margin-right: 20px"
                >{{ item.question }}</span
              >
            </div>
          </div>
        </div>
      </template>
      <div v-for="(message, msgIndex) in session.talkTraceList" :key="message.messageId">
        <div v-if="!isLastestDialog(dialogIndex)" class="item-date margin-t">
          {{ message.date }}
        </div>
        <!-- 问题 -->
        <div class="item-question margin-t" v-if="message.question">
          <span class="dialog-bg blue" :class="{ gray: message.questionLoading }">
            <template v-if="message.questionLoading">
              <i class="rotate el-icon-loading" style="margin-right: 5px" size="16" />
            </template>
            <template v-else>
              <div>{{ message.question }}</div>
              <file-item-view
                v-for="(item, ind) in message.fileArr || []"
                :key="ind"
                :file="item"
              />
            </template>
          </span>
          <!--<van-image
            :src="require('@/assets/modular/home/<USER>')"
            class="icon-q"
          />-->
        </div>
        <!-- 回答 -->
        <div v-if="!message.questionLoading">
          <div class="item-answer margin-t">
            <template v-if="isLoading == 1 && isLastestMsg(dialogIndex, msgIndex)">
              <div class="dialog-bg purple loading-text">
                <van-image
                  class="rotate"
                  style="margin-right: 5px"
                  width="16"
                  :src="require('@/assets/icon/ico_loading.png')"
                />思考中…
              </div>
            </template>
            <template v-else>
              <span class="dialog-bg purple" v-if="message.answer">
                <markdown-it-vue
                  :content="message.answer || ''"
                  class="markdown-body"
                  :options="options"
                />
                <!-- <dialog-bottom-view v-if="message.showToolView" :message="message"></dialog-bottom-view> -->
                <template v-if="message.workFlow">
                  <!--<div v-if="message.workFlow.type == 'bank_attach'&& isLastestMsg(dialogIndex, msgIndex)"
										style="color: #4187F2;margin-top: 5px;" @click="uploadBank(message)">
										<van-icon name="link-o" />
										点此上传
									</div>-->
                  <div
                    v-if="
                      message.workFlow.type == 'collect_signatures' &&
                      isLastestMsg(dialogIndex, msgIndex)
                    "
                    style="color: #4187f2; margin-top: 5px"
                    @click="sign(message)"
                  >
                    <van-icon name="link-o" />
                    点此签字
                  </div>
                  <!--<div v-else-if="message.workFlow.type == 'idno_attach'&& isLastestMsg(dialogIndex, msgIndex) "
										style="color: #4187F2;margin-top: 5px;">
										<van-icon name="link-o" />
										身份证上传
									</div>-->
                </template>
              </span>
              <!-- 身份证上传/银行卡上传 -->
              <span
                class="dialog-bg purple"
                v-else-if="message.isIdupload || message.isBankLoad"
              >
                <upload-id-image
                  ref="idCardInfoRef"
                  @idcardInfoConfirm="onIdcardInfoConfirm"
                  :dataSource="workFlow"
                  :title="title"
                  :uploadType="message.uploadType"
                >
                </upload-id-image>
              </span>
            </template>
          </div>
          <template v-if="message.workFlow">
            <template v-if="message.workFlow.type == 'claim_process'">
              <process-status
                :workFlow="message.workFlow"
                @claimCommendClick="recommendClick"
              ></process-status>
            </template>
            <template v-if="message.workFlow.type == 'no_connect_tencent'">
              <self-pay-detail
                class="dialog-bg purple margin-t"
                :message="message"
              ></self-pay-detail>
            </template>
            <radio-box-view
              v-if="message.workFlow.type == 'option_cards'"
              :disable="!isLastestMsg(dialogIndex, msgIndex)"
              :list="message.workFlow.options"
              @onConfirm="optionClick"
            ></radio-box-view>
          </template>
        </div>
      </div>
    </div>
    <bank-info-upload-view
      ref="bankInfoRef"
      @bankInfoConfirm="onBankInfoConfirm"
    ></bank-info-upload-view>

    <sign-file-view
      v-if="signFile"
      class="dialog-bg purple margin-t"
      ref="signFileRef"
      @onSignSuccess="onSignSuccess"
    ></sign-file-view>

    <!--<idcard-info-upload-view ref="idCardInfoRef" :title="title" @idcardInfoConfirm="onIdcardInfoConfirm"></idcard-info-upload-view>-->
    <!-- <ProcessDialog
      v-if="showProcessDialog"
      :total="totalMoney"
      :claimNo="claimNo"
      :insuName="name"
    ></ProcessDialog> -->
    <!-- <div id="msg_end" style="height: 1px; width: 1px"></div> -->
  </div>
</template>

<script>
import config from "@/env.config.js";
import MarkdownItVue from "markdown-it-vue";
import "markdown-it-vue/dist/markdown-it-vue.css";
import DialogBottomView from "@/components/DialogBottomView.vue";
import { ImagePreview } from "vant";
import { fetchEventSource, EventStreamContentType } from "@microsoft/fetch-event-source";
import FileItemView from "@/components/FileItemView.vue";
import RadioBoxView from "@/components/RadioBoxView.vue";
import BankInfoUploadView from "@/components/BankInfoUploadView.vue";
import SignFileView from "@/components/SignFileView.vue";
import WelcomeView from "@/components/WelcomeView.vue";
import IdcardInfoUploadView from "@/components/IdcardInfoUploadView.vue";
import ProcessStatus from "@/components/ProcessStatus.vue";
import SelfPayDetail from "@/components/SelfPayDetail.vue";
import ProcessDialog from "@/components/ProcessDialog.vue";
export default {
  name: "MessageItemView",
  components: {
    DialogBottomView,
    MarkdownItVue,
    FileItemView,
    RadioBoxView,
    [ImagePreview.Component.name]: ImagePreview.Component,
    BankInfoUploadView,
    SignFileView,
    WelcomeView,
    IdcardInfoUploadView,
    ProcessStatus,
    SelfPayDetail,
    ProcessDialog,
  },
  props: {
    total: {
      type: Number,
      default: 0,
    },
    dialogs: {
      type: Array,
      default: () => [],
    },
    // 0：正常状态， 1：在获取资料， 2：在回答
    loading: {
      type: Number,
      default: 0,
    },
    bot: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      options: {
        markdownIt: {
          html: true,
          linkify: true,
          highlight: true,
        },
        linkAttributes: {
          attrs: {
            target: "_blank",
            rel: "noopener",
          },
        },
      },
      recommendQuestion: [
        {
          id: 1,
          question: " 苏惠保2025赔付范畴？",
        },
        {
          id: 2,
          question: " 苏惠保2025赔付比例？",
        },
        {
          id: 3,
          question: " 苏惠保2025是否支持异地就医？",
        },
      ],
      questionInfo: {}, //推荐问题（个人自费/自付）
      dialogList: [],
      showProcessDialog: false,
      url: {
        sse: "createSse",
        chat: "chat", // 聊天发送消息
        uploadBankPic: "intelligent/claim/bankAttachUpload",
        claimAfterDetailPreInquiry: "intelligent/claim/claimAfterDetailPreInquiry",
      },
      sseConnected: false,
      ctrl: null,
      userInfo: null,
      firstIn: true,
      isScrolling: false,
      workFlow: {},
      showIdUpload: false,
      uploadType: "",
      // name: "", //姓名
      // claimNo: "", //赔案号
      signFile: false, //签字组件展示
      resetAnswerIndex: [], // 重新生成回答索引 项1表示diaolog索引，项2表示message索引
      // totalMoney: 0,
    };
  },
  computed: {
    isLoading: {
      get() {
        return this.loading;
      },
      set(val) {
        this.$emit("update:loading", val);
      },
    },
    isLastestDialog() {
      return (dialogIndex) => {
        let b = dialogIndex == this.dialogList.length - 1;

        return b;
      };
    },
    // 是否是最新消息
    isLastestMsg() {
      // dialogIndex：对话索引， msgIndex：消息索引
      return (dialogIndex, msgIndex) => {
        let msgs = this.dialogList[dialogIndex].talkTraceList;
        let b = dialogIndex == this.dialogList.length - 1 && msgIndex == msgs.length - 1;

        return b;
      };
    },
    dividerTitle() {
      return (dialogIndex) => {
        let title = null;
        if (this.total == 0) {
          return title;
        }
        if (this.total > this.dialogList.length) {
          if (this.dialogList.length == 1) {
            // 只有一个对话
            // title = '以下是新的对话';
          } else {
            // 多个对话
            if (this.dialogList.length - 1 == dialogIndex) {
              // 最新的对话
              title = "以上为历史对话";
            } else if (dialogIndex == 0) {
              // 第一个对话
              title = "下拉查看历史记录";
            }
          }
        } else if (dialogIndex == 0) {
          title = "没有更多了";
        } else if (this.dialogList.length - 1 == dialogIndex) {
          // 最新的对话
          title = "以上为历史对话";
        }

        return title;
      };
    },
  },
  watch: {
    dialogs: {
      handler(val) {
        if (val) {
          this.dialogList = val;
        }
      },
      immediate: true,
      deep: true, // 可以深度检测到 person 对象的属性值的变化
    },
  },
  mounted() {
    this.initSSE((result) => {
      if (result == "onopen") {
        console.log('SSE链接成功')
      }
    });
    window.addEventListener("touchstart", this.handleTouchStart);
    window.addEventListener("touchend", this.handleTouchEnd);
  },
  beforeDestroy() {
    window.removeEventListener("touchstart", this.handleTouchStart);
    window.removeEventListener("touchend", this.handleTouchEnd);
  },
  methods: {
    handleTouchStart() {
      this.isScrolling = true;
    },
    handleTouchEnd() {
      setTimeout(() => {
        // 稍微延迟确保触摸事件已结束
        this.isScrolling = false;
      }, 100); // 延时100毫秒判断触摸结束
    },
    //关闭dialog弹窗
    // closeDialog() {
    //   this.showProcessDialog = false;
    // },
    // 添加回答内容
    appendAnswer(msg) {
      this.isLoading = 2;
      let item,
        lastDialog;

      if (this.resetAnswerIndex.length > 0) {
        lastDialog = this.dialogList[this.resetAnswerIndex[0]];
        item = this.dialogList[this.resetAnswerIndex[0]].talkTraceList[this.resetAnswerIndex[1]];
      } else {
        lastDialog = this.dialogList[this.dialogList.length - 1];
        let talkTraceList = this.dialogList[this.dialogList.length - 1].talkTraceList;

        item = talkTraceList[talkTraceList.length - 1];
      }
      if (!item) { return; }

      if (msg === 'END_OF_STREAM') {
        // 结束标记
        this.stopTalking();
        item.showToolView = true;
        this.resetAnswerIndex = [];
      } else if (msg === 'PARAM_END') {
        // 参数结束标记
        let q = {
          id: 1,
          question: '',
          fileArr: [],
          answer: '',
          showToolView: false,
          questionLoading: false
        };

        this.dialogList[this.dialogList.length - 1].talkTraceList.push(q);
        this.isLoading = 0;
      } else {
        let jsonData = JSON.parse(msg);

        item.answerLoading = false;
        if (jsonData) {
          if (jsonData.sessionId) {
            lastDialog.sessionId = jsonData.sessionId;
            item.sessionId = jsonData.sessionId;
          }
          if (jsonData.messageId) {
            item.messageId = jsonData.messageId;
          }

          if (jsonData.reply) {
            item.answer += jsonData.reply;
          }

          if (jsonData.knowledgeList) {
            item.referencesList = jsonData.knowledgeList;
          }
        }
        if (jsonData.type && jsonData.type == 'switch-page') {
          this.proposalDataSource = {};
        }
        if (jsonData.workFlow && jsonData.workFlow.type) {
          item.workFlow = jsonData.workFlow;
          let talkTraceList = this.dialogList[this.dialogList.length - 1].talkTraceList;
          let isIdupload = true;
          let isBankLoad = true;
          //talkTraceList.map((talk,index)=>{
          //	if(talk.isIdupload){
          //		isIdupload = false
          //	}
          //	if(talk.isBankLoad){
          //		isBankLoad = false
          //	}
          //})
          if (jsonData.workFlow.type == "idno_attach") {
            if (isIdupload && jsonData.reply) {
              let idcard = {
                answer: "",
                messageId: "000",
                question: "",
                isIdupload: true,
                uploadType: "idcard",
              };
              this.dialogList[this.dialogList.length - 1].talkTraceList.push(idcard);
            }
          }
          if (jsonData.workFlow.type == "bank_attach") {
            if (isBankLoad && jsonData.reply) {
              let bank = {
                answer: "",
                messageId: "001",
                question: "",
                isBankLoad: true,
                uploadType: "bank",
              };
              this.dialogList[this.dialogList.length - 1].talkTraceList.push(bank);
            }
          }
        }
        this.$emit('eventStream', jsonData);
        // 如果是生成建议书页面，则显示建议书按钮
        try {
          if (jsonData.workFlow && jsonData.workFlow.param.url) {
            let url = JSON.parse(jsonData.workFlow.param.url);

            this.proposalDataSource = url.knowledgeList[0];
          } else {
            // this.proposalDataSource = {};
          }

        } catch (error) {
          console.log(error);
        }

      }

      this.toBottom();
    },
    prepareSendMsg(msg) {
      if (this.sseConnected == false) {
        this.initSSE((result) => {
          if (result == "onopen") {
            this.sendMsg(msg);
          }
        });
      } else {
        this.sendMsg(msg);
      }
    },
    /// 上传银行卡信息
    uploadBank(message) {
      this.showIdUpload = true;
      this.uploadType = "bank";
      //this.$refs.bankInfoRef.show(message)
    },
    // 上传身份证信息
    uploadIdCard(message) {
      this.showIdUpload = true;
      this.uploadType = "idcard";
    },
    /// 签字
    sign(message) {
      this.signFile = true;
      this.$nextTick(() => {
        this.$refs.signFileRef.show(message);
      });
      this.toBottom();
    },
    onSignSuccess(info) {
      this.$toast(
        "您已成功完成“苏惠保2025”的智能理赔报案！后续理赔进展请留意“东吴人寿微服务”公众号的消息通知。"
      );
      this.dialogList.push({
        sessionId: "",
        talkTraceList: [],
      });
      this.signFile = false;
      this.firstIn = false;
      this.$nextTick(() => {
        this.toBottom();
      });

      const param = {
        claimNo: info.claimNo,
        name: info.name,
      };

      this.claimNo = info.claimNo;
      this.name = info.name;

      this.$http.getAction(this.url.claimAfterDetailPreInquiry, param).then((res) => {
        console.log(res);
        if (res.code == 200) {
          // this.totalMoney = res.data.totalMoney;
          // this.showProcessDialog = true;
          const obj ={
            claimNo:info.claimNo,
            name:info.name,
            totalMoney:res.data.totalMoney,
            showProcessDialog:true
          }

          console.log(obj); 
          this.$emit("getInfo",obj)
          // this.toBottom();
        }
      });
    },
    // 上传银行卡信息
    onBankInfoConfirm() {
      let q = {
        id: 1,
        question: "",
        fileArr: [],
        answer: "",
        showToolView: false,
        questionLoading: false,
      };
      // 新增条消息，银行卡上传成功后sse会返回新消息
      this.dialogList[this.dialogList.length - 1].talkTraceList.push(q);

      if (this.sseConnected == false) {
        this.initSSE((result) => {
          if (result == "onopen") {
            this.$refs.bankInfoRef.upload((res) => {
              if (res == false) {
                // 识别失败 删除新增的消息
                this.dialogList[this.dialogList.length - 1].talkTraceList.pop();
              }
            });
          }
        });
      } else {
        this.$refs.bankInfoRef.upload(() => {});
      }
    },
    onIdcardInfoConfirm() {
      let q = {
        id: 1,
        question: "",
        fileArr: [],
        answer: "",
        showToolView: false,
        questionLoading: false,
      };
      // 新增条消息，身份证上传成功后sse会返回新消息
      this.dialogList[this.dialogList.length - 1].talkTraceList.push(q);

      if (this.sseConnected == false) {
        this.initSSE((result) => {
          if (result == "onopen") {
            this.$refs.idCardInfoRef.upload((res) => {
              if (res == false) {
                // 识别失败 删除新增的消息
                this.dialogList[this.dialogList.length - 1].talkTraceList.pop();
              }
            });
          }
        });
      } else {
        this.$refs.idCardInfoRef.upload(() => {});
      }
    },
    // 发送问题
    sendMsg(msg) {
      console.log(msg);
      let q = {
        id: 1,
        question: msg.msg,
        fileArr: msg.fileArr,
        answer: "",
        showToolView: false,
        questionLoading: true,
      };
      if (msg.isTabChange) {
        this.dialogList = [];
        //按钮切换不展示第一句消息
        q = {
          id: 1,
          question: "",
          fileArr: msg.fileArr,
          answer: "",
          showToolView: false,
          questionLoading: true,
        };
      }
      if (this.dialogList.length == 0) {
        this.dialogList.push({
          sessionId: "",
          talkTraceList: [q],
        });
      } else {
        this.dialogList[this.dialogList.length - 1].talkTraceList.push(q);
      }
      let lastDialog = this.dialogList[this.dialogList.length - 1];
      let type = {
        baoquan: "5",
        lipei: "1",
        lipeiCode: "2",
        shbServe: "6",
      };
      let param = {
        ...this.bot,
        visitorBizId: this.userInfo.phone,
        questionType: msg.questionType,
        sessionId: lastDialog.sessionId,
        newDialog: lastDialog.talkTraceList.length == 1, // 是否是新对话
        type: type[this.title],
        claimNo: msg.claimNo,
        name: msg.insuName,
      };

      let p = new FormData();
      msg.fileArr.forEach((item) => {
        p.append("files", item.file);
      });
      param.msg = msg.msg;
      p.append("paramJsonStr", JSON.stringify(param));
      q.questionLoading = false;

      this.$http
        .postAction(this.url.chat, p, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((res) => {
          console.log(res);
          if (res.code == 200) {
            q.questionLoading = false;
            this.isLoading = 1;
            this.toBottom();
          } else {
            this.$toast(res.msg);
            this.cancelMsg();
          }
        })
        .catch((res) => {
          if (res) {
            this.$toast(res.msg || "消息发送失败");
          } else {
            this.$toast("消息发送失败");
          }
          this.cancelMsg();
        });
      this.toBottom();
    },
    optionClick(e) {
      let text = e.data[0].title;
      let p = {
        msg: text,
        fileArr: [],
      };
      this.prepareSendMsg(p);
    },
    cancelMsg() {
      this.dialogList[this.dialogList.length - 1].talkTraceList.pop();
    },
    recommendClick(item) {
      let msg = {
        msg: item.question,
        claimNo: item.claimNo || "",
        insuName: item.insuName || "",
        fileArr: [],
      };
      this.questionInfo = item;
      this.prepareSendMsg(msg);

      this.toBottom();
    },
    stopTalking() {
      this.isLoading = 0;
      this.toBottom();
      this.cancelSSE();
      this.$emit("onStopTalking", {});
    },

    toBottom() {
      if (this.isScrolling) return;
      setTimeout(() => {
        this.$nextTick(() => {
          document.querySelector("#msg_end").scrollIntoView({
            behavior: "smooth",
          });
        });
      }, 100);
    },
    imagePreview(src) {
      ImagePreview({
        images: [src],
      });
    },
    initSSE(callback) {
      this.userInfo = JSON.parse(localStorage.getItem("UserInfo"));
      let that = this;
      const url = `${config.baseURL}${this.url.sse}`; // 替换为实际的SSE端点URL
      this.ctrl = new AbortController();
      fetchEventSource(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Access-Token": localStorage.getItem("Token"), // 可选：请求头
        },
        openWhenHidden: true, // 可选：控制在页面不可见时是否仍然保持连接
        responseType: EventStreamContentType, // 有些后端实现sse的时候重写了fetch，所以需要加这个参数才行
        signal: that.ctrl.signal,
        body: JSON.stringify({
          visitorBizId: that.userInfo.phone,
        }),
        onopen(response) {
          that.sseConnected = true;
          console.log("onopen");
          callback && callback("onopen");
        },
        onclose() {
          //that.cancelSSE();
          console.log("onclose");
          //that.cancelSSE();
          that.initSSE(() => {
            console.log("重连成功");
          });
        },
        onmessage(event) {
          if (event.data == "HEARTBEAT_STREAM") {
            // 连接成功后的消息 不做处理
            return;
          }
          that.appendAnswer(event.data);
        },
        onerror(error) {
          console.error("Error occurred:", error);
          that.cancelSSE();

          throw error; // 直接抛出错误，避免反复调用
        },
      });
    },
    cancelSSE() {
      if (this.ctrl) {
        this.ctrl.abort();
        this.sseConnected = false;
        this.isLoading = 0;

        // 由于abort函数只能调用一次，第二次就不管用了， aborted属性由false变成true之后，不会再次被改变了，所以下面只能重新赋值null后，再次重新实例化一个新的。
        this.ctrl = null;
        this.ctrl = new AbortController();
      }
    },
  },
};
</script>

<style scoped lang="less">
.title {
  font-size: 16px;
  color: #9c9c9c;
  line-height: 22px;
  height: 22px;
  font-style: normal;
}

.dialog-bg {
  display: inline-block;
  box-sizing: border-box;
  padding: 10px;
  font-size: 28px !important;
  font-style: normal;
}
/deep/ .markdown-body {
  /*color: #101010 !important;*/
  font-size: 28px !important;
  /*background-color: #DFEAFC !important;*/

  hr {
    height: 1.5px !important;
  }
}

.item-date {
  text-align: center;
  color: #9c9c9c;
  font-size: 13px;
}

.item-question {
  display: flex;
  justify-content: right;
  align-items: center;
}

.item-answer {
  display: flex;
  justify-content: left;
}

.white {
  background: #ffffff;
}

.blue {
  background: #3772fa;
  color: #fff;
  border-radius: 20px 0 20px 20px;
  max-width: 85%;
}
.recommend-gray {
  background: #dde9fc;
  color: #2c2e30;
  border-radius: 0 20px 20px 20px;
}
.gray {
  background: #f1f1f1 !important;
}

.purple {
  background: #ffffff;
  color: #2f3133;
  opacity: 0.95;
  border-radius: 0 20px 20px 20px;
  width: 100%;
}
.icon-q {
  width: 57px;
  height: 57px;
  margin-left: 15px;
}

.margin-t {
  margin-top: 15px;
}

.loading-text {
  display: flex;
  align-items: center;
}

.stop-talking {
  height: 32px;
  line-height: 32px;
  background-color: #e2e9ff;
  width: 90px;
  text-align: center;
  border-radius: 6px;
  color: #4c76f9;
  font-size: 13px;
}

@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.rotate {
  animation: rotateAnimation 2s linear infinite;
}
</style>
