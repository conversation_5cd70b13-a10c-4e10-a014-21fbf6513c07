<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-28 11:02:19
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2022-12-19 09:52:18
 * @Description  : 对话框文件
-->
<template>
	<div>
		<div class="file" @click="click">
			<div class="file-img">
				<van-image lazy-load width="24" fit="contain"
					:src="require(`@/assets/icon/${judgeFileType(fileObj.name)}`)" />
			</div>
			<div>
				<div class="title">{{fileObj.name}}</div>
				<div class="size">{{formatFileSize(fileObj.size)}}</div>
			</div>
			<van-icon name="close" size="20" v-if="showClose" style="position: absolute;right: -3px;top:-5px;background-color: white;border-radius: 10px;"
				@click.stop="removeFile" />
		</div>
		<file-preview-view ref="previewRef"></file-preview-view>
	</div>
</template>

<script>
	import FilePreviewView from "@/components/FilePreviewView.vue"
	export default {
		name: "FileItemView",
		components: {
			FilePreviewView
		},
		props: {
			showClose: {
				type: Boolean,
				default: false
			},
			file: {
				type: Object,
				default: () => null,
			},
		},
		data() {
			return {
				fileObj: null
			};
		},
		watch: {
			file: {
				handler(val) {
					if (val) {
						this.fileObj = val;
					}
				},
				immediate: true,
				deep: false, // 可以深度检测到 person 对象的属性值的变化
			},
		},
		mounted() {},
		methods: {
			judgeFileType(fileName) {
				if (!fileName) return;
				let ext = fileName.split('.').pop();
				let img = '';
				this.fileObj.ext = ext;
				switch (ext) {
					case 'png':
					case 'jpg':
					case 'jepg':
						img = 'ico_pic.svg';
						break;
					case 'doc':
					case 'docx':
						img = "ico_doc.svg";
						break;
					case 'xls':
					case 'xlsx':
						img = "ico_xls.svg";
						break;
					case 'pdf':
						img = "ico_pdf.svg";
						break;
					case 'txt':
						img = "ico_txt.svg";
						break;
					default:
						img = "ico_unknow.svg";
						break;
				}
				return img;
			},
			formatFileSize(bytes, decimalPlaces = 2) {
				if (bytes === 0) {
					return '0 Bytes';
				}

				const k = 1024;
				const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));

				return parseFloat((bytes / Math.pow(k, i)).toFixed(decimalPlaces)) + ' ' + sizes[i];
			},
			click() {
				console.log(this.fileObj)

				let extArr = ['docx', 'xlsx', 'pdf', 'txt', 'jpg', 'png', 'jepg'];
				if (extArr.indexOf(this.fileObj.ext) > -1) {
					this.$refs.previewRef.show(this.fileObj)
				} else {
					this.$toast('不支持该类型文件预览');
				}

			},
			removeFile() {
				this.$emit('remove', this.fileObj);
			}
		},
	};
</script>

<style lang='less' scoped>
	
	.file {
		position: relative;
		display: flex;
		justify-content: start;
		flex-wrap: nowrap;
		align-items: center;
		background-color: white;
		padding: 5px 10px;
		background: #FFFFFF;
		border-radius: 16px;
		border: 1px solid #DFE3E7;
		margin: 2px;
		width: 200px;
	}
	
	.text-c {
		text-align: center;
	}

	.title {
		width: 150px;
		font-size: 12px;
		color: #6B6B6B;
		display: -webkit-box;
		/* 使用旧版弹性盒子布局 */
		-webkit-box-orient: vertical;
		/* 设置垂直方向 */
		-webkit-line-clamp: 1;
		/* 限制显示的行数 */
		overflow: hidden;
		/* 隐藏超出部分 */
		text-overflow: ellipsis;
		/* 显示省略号 */
		line-height: 20px;
		/* 设置行高，确保两行高度正确 */
		line-height: 20px;
		/* 设置最大高度（行高 × 行数） */
	}

	.size {
		font-size: 11px;
		color: #C6C6C6;
	}


	.file-img {
		width: 24px !important;
		height: 24px !important;
		margin-right: 5px;
		margin-left: 5px;
	}
</style>