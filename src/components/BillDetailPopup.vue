<!-- 账单详情 -->
<template>
  <div >
	<van-popup 
		v-model="showPop"
		style="height:90%;"
		closeable
		close-icon="close"
		position="bottom"
		get-container="#app"
		@closed="cancel"
	>
		<div class="title">苏惠保理赔账单明细</div>
		<table class="table">
			<tr>
				<td>医疗机构名称</td>
				<!--<td>苏惠保定点医疗机构</td>-->
				<td>就医方式</td>
				<!--<td>门特结算</td>-->
				<!--<td>责任免除金额</td>-->
				<td>费用类别</td>
				<td>金额</td>
			</tr>
			<tbody v-for="item in tableList" :key="item.id">
				<tr>
					<td :rowspan="item.rowspan">{{item.fixmedinsname}}</td>
					<!--<td :rowspan="item.rowspan">{{item.shbhospital=='1' ? '是':'否'}}</td>-->
					<td :rowspan="item.rowspan">{{item.othersign}}</td>
					<!--<td :rowspan="item.rowspan">{{item.opspflag=='1'?'是':'否'}}</td>-->
					<!--<td :rowspan="item.rowspan">{{item.exclusionmoney}}</td>-->
					<td v-if="item['typeList']">{{typeText[item['typeList'][0]]}}</td>
					<td v-else></td>
					<td v-if="item['typeList']">{{item[item['typeList'][0]]}}</td>
					<td v-else></td>
				</tr>
				<template v-if="item['typeList'].length>1">
					<tr v-for="(value,index) in item['typeList']" :key="value" v-if="index!=0">
						<td>{{typeText[item['typeList'][index]]}}</td>
						<td>{{item[item['typeList'][index]]}}</td>
					</tr>
				</template>
			</tbody>
		</table>
		<div v-if="tableList.length>0">
			<p style="margin: 10px;">{{tableList[0].selfFunded}}</p>
			<p style="margin: 10px">{{tableList[0].selfPay}}</p>
		</div>
	</van-popup>
  </div>
</template>

<script>
export default {
	name: 'Name',
	components: { },
	mixins: [],
	props: {
	},
	data() {
		return {
			showPop:false,
			tableList:[],
			typeText:{
				'selfpaycashamt':'个人自付金额',
				'acctzfupay':'个人账户支付自付费用',
				'acctmulaidzfpay':'账户共济自付费用支付金额',
				'ownpaycashamt':'个人自费金额',
				'ownpayinscpamt':'个人合规自费金额',
				'acctzfepay':'个人账户支付全自费费用',
				'accthgpay':'个人账户支付合规自费费用',
				'acctmulaidzfepay':'账户共济自费费用支付金额',
				'acctmulaidhgpay':'账户共济合规自费支付金额'
			},
			url:{
				claimDetailInquiry:'/intelligent/claim/claimDetailInquiry'
			},
		}
	},
	// 挂载完成（可以访问DOM元素）
	mounted() {
	},
	// 方法
	methods: {
		isValue(value){
			if(value!==''&& Number(value)>0){
				return true
			}else{
				return false
			}
		},
		getSecondList(list){
			console.log('循环去掉第一个数组',list,list.slice(1))
			return list.slice(1)
		},
		cancel(){
			this.showPop = false
		},
		getClaim(claimNo){
			const param = {
				claimNo:claimNo
			}
			this.$http.getAction(this.url.claimDetailInquiry,param).then(res=>{
				console.log('理赔详情res----',res)
				let data = res.data
				let arr=['selfpaycashamt','acctzfupay','acctmulaidzfpay','ownpaycashamt','ownpayinscpamt','acctzfepay','accthgpay','acctmulaidzfepay','acctmulaidhgpay']
				data.map(item=>{
					let values = arr.filter(function(value){
						//if(item[value]==''||Number(item[value])>0)
						return item[value]!==''&& Number(item[value])>0
					})
					item.rowspan = values.length
					if(values.length>0){
						item['typeList']=values
					}
					//console.log('哪些数据不为空---',values,item.rowspan)
				})
				this.tableList = data
			})
		}
	}
}
</script>

<style lang='less' scoped>
.title{
	text-align: center;
	color: #EF895D;
	background-color: #FDF2E9;
	padding: 10px;
	font-size: 14px;
}
.table{
	text-align: center;
	border-spacing: 0;
    border-collapse: collapse;
	background-color: #fff;
	margin-bottom:16px ;
	font-size: 11px;
	td{
		border: 1px solid #dfe2e5;
		padding: 0.375rem 0.8125rem;
		width: 25%;
	}
}
</style>
