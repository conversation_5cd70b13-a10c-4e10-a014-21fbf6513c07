<!-- 进度查询回复 -->
<template>
  <div>
    <div class="dialog-bg margin-t purple">
      <div>您好，已为您查询到您所有的理赔报案信息，请查看</div>
      <table class="table">
        <tr style="width: 100%; background-color: #4095e5; color: #ffffff">
          <td>报案号</td>
          <td>报案时间</td>
          <td>出险人</td>
          <td>报案状态</td>
          <td>点击查看</td>
        </tr>
        <tr v-for="item in reply" :key="item.claimNo">
          <td>{{ item.claimNo }}</td>
          <td>{{ item.reportTime }}</td>
          <td>{{ item.insuName }}</td>
          <td :style="{ color: colors[item.reportState] || '#0f40f5' }">
            {{ item.reportState | status }}
          </td>
          <td style="color: #4187f2" @click="showReason(item)">查看详情</td>
        </tr>
      </table>
      <div style="display: flex; justify-content: right">
        <van-button
          round
          style="background: linear-gradient(to right, #7bbcf4, #305bef); color: #fff"
          @click="getDetailData"
          >查看医保明细数据</van-button
        >
      </div>
      <div v-if="isReason">
        <p>尊敬的{{ insuName }}先生/女士</p>
        <template
          v-if="reportState == '70' || reportState == '60' || reportState == '99'"
        >
          <div style="margin-bottom: 5px; white-space: pre-wrap">
            {{ compensationAiReason }}
          </div>
        </template>
        <template v-else>
          <div style="margin-bottom: 5px">您的理赔正在流程中，请您耐心等待</div>
        </template>
        <!--<p style="color: #4187F2;"@click="checkDetail">
				点击查询详细账单
			</p>-->
      </div>
    </div>
    <!-- <div
      class="recommend-q margin-t"
      v-show="message.recommendClaimQuestion && message.recommendClaimQuestion.length > 0"
    >
      <div class="title">推荐问题：</div>
      <div
        v-for="(item, index) in message.recommendClaimQuestion"
        :key="index"
        @click="recommendClick(item)"
      >
        <span class="dialog-bg blue margin-t">{{ item.question }}</span>
      </div>
    </div> -->

    <!-- <van-popup
      v-model="show"
      position="bottom"
      :overlay="false"
      round
      :style="{ height: '70%' }"
    >
      <div class="popup-content">
        <div class="popup-close">
          <img src="@/assets/images/icon-close.png" alt="" @click="show = false" />
        </div>
        <p>尊敬的{{ insuName }}先生/女士,您好</p>
        <p v-if="selectItem">您的赔案号是：{{ selectItem.claimNo }}</p>
        <p>根据您的保险理赔申请，我们对个人自付费用进行了详细计算，构成如下:</p>
        <div>
          <span class="font1">总自付费用=</span
          ><span class="font2"
            >个人自付金额+个人账户支付自付费用+账户共济自付费用支付金额</span
          >
        </div>
        <div>
          <span class="font1">总自费费用=</span
          ><span class="font2">
            个人合规自费金额 + 个人账户支付全自费费用 + 个人账户支付合规自费费用 +
            账户共济自费费用支付金额 + 账户共济合规自费支付金额 - 责任免除金额</span
          >
        </div>

        <div>
          <span class="font3"
            >(注：总自付费用包含在苏惠保指定医疗机构内产生的门诊或住院费用。)</span
          >
        </div>

        <div class="table-content">
          <table class="table" v-if="tableData && tableData.length > 0">
            <thead>
              <tr style="width: 100%; background-color: #4095e5; color: #ffffff">
                <td>时间</td>
                <td>医疗机构</td>
                <td>费用类别</td>
                <td>金额（元）</td>
              </tr>
            </thead>
            <tbody v-for="item in tableData" :key="item.id">
              <tr
                v-if="item['feeType'] && item['feeType'].length > 0"
                :style="{
                  borderBottom:
                    item['feeType'] && item['feeType'].length == 1
                      ? '1px solid #dfe2e5'
                      : '',
                }"
              >
                <td :rowspan="item.rowspan">{{ item.setltime }}</td>
                <td :rowspan="item.rowspan">{{ item.fixmedinsname }}</td>
                <td>{{ feeText[item["feeType"][0]] }}</td>
                <td>{{ item[item["feeType"][0]] }}</td>
              </tr>
              <template v-if="item['feeType'].length > 1">
                <tr
                  v-for="(fee, idx) in item['feeType']"
                  :key="fee"
                  v-if="idx != 0"
                  :style="{
                    borderBottom:
                      idx == item['feeType'].length - 1 ? '1px solid #dfe2e5' : '',
                  }"
                >
                  <td>{{ feeText[item["feeType"][idx]] }}</td>
                  <td>{{ item[item["feeType"][idx]] }}</td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
        <div v-if="detail" class="deatil-text">
          根据您的理赔申请，我们已完成了审核并进行了赔付。本次理赔涉及的是医疗费用，具体赔付金额为{{ detail.totalMoney }}元。
        </div>
        <table class="table">
          <thead>
            <tr style="width: 100%; background-color: #4095e5; color: #ffffff">
              <td>总费用（元）</td>
              <td>医疗基金支付总额</td>
              <td>个人自费金额</td>
              <td>个人自付金额</td>
            </tr>
          </thead>
          <tr v-for="item in totalData">
            <td>{{ item.total }}</td>
            <td>{{ item.totalFundPaySumAmt }}</td>
            <td>{{ item.totalSelfFund }}</td>
            <td>{{ item.totalSelfPay }}</td>
          </tr>
        </table>
      </div>
    </van-popup> -->

    <Popup
      :show.sync="show"
      :insuName="insuName"
      :selectItem="selectItem"
      :detail="detail"
    ></Popup>
  </div>
</template>

<script>
import Popup from "./Popup.vue";

export default {
  name: "Name",
  components: {
    Popup,
  },
  mixins: [],
  props: {
    workFlow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      reply: [],
      detail: null,
      insuName: "",
      reportState: "", //70：已关闭，60：已完成，其他：流程中
      isReason: false, //是否展示详情
      compensationAiReason: "",
      colors: {
        70: "#bd3124",
        60: "#84e384",
      },
      selectItem: null,
      message: {},
      show: false,
      url: {
        claimDetailPreAskInquiry: "/intelligent/claim/claimDetailPreAskInquiry",
        claimDetailPreInquiry: "/intelligent/claim/claimDetailPreInquiry",
      },
      feeText: {
        selfpaycashamt: "个人自付额",
        acctzfupay: "账户自付额",
        acctmulaidzfpay: "共济自付额",
        ownpaycashamt: "个人自费额",
        acctzfepay: "账户自费额",
        acctmulaidzfepay: "共济自费额",
        ownpayinscpamt: "个人合规自费额",
        accthgpay: "账户合规自费额",
        acctmulaidhgpay: "共济合规自费额",
        exclusionmoney: "责任免除额",
        fundpaysumamt: "基金支付金额",
      },
    };
  },

  computed: {
    tableData() {
      console.log(this.detail);
      if (this.detail) {
        let arr = [
          "selfpaycashamt",
          "acctzfupay",
          "acctmulaidzfpay",
          "ownpaycashamt",
          "acctzfepay",
          "acctmulaidzfepay",
          "ownpayinscpamt",
          "accthgpay",
          "acctmulaidhgpay",
          "exclusionmoney",
          "fundpaysumamt",
        ];

        let data = this.detail.data;
        data.map((item) => {
          let value = arr.filter((arr) => {
            return item[arr] != "" && Number(item[arr]) != 0;
          });
          item["feeType"] = value;
          item["rowspan"] = value.length;
        });
        console.log("看看最终的结果 数据", data[0]);
        return data;
      } else {
        return [];
      }
    },
    totalData() {
      if (this.detail) {
        let arr = [
          {
            total: this.detail.totalMoney,
            totalFundPaySumAmt: this.detail.totalSelfFund,
            totalSelfFund: this.detail.totalSelfFund,
            totalSelfPay: this.detail.totalSelfPay,
          },
        ];

        return arr;
      } else {
        return [];
      }
    },
  },
  watch: {
    workFlow: {
      handler(obj) {
        if (obj) {
          this.reply = obj.reasons;
          this.message = obj;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  filters: {
    status(key) {
      let value = "";
      switch (key) {
        case "70":
          value = "已关闭";
          break;
        case "60":
          value = "已完成";
          break;
        case "99":
          value = "体验";
          break;
        default:
          value = "流程中";
          break;
      }
      return value;
    },
  },
  // 挂载完成（可以访问DOM元素）
  mounted() {
    //
  },
  // 方法
  methods: {
    showReason(item) {
      console.log("item", item);
      this.selectItem = item;
      this.isReason = true;
      this.compensationAiReason = item.compensationAiReason;
      this.reportState = item.reportState;
      this.insuName = item.insuName;
      if (
        item.reportState == "70" ||
        item.reportState == "60" ||
        item.reportState == "99"
      ) {
        // this.getrecommendQuestion(item.claimNo);
      } else {
        this.message = Object.assign({}, this.message, { recommendClaimQuestion: [] });
      }
    },
    //查看医保明细数据
    getDetailData() {
      if (!this.selectItem) {
        this.$toast("请先查看一条医保信息详情");
        return;
      } else {
        const param = { claimNo: this.selectItem.claimNo, name: this.insuName };
        this.$http.getAction(this.url.claimDetailPreInquiry, param).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.show = true;
            this.detail = res.data;
          }
        });
      }
    },

    getrecommendQuestion(claimNo) {
      const param = {
        claimNo: claimNo,
      };
      this.$http.getAction(this.url.claimDetailPreAskInquiry, param).then((res) => {
        console.log("获取推荐问题", res);
        if (res.code == 200 && res.data.asks.length > 0) {
          let asks = [];
          res.data.asks.map((item, index) => {
            asks.push({
              id: index,
              question: item,
              claimNo: claimNo,
              insuName: this.insuName,
            });
          });
          this.message = Object.assign({}, this.message, {
            recommendClaimQuestion: asks,
          });
          console.log("看看这个reply", this.message);
        }
      });
    },
    recommendClick(item) {
      this.$emit("claimCommendClick", item);
    },
  },
};
</script>

<style lang="less" scoped>
.popup-content {
  position: relative;
  padding: 20px;
  p {
    font-size: 24px;
    margin: 8px 0;
  }

  .font1 {
    color: #2f3133;
    font-size: 28px;
    font-weight: 500;
  }
  .font2 {
    color: #4671f7;
    font-size: 24px;
    font-weight: 500;
  }
  .font3 {
    color: #ef8e5c;
    font-size: 24px;
  }

  .deatil-text {
    font-size: 24px;
    color: #2f3133;
  }

  .table-content {
    margin-top: 8px;
    width: 100%;
    height: 800px;
    overflow: auto;
  }
  .table {
    margin: 0 auto;
  }
}

thead {
  position: sticky;
  top: 0;
}

.popup-close {
  position: absolute;
  right: 40px;
  top: 40px;
  img {
    width: 128px;
    height: 128px;
  }
}

.dialog-bg {
  display: inline-block;
  box-sizing: border-box;
  padding: 10px;
  font-size: 28px !important;
  font-style: normal;
}
.purple {
  background: #ffffff;
  color: #2f3133;
  opacity: 0.95;
  border-radius: 0 20px 20px 20px;
  // width: 1097px;
  width: 100%;
}
.blue {
  background: #dfeafc;
  color: #00010a;
}
.margin-t {
  margin-top: 15px;
}

.table {
  margin: 15px auto 0;
  text-align: center;
  border-spacing: 0;
  border-collapse: collapse;
  background-color: #fff;
  margin-bottom: 16px;
  width: 95%;
  td {
    border: 1px solid #dfe2e5;
    padding: 5px;
  }
  tr {
    display: table-row;
  }
}

.width25 {
  width: 25%;
}
</style>
