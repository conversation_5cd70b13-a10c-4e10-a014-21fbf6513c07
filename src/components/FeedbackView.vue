<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2023-01-03 14:51:09
 * @LastEditors  : <PERSON><PERSON><PERSON>
 * @LastEditTime : 2024-07-31 13:14:27
 * @Description  : 输入框
-->
<template>
	<div>
		<van-popup v-model="showPop" style="max-height: 80%;" position="top" get-container="body" @closed="cancel">
			<div class="content margin-t">
				<div>
					<div class="title require"> 问题类型：</div>
					<radio-button-view ref="qtypeRef" plain :multiple="type == 1" :list="questionTypeList"
						@onChange="onChange" />
				</div>
				<div v-if="needEdit">
					<div class="title require"> 请点击有误的内容：</div>
					<div>
						<radio-button-view ref="textRef" :height="28" :text-padding-top="3" :text-padding="3"
							:text-margin="1" :multiple="true" :list="editList" @onChange="onTextChange" />
					</div>
				</div>
				<van-divider />
				<div class="bottom-input">
					<div class="title"> 问题描述：</div>
					<van-field autofocus v-model="desc" :border="false" rows="5" type="textarea" maxlength="1000"
						placeholder="请输入描述内容" show-word-limit clearable />
					<div class="bottom-div">
						<van-button class="cancel-btn" type="default" size="small" @click="cancel">取消</van-button>
						<van-button class="confirm-btn" type="info" color="linear-gradient(to right, #6087FF, #436FF6);"
							size="small" :loading="loading" @click="confirm">发送</van-button>
					</div>
				</div>
			</div>
		</van-popup>
	</div>
</template>

<script>
	import RadioButtonView from "@/components/RadioButtonView.vue"

	const goodList = [{
		title: '内容准确',
		value: '100',
	}, {
		title: '全面完整',
		value: '101',
	}, {
		title: '格式规范',
		value: '102',
	}];

	const badList = [{
		title: '内容有误',
		value: '200',
		edit: true
	}, {
		title: '内容欠缺',
		value: '201',
	}, {
		title: '无法回答',
		value: '202',
	}, {
		title: '答非所问',
		value: '203',
	}, {
		title: '其他',
		value: '204',
	}]

	export default {
		name: "FeedbackView",
		components: {
			RadioButtonView,
		},
		props: {},
		data() {
			return {
				loading: false,
				showPop: false,
				desc: '',
				popHeight: 280,
				type: 1, // 1：点好，2：点坏
				questionTypeList: [],
				selectedTypeList: [],
				needEdit: false,
				editList: [],
				selectedEditList: [],
				message: {},
			};
		},
		watch: {
			type: {
				handler(val) {
					if (val) {
						this.questionTypeList = val == 1 ? goodList : badList;
					}
				},
				immediate: true,
				deep: false, // 可以深度检测到 person 对象的属性值的变化
			},
		},
		mounted() {},
		methods: {
			/* 
				以指定分隔符分割文字，分割后的内容保留分隔符
				text: 要分割的内容，
				delimiter: 分隔符
				maxLength: 分割后文字最大长度，如超出长度再分割
			 */
			splitTextWithDelimiter(text, delimiter, maxLength = 15) {
				// 正则表达式匹配常用的标点符号
				const regex = new RegExp(`[^${delimiter}]+[${delimiter}]`, 'g');

				// 使用split方法分割文本，并保留分隔符
				const parts = text.match(regex);
				console.log(parts)
				if (!parts) {
					return [text];
				}
				// 对每个部分检查长度，如果超过 maxLength，则进一步分割
				const result = [];
				for (const part of parts) {
					if (part.length <= maxLength) {
						result.push(part); // 如果长度不超过 maxLength，直接加入结果
					} else {
						// 如果长度超过 maxLength，按 maxLength 分割
						let start = 0;
						while (start < part.length) {
							// 截取最多 maxLength 个字符
							const chunk = part.slice(start, start + maxLength);
							result.push(chunk);
							start += maxLength;
						}
					}
				}
				return result;
			},
			show(type, msg) {
				this.loading = false;
				this.showPop = true;
				this.type = type;
				this.message = {
					...msg
				};
			},
			onChange(e) {
				this.selectedTypeList = [...e.data];
				this.editList = [];
				this.needEdit = false;
				if (this.type == 2 && this.selectedTypeList.length > 0) {
					let item = this.selectedTypeList[0];
					this.needEdit = item.edit || false;
					if (this.needEdit) {
						this.editList = this.splitTextWithDelimiter(this.message.answer, '，。！？；：').map(item => {
							return {
								title: item,
								value: item
							}
						});
					}
				}
			},
			onTextChange(e) {
				this.selectedEditList = [...e.data];
			},
			confirm() {
				if (this.selectedTypeList.length == 0) {
					this.$toast("请选择问题类型");
					return;
				}
				if (this.type == 2 &&
					this.selectedTypeList[0].edit == true &&
					this.selectedEditList.length == 0) {
					this.$toast("请选择有误的问题内容");
					return;
				}
				
				this.$emit("confirm", {
					type: this.type,
					list: this.selectedTypeList, //问题类型
					errorList: this.selectedEditList, // 选择【内容有误】选项选择的错误内容
					desc: this.desc
				})

				this.cancel();
			},
			cancel() {
				console.log("点击了cancel")
				this.showPop = false;
				this.needEdit = false;
				this.desc = '';
				this.message = null;
				this.selectedTypeList = [];
				this.editList = [];
				this.selectedEditList = [];
				this.$refs.qtypeRef.reset();
				
				this.$emit("cancel", {
					
				})
			},
		},
	};
</script>

<style scoped lang='less'>
	/deep/ .van-field__word-limit {
		text-align: left;
	}

	.content {
		display: flex;
		flex-direction: column;
		/* height: 100%; */
	}

	.bottom-input {
		margin-top: auto;
		margin-bottom: 10px;

		.bottom-div {
			display: flex;
			justify-content: right;
			padding: 0 10px;
			margin-top: -30px;

			.cancel-btn {
				width: 80px;
				margin-right: 10px;
			}

			.confirm-btn {
				width: 80px;
			}
		}
	}

	.title {
		font-weight: 400;
		font-size: 16px;
		color: #262628;
		line-height: 25px;
		font-style: normal;
	}

	.margin-t {
		margin-top: 20px;
	}

	.require::before {
		content: "*";
		color: #CE1A1A;
	}
</style>