<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2023-01-03 14:51:09
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2025-05-26 09:42:24
 * @Description  : 身份证识别
-->
<template>
	<div>
		<van-popup v-model="showPop" style="height:90%;" closeable close-icon="close" position="bottom" get-container="#app"
			@closed="cancel">
			<div class="title">拍摄时请确保身份证边框完整、字迹清晰、亮度均衡，识别内容有误时请点击重拍。</div>
			<div class="flex-start margin-t">
				<van-uploader
					accept=".png, .jpg, .jepg"
					image-fit="contain"
					:max-size="4 * 1024 * 1024"
					@oversize="onOversize"
					style="width: 250px;height: 148px;"
					v-model="fileJustList"
					:max-count="1"
					:after-read="afterReadJust"
				>
					<van-image
						width="250"
						height="148"
						:src="require('@/assets/images/ico-bank-upload.png')">
					</van-image>
				</van-uploader>
				<div>点击拍摄身份证人像面影像信息</div>
				<div style="margin-top: 30px;">
					<van-uploader
						accept=".png, .jpg, .jepg"
						image-fit="contain"
						:max-size="4 * 1024 * 1024"
						@oversize="onOversize"
						style="width: 250px;height: 148px;"
						v-model="fileBackList"
						:max-count="1"
						:after-read="afterReadBack"
					>
						<van-image
							width="250"
							height="148"
							:src="require('@/assets/images/ico-bank-upload.png')">
						</van-image>
					</van-uploader>
					<div>点击拍摄身份证国徽面影像信息</div>
				</div>
				<van-button :loading="loading" class="margin-t2" type="info" block @click="confirm">提   交</van-button>
			</div>
			
		</van-popup>
	</div>
</template>

<script>
	
	export default {
		name: "IdcardInfoUploadView",
		components: {
		},
		props: {
			title: {
				type: String,
				default: '',
			},
		},
		data() {
			return {
				showPop: false,
				fileJustList: [],
				fileBackList: [],
				fileList: [],
				loading: false,
				url: {
					claimIdUpload: 'intelligent/claim/idAttachUpload',
					baoquanIdUpload: '/intelligent/preserve/idAttachUpload',
				},
				msg: null
			};
		},
		mounted() {

		},
		methods: {
			show(message) {
				this.showPop = true;
				this.msg = message;
				console.log(message)
			},
			cancel() {
				this.showPop = false;
				this.fileJustList = [];
				this.fileBackList = [];
				this.loading = false;
			},
			afterReadJust(file){
				console.log('身份证人像面信息',this.fileJustList)
			},
			afterReadBack(file){
				console.log('身份证国徽面信息',file,this.fileBackList)
			},
			onOversize() {
				this.$toast('图片大小不能超过4MB');
			},
			upload(callback) {
				let p = new FormData();
				p.append('frontImage',this.fileJustList[0].file)
				p.append('backImage',this.fileBackList[0].file)
				let userInfo = JSON.parse(localStorage.getItem("UserInfo")) || {};
				//p.append('taskId', 'Tbc147286b2e84d7186a468481c549230');
				p.append('taskId', userInfo.phone);
				p.append('sessionId', this.msg.sessionId);
				p.append('type',this.$route.query.type || '')
				p.append('process',this.msg.workFlow.process || '')
				this.loading = true;
				let url = this.title=='lipei'?this.url.claimIdUpload :this.url.baoquanIdUpload
				this.$http.postAction(url, p, {
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				}).then(res=> {
					this.$toast(res.msg);
					this.cancel();
					callback && callback(true);
				}).catch(res=> {
					callback && callback(false);
					this.loading = false;
					this.$toast(res.msg ||res.message);
				});
			},
			confirm() {
				console.log(this.fileList)
				if(this.fileJustList.length == 0) {
					this.$toast('请上传身份证人像面信息');
					return;
				}
				if(this.fileBackList.length == 0) {
					this.$toast('请上传身份证国徽面信息');
					return;
				}
				
				this.$emit('idcardInfoConfirm',{
					file: this.fileList,
					msg: this.msg
				})
			}
		},
	};
</script>

<style scoped lang='less'>
	.title {
		color: #EF895D;
		background-color: #FDF2E9;
		padding: 10px;
		font-size: 14px;
		font-weight: normal;
	}
	
	.flex-start {
		/* display: flex; */
		/* flex-direction: column; */
		/* justify-content: center;
		align-items: center; */
		padding: 20px;
		/* flex-wrap: wrap; */
		text-align: center;
	}
	
	.margin-t {
		margin-top: 30px;
	}
	
	.margin-t2 {
		margin-top:50px;
	}
	/deep/.van-uploader__preview-image{
		width: 250px;
		height: 148px;
	}
	/* /deep/ .van-uploader__preview {
		position: absolute;
		
		.van-uploader__preview-image {
		  width: auto;
		  max-height: 148px !important;
		  object-fit: cover; 
		}
	} */
</style>