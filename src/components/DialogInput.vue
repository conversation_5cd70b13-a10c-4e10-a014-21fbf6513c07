<template>
	<div style="width: 100%;height:100%;display: flex;align-items: center;flex-direction: column;">

		<div v-if="loading != 0" class="new-dialog" @click="newDialog">
			<i class="el-icon-send-loading" />
			<span class="dialog-btn">停止生成</span>
		</div>

		<div :class="{'disable': loading!=0}" class="input-container ">
			<div v-if="fileList.length > 0" class="file-container">
				<div v-for="(item, index) in fileList" :key="index">
					<file-item-view show-close :file="item" @remove="removeFile(index)" />
				</div>
			</div>
			<div class="flex_space_between">
				
				<van-field v-model="text" type="textarea" :border="false" rows="1" autosize autofocus
					placeholder="请给小智发消息" @focus="inputting = true" @blur="inputting= false" clearable
					maxlength="1000" />
				<div class="flex_space_between padding-h" style="flex-shrink: 0;margin-left: 0px;width: 110px;box-sizing: border-box;">
					<van-icon :name="showAttach ? 'close': 'add-o'" size="22" color="#878895"
						@click="showAttach = !showAttach" />
					<van-button type="info" style="width: 62px;height: 32px;margin-left: 10px;" size="small" :disabled="!text"
						@click="sendMsg">发送</van-button>
				</div>
			</div>
			<attachment-view v-show="showAttach" @confirm="attachmentConfirm"></attachment-view>
		</div>
	</div>
</template>

<script>
	import FileItemView from './FileItemView.vue';
	import RadioButtonView from './RadioButtonView.vue';
	import AttachmentView from './AttachmentView.vue';

	export default {
		name: 'DialogInput',
		components: {
			FileItemView,
			RadioButtonView,
			AttachmentView
		},
		props: {
			showTopTitle: {
				default: true,
				type: Boolean
			},
			showNewDialogBtn: {
				default: false,
				type: Boolean
			},
			// 0：正常状态， 1：在获取资料， 2：在回答
			loading: {
				type: Number,
				default: 0
			},
			checkList: {
				type: Array,
				default: () => {
					return [];
				}
			},
			pageContent: {
				type: String,
				default:''
			}
		},
		data() {
			return {
				text: '',
				// checkList: [{
				// 	title: '深度思考',
				// 	value: '1',
				// 	className: 'el-icon-think',
				// 	clickClassName: 'el-icon-think-click'
				// }, {
				// 	title: '联网搜索',
				// 	value: '2',
				// 	className: 'el-icon-internet',
				// 	clickClassName: 'el-icon-internet-click'
				// }],
				answerMode: [], // 回答模式  是否需要深度思考或者联网搜索
				fileList: [],
				inputting: false, // 是否在输入
				showAttach: false,
				answerMode: [],
				inputTags: [{
					text: '我要理赔',
					content: '苏惠保2025理赔报案',
					id: 0
				}, {
					text: '进度查询',
					content: '查询报案状态',
					id: 1
				}, {
					text: '在线客服',
					content: '',
					href: 'https://soochowlife.qiyukf.com/client?k=0639ae5b1fb6397051eb982cbc8a8743&wp=1&robotShuntSwitch=0&shuntId=0',
					id: 2
				},]
			};
		},
		computed: {
			isLoading: {
				get() {
					return this.loading;
				},
				set(val) {
					this.$emit('update:loading', val);
				}
			}
		},
		watch: {},
		mounted(){
		},
		methods: {
			attachmentConfirm(e) {
				let files = e.data.map(item => {
					return {
						...item,
						src: item.content,
						name: item.file.name,
						size: item.file.size,
					}
				});
				this.fileList.push(...files);
			},
			removeFile(index) {
				this.fileList.splice(index, 1);
			},
			newDialog() {
				this.stopAnswer();
				this.clear();
				this.$emit('onNewDialog', {

				});
			},
			inputTagClick(item) {
				console.log(item)
				if(item.content) {
					this.text = item.content;
					this.sendMsg();
				}
				if(item.href) {
					window.location.href = item.href;
				}
			},
			sendMsg() {
				console.log('手动输入的loading',this.loading)
				if (!this.text) {
					return;
				}
				this.inputting = false;
				console.log('发送消息:' + this.text);
				this.$emit('onSendMsg', {
					data: this.text,
					file: this.fileList,
					mode: this.answerMode
				});

				this.$nextTick(() => {
					this.clear();
				});
			},
			stopAnswer() {
				this.$emit('onStopAnswer', {});
			},
			clear() {
				this.text = '';
				this.fileList = [];
			}
		}
	};
</script>

<style scoped lang="less">
	.disable {
		pointer-events: none;

		background-color: #F7F8FA !important;

		/deep/ .van-cell {
			background-color: #F7F8FA !important;
		}
	}

	.flex_space_between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.flex_start {
		display: flex;
		justify-content: start;
		align-items: center;
	}
	
	.padding-h {
		padding-left: 5px;
		padding-right: 10px;
	}

	.margin-t10 {
		margin-top: 10px;
	}

	.margin-t20 {
		margin-top: 20px;
	}


	.sub-title {
		font-size: 16px;
		color: #333333;
		line-height: 24px;
		text-align: center;
	}

	.input-container {
		width: 100%;
		background: #fff;
		border: 1px solid #E8EAEE;
		/*使用css样式穿透,input框的样式就没了*/
		/deep/ .van-field__body {
			background-color: rgba(247,247,247,1);
			background-image: none;
			border-radius: 6px;
			border: 0px;
			width: 100%;
			padding: 5px 5px 5px 10px;
			box-sizing: border-box;
		}
	}

	.focus-border {
		-webkit-appearance: none;
		box-shadow: 0px 6px 10px 0px rgba(192, 204, 243, 0.09);
		background: linear-gradient(to right, #fff, #fff) padding-box,
			linear-gradient(148deg, rgba(110, 181, 248, 1), rgba(92, 90, 239, 1)) border-box;
		background-clip: padding-box, border-box;
		border: 1px solid transparent;
	}

	.el-icon-send {
		background: url('../assets/icon/ico_send.png') center no-repeat;
		background-size: 20px;
		width: 20px;
		height: 20px;
		cursor: pointer;
		border-radius: 50%;
		transition: box-shadow 400ms ease;
	}

	.el-icon-send:hover {
		box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
	}

	.el-icon-send-disable {
		background: url('../assets/icon/ico_send_disable.png') center no-repeat;
		background-size: 36px;
		width: 36px;
		height: 36px;
		cursor: not-allowed;
	}

	.el-icon-send-loading {
		background: url('../assets/icon/ico-stop-answer.svg') center no-repeat;
		background-size: 18px;
		width: 18px;
		height: 18px;
		cursor: pointer;
		border-radius: 50%;
	}


	.new-dialog {
		margin: 10px;
		width: 110px;
		height: 36px;
		background: #F4F7FC;
		box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.17);
		border-radius: 6px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		transition: box-shadow 400ms ease;

		.dialog-btn {
			margin-left: 6px;
			font-size: 14px;
			color: #2F3133;
			line-height: 24px;
			text-align: left;
			font-style: normal;
		}
	}

	.new-dialog:hover {
		box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
	}


	.file-container {
		display: flex;
		justify-content: start;
		align-items: center;
		flex-wrap: wrap;
		width: 100%;
		background: #F7F8FA;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		// border: 1px solid rgba(79, 121, 250, 0.2);
		border-bottom: none;
		padding: 5px;
		box-sizing: border-box;
	}
	
	.input-tag {
		margin-left: 5px;
		margin-top: 10px;
		
		/deep/.van-tag {
			padding:2px 20px;
			height: 20px;
			line-height: 20px;
			font-size: 12px;
		}
	}
</style>