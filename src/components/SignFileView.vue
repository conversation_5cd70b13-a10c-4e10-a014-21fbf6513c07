<!--
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2023-01-03 14:51:09
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2025-06-04 10:34:55
 * @Description  : 输入框
-->
<template>
	<div id="sign">
		<div class="flex-start" v-if="tabIndex!=3" >
			<div v-for="(item, index) in tabs" :key="index" class="tab-box">
				<div 
					class="tab"
					:class="{'tab-active': (tabIndex == item.id)}" @click="tabClick(item)"
				>
					{{item.title}}
				</div>
				<div class="bottom-line" :style="{'opacity':tabIndex == item.id ?'1':'0',}"></div>
			</div>
		</div>
		<div ref="bottom" class="bottom-div">
			<shb-pdf1 v-if="tabIndex == 0">
			</shb-pdf1>
			<shb-pdf2 v-else-if="tabIndex == 1">
			</shb-pdf2>
			<shb-pdf3 v-else-if="tabIndex == 2">
			</shb-pdf3>
			<div v-else-if="tabIndex == 3">
				<Sign ref="sign"/>
			</div>
		</div>
		<div v-if="tabIndex==0 || tabIndex==1" class="button-box">
			<van-button v-if="confirmIndex < tabIndex" round @click="confirm(tabIndex)" class="button">确认</van-button>
		</div>
		<div v-if="tabIndex==2" class="button-box">
			<van-button
				v-if="confirmIndex < tabIndex"
				round
				@click="sign"
				class="button"
			>出险人签字</van-button>
		</div>
		
	</div>
</template>

<script>
	import ShbPdf1 from "./pdfHtml/ShbPdf1.vue"
	import ShbPdf2 from "./pdfHtml/ShbPdf2.vue"
	import ShbPdf3 from "./pdfHtml/ShbPdf3.vue"
	import Sign from '@/components/Sign.vue'

	export default {
		name: "SignFileView",
		components: {
			ShbPdf1,
			ShbPdf2,
			ShbPdf3,
			Sign
		},
		props: {
		},
		data() {
			return {
				signKey: 0,
				showPop: false,
				loading: false,
				url: {
					getSignerInfo: 'intelligent/claim/getSignerInfo',
					saveSign: 'intelligent/claim/saveSignData'
				},
				tabs: [{
					id: 0,
					title: '反保险欺诈提示',
				}, {
					id: 1,
					title: '声明及授权',
				}, {
					id: 2,
					title: '苏州医保数据调取授权书',
				}, ],
				tabIndex: 0,
				confirmIndex: -1,
				msg: null,
				name: null,
				signShow:false,
			};
		},
		mounted() {
			
		},
		methods: {
			show(message) {
				this.showPop = true;
				this.msg = message;
				console.log(message)
				let userInfo = JSON.parse(localStorage.getItem("UserInfo")) || null;
				let p = {
					taskId: userInfo.phone,
				}
				this.$http.getAction(this.url.getSignerInfo, p).then(res => {
					if (res.code == '200') {
						this.name = res.data.applName;
					}
				})
			},
			cancel() {
				this.showPop = false;
				this.loading = false;
			},
			tabClick(item) {
				console.log('tab切换',item)
				if (this.confirmIndex < item.id - 1) {
					return;
				}
				this.tabIndex = item.id;
			},
			confirm(index) {
				this.confirmIndex = index;
				this.tabIndex++;
			},
			sign() {
				// this.cancel();
				// this.$emit("onSign", {})
				if(!this.name) {
					this.$toast('未获取到申请人姓名，请重新点击签字');
					return
				}
				this.tabIndex = 3
				this.$nextTick(()=>{
					//this.$refs.sign.setSignHtml()
					this.showSignView(true, this.name)
					
				})
			},
			showSignView(show, name) {
				this.signKey++;
				let dialog= document.getElementById("single_dialog")
					//let sign = document.getElementById('sign')
					dialog.style.display = show ? "block" : "none";
					//dialog.style.width = sign.offsetWidth - 20 +'px'
				if (show) {
					this.$refs.sign.signInit(name, (res) => {
						console.log(res)
						this.showSignView(false)
						if (res.srcImg) {
							let userInfo = JSON.parse(localStorage.getItem("UserInfo")) || null;
							let p = {
								taskId: userInfo.phone,
								orderNo: res.orderNo,
								signData: res
							}
							this.$toast.loading({
								duration: 0, 
								message: '加载中...',
								forbidClick: true,
							});
							this.$http.postAction(this.url.saveSign, p).then(res => {
								this.$toast.clear();
								
								if (res.code == '200') {
									this.cancel();
									const info = {
										claimNo:res.data.claimNo,
										name:res.data.name
									}
									this.$emit("onSignSuccess",info)
								} else {
									this.$toast(res.msg);
								}
							}).catch(res => {
								this.$toast.clear();
								if (res && res.msg) {
									this.$toast(res.msg)
								}
							})
						}
					})
					
				}
				
			},

		},
	};
</script>

<style scoped lang='less'>
	.title {
		color: #EF895D;
		background-color: #FDF2E9;
		padding: 10px;
		font-size: 14px;
		font-weight: normal;
	}

	.flex-start {
		margin-top: 10px;
		box-sizing: border-box;
		display: flex;
		justify-content: start;
		align-items: center;
		padding: 5px 10px 0 10px;
		overflow-x: auto;
		height: 60px;
		align-self: flex-start;
	}

	.margin-t {
		margin-top: 30px;
	}

	.margin-t2 {
		margin-top: 50px;
	}

	.tab {
		box-sizing: border-box;
		/*border: 1px solid #EFEFEF;*/
		font-size: 12px;
		background-color: white;
		color: #646567 100%;
		padding: 10px 15px;
		text-align: center;
		white-space: nowrap;
		margin: 0 5px;
		font-size: 25px;
	}
	.tab-box{
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.tab-active {
		color: #EF8E5C;
		font-size: 28px;
		/*box-shadow: 0px 2px 6px 0px rgba(222, 134, 143, 0.4);*/
	}
	.bottom-line{
		width: 58px;
		height: 7px;
		border-radius: 3px;
		background: #EF8E5C;
	}
	.bottom-div {
		overflow-y: auto;
		height: 400px;
		margin-bottom: 10px;
	}
	.button-box{
		width: 420px;
		height: 88px;
		margin:20px auto;
	}
	.button{
		width: 100%;
		border-radius: 44px;
		height: 100%;
		background: linear-gradient(to right,#7BBCF4,#305BEF);
		color: #FFFFFF;
		font-size: 28px;
	}
</style>