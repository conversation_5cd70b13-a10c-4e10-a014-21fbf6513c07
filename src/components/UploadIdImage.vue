<!--
 * <AUTHOR> a大师兄
 * @Date         : 2025-05-14 10:20:10
 * @LastEditors  : a大师兄
 * @LastEditTime : 2025-06-06 08:06:37
 * @Description  : 上传身份证印象
-->
<template>
	<div class='layout-content_top ins-upload-qr'>
        <div class="insure-title_h1 insure-text_color">
            <span v-if="uploadType=='idcard'">
                请用微信扫描下方二维码，上传您的身份证影像
            </span>
            <span v-else style="color: #EF8E5C;">
                您好，请用微信扫描下方二维码，上传您的本次领款账户信息。
            </span>
        </div>
        <div class="qrcode-bg">
            <div class="text">
                打开手机【扫一扫】
            </div>
            <div class="ins-upload_qr_img qrcode" id="qrcode">
                <!--<img
                    :src="require('@/assets/modular/home/<USER>')"
                    alt=""
                >-->
            </div>
        </div>
	</div>
</template>

<script>
import storage from '@/storage/index';
export default {
    name:'insure-upload-id-image',
    props:{
        qr:String,
        dataSource:{
            type:Object,
            default:()=>{}
        },
        title: {
            type: String,
            default: '',
        },
        uploadType:{
            type: String,
            default: '',
        },
        
    },
    data () {
        return {

        };
    },
    components: {

    },
    computed:{

    },
    mounted() {
        try {
            let botId = 16
            let type={
					"baoquan":"5",
					"lipei":"1",
					"lipeiCode":"2",
					"shbServe":"6"
				}
                let index = document.getElementsByClassName('qrcode').length-1
                console.log('二维码元素',document.getElementsByClassName('qrcode')[index])
            let qrcode = new window.QRCode(document.getElementsByClassName('qrcode')[index], {
                    width : 180,
                    height : 180
                }),
                {sessionId,process} = this.dataSource,
                userInfo = JSON.parse(localStorage.getItem("UserInfo")) || {},
                token = localStorage.getItem("Token"),
                query=`sessionId=${sessionId}&botId=${botId}&title=${this.title}&process=${process}&taskId=${userInfo.phone}&Token=${token}&type=${type[this.title]}`,
                base=`${window.location.origin}/preserveui/`,
                elText = this.uploadType=='idcard'?`${base}#/ocrIdcard?${query}`:`${base}#/ocrBank?${query}`;
            console.log('二维码生成地址',elText,this.dataSource);
            qrcode.makeCode(elText);
        } catch (error) {
            console.log('222', error);
        }
    },
    methods: {

    }
};

</script>
<style scoped lang='less'>
.qrcode-bg{
    background: #FFFCF4;
    width: 256px;
    height: 300px;
    padding-top: 20px;
    box-sizing: border-box;
    margin: 40px;
    text-align: center;
}
.text{
    color: #EF8E5C;
    font-size: 24px;
    margin-bottom: 20px;

}
.ins-upload_qr_img{
    width:180px;
    height:180px;
    margin:0 auto ;
    padding:6px;
    border-radius:6px;
    background-color:#ffffff;
}
.ins-upload_tip{
    text-align:left;
    padding:0 26px;
    line-height: 26px;
    font-weight: bold;
    padding-top:20px;
}
.ins-upload_tip p{
    text-indent:1em
}
</style>