<!--
 * <AUTHOR> a大师兄
 * @Date         : 2025-05-16 15:15:29
 * @LastEditors  : a大师兄
 * @LastEditTime : 2025-06-06 06:46:52
 * @Description  : 数字人盒子
-->
<template>
    <div class="human-content">
        <!-- 数智人视频播放器 - 限制最大宽度 -->
        <div
            ref="videoAreaRef"
            class="video-area"
        ></div>

        <!-- 加载界面 -->
        <div
            v-show="isLoading"
            class="ai_loading"
        >
            <van-loading color="#0094ff">
                数智人加载中...
            </van-loading>
        </div>

        <div
            class="glow-effect absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white bg-opacity-30 rounded-full filter blur-xl z-0">
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { TRTC_API } from '@/api/api.js';
import TRTC from 'trtc-sdk-v5';


export default {
    name: 'tencent-digital-human',
    props: {
        navBarShow: Boolean,
        layout: {
            type: String,
            default: 'full', // 'full', 'left', 'left-third'
            validator(value) {
                return ['full', 'left', 'left-third'].includes(value);
            }
        },
        // 腾讯数智人配置
        virtualmanKey: {
            type: String,
            default: ''
        },
        sign: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            messages: [],
            columnHeight: 600,
            connected: false,
            showInsuranceButton: false,
            layoutMode: 'initial',
            videoAreaRef: null,
            trtcClient: null,
            isLoading: false,
            isInitialized: false,
            isConnecting: false,
            audioPermGranted: false,
            digitalHumanRef: null,
            streamSeq: 1,
            sessionId: '',
            streamReqId: '',
            taskId: '',
            roomId: '',
            userId: '',
            robotId: '',
        };
    },
    components: {
    },
    computed: {
        isConnected() {
            return this.layoutMode !== 'initial';
        }
    },
    destroyed() {
        console.log('TencentDigitalHuman组件即将卸载，停止会话');
        this.stopSession();
    },
    mounted() {
        this.digitalHumanRef = this.$refs.videoAreaRef;
        //this.call()
        this.$bus.$on('startLocalAudio', (params) => {
            this.localAudioSwitch(true);
        });
        // 点击 可拖动录音按钮触发 -暂停本地录音采集
        this.$bus.$on('stopLocalAudio', (params) => {
            this.localAudioSwitch(false);
        });
        // 静音模式开关
        //this.$bus.$on('muteRemoteAudio', (boo) => {
        //    //this.muteRemoteAudio(boo);
        //    this.sendCustomMessage();
        //});
        // 发送自定义消息-打断播报
        this.$bus.$on('sendCustomMessage', (boo) => {
            //this.muteRemoteAudio(boo);
            this.sendCustomMessage();
        });
    },
    methods: {
        // 开始本地音频采集 or 暂停本地音频采集
        localAudioSwitch(boo) {
            try {
                if (boo) {
                    console.log('开始本地音频采集');
                    this.trtcClient.startLocalAudio();
                } else {
                    console.log("暂停本地音频采集----")
                    this.trtcClient.stopLocalAudio();
                    if (this.lastMessage && this.lastMessage.endFlag != true) {
                        // 手动暂停采集后发送最后识别的语音
                        console.log("暂停本地音频采集，发送最后语音----")
                        this.submitMessage();
                        //this.$emit('send-message');
                    }
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 发送自定义消息 打断语音
        async sendCustomMessage() {
            // send custom message
            console.log('调用自定义消息11');
            let message = {
                type: 20001,
                sender: this.userId,
                receiver: [this.robotId],
                payload: {
                    id: 'AAAAABBBBBBBCCCCCCC',
                    timestamp: 123
                }
            },
                messageBuffer = new TextEncoder().encode(JSON.stringify(message)).buffer;

            console.log(message, messageBuffer);
            try {
                let res = await this.trtcClient.sendCustomMessage({
                    cmdId: 2,
                    data: messageBuffer
                });

                return res;
            } catch (error) {
                console.log(error);
            }
        },
        async call() {
            this.isLoading = true;
            await this.startHuman();
            this.$emit('call');
            console.log('开始语音对话');
            await this.startConversation();
        },
        // 开始对话
        async handleStartCmdMessage() {
            await this.startConversation();
        },
        // 调用后端API停止AI对话
        async stopAIConversation(data) {
            const response = await axios({
                url: TRTC_API.STOP_CONVERSATION,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: data
            });

            if (!response.data.success) {
                throw new Error(`停止对话失败: ${response.status}`);
            }

            return await response.data;
        },
        submitMessage() {
            if (!this.lastMessage || this.lastMessage.endFlag == true) {
                return;
            }
            console.log('发送语音消息--lastMessage',this.lastMessage)
            let data = this.lastMessage;
            const sender = data.sender,
                currentText = data.payload.text,
                roundId = data.payload.roundid,
                isRobot = sender.includes('ai_'),
                end = data.payload.end; // robot动态展示文字，不显示省略号

            // 处理消息显示
            // msgItem = this.messages.find(item => item.id === roundId && item.sender === sender);

            // this.$emit('send-message', this.messages);

            this.messages = {
                id: roundId,
                content: currentText,
                sender,
                type: isRobot ? 'ai' : 'user',
                end: end
            };
            this.$emit('send-message', this.messages);
            //this.$bus.$emit('isBroadcastFlag', false);

            if (!isRobot) {
                this.lastMessage.endFlag = true;
                this.$bus.$emit('isSpeakFlag');
                this.localAudioSwitch(false)
            }

        },
        // 启动AI对话
        async startConversation() {

            this.isConnecting = true;

            // 获取数字人实例，确保在启动对话前已经获取到数字人实例
            if (!this.digitalHumanRef) {
                this.digitalHumanRef = this.videoAreaRef;
                console.log('[App.vue] 在启动对话前获取数字人实例:', this.digitalHumanRef ? '成功' : '失败');
            }

            try {
                // 创建TRTC客户端
                this.trtcClient = this.trtcClient || TRTC.create();

                console.log('this.trtcClient', this.trtcClient);
                // 配置信息 - 随机生成roomId、userId和robotId
                const generateRandomId = (prefix) => {
                    return `${prefix}_${Math.random().toString(36).substring(2, 10)}`;
                },

                    roomId = Math.floor(100000 + Math.random() * 900000), // 6位随机数
                    userId = generateRandomId('user'),
                    robotId = generateRandomId('ai');

                // 用于存储上一次接收到的文本，用于计算增量
                let lastReceivedText = '';

                // 监听自定义消息
                this.trtcClient.on(TRTC.EVENT.CUSTOM_MESSAGE, (event) => {
                    let jsonData = new TextDecoder().decode(event.data),
                        data = JSON.parse(jsonData);

                    if (data.type === 10000) {
                        const sender = data.sender,
                            currentText = data.payload.text,
                            roundId = data.payload.roundid,
                            isRobot = sender.includes('ai_'),
                            end = data.payload.end; // robot动态展示文字，不显示省略号

                        this.lastMessage = data;
                        // 处理消息显示
                        //msgItem = this.messages.find(item => item.id === roundId && item.sender === sender);

                        if (data.payload.end == true) {
                            // 识别结束，直接发送消息
                            console.log('data.type === 10000',data.payload.end)
                            this.submitMessage();
                        }
                        //this.$emit('send-message', this.messages);

                        //if (msgItem) {
                        //    msgItem.content = currentText;
                        //    msgItem.end = end;
                        //} else {
                        //    this.messages.push({
                        //        id: roundId,
                        //        content: currentText,
                        //        sender,
                        //        type: isRobot ? 'ai' : 'user',
                        //        end: end
                        //    });
                        //    this.$emit('send-message', this.messages);

                        //    // 如果是新消息，重置lastReceivedText
                        //    if (isRobot) {
                        //        lastReceivedText = '';
                        //    }
                        //}

                        // 如果是AI消息且正在说话，启动嘴型动画
                        if (isRobot && !end) {
                            // 计算增量文本
                            let incrementalText = '';

                            if (currentText.startsWith(lastReceivedText)) {
                                incrementalText = currentText.substring(lastReceivedText.length);
                            } else {
                                // 如果当前文本不是上一次文本的延续，则使用整个文本
                                incrementalText = currentText;
                            }

                            // 更新lastReceivedText
                            lastReceivedText = currentText;

                            // 只有当增量文本不为空时才发送
                            if (incrementalText) {
                                console.log('[App.vue] 发送增量文本到数字人:', incrementalText, '(完整文本:', currentText, ')');
                                // 将AI响应的增量文本发送给数字人，驱动嘴型动画
                                this.startMouthAnimation(incrementalText, !end);
                            }
                        }
                        // 如果是AI消息且已结束，停止嘴型动画
                        else if (isRobot && end) {
                            // 重置lastReceivedText
                            lastReceivedText = '';
                            this.stopMouthAnimation();
                        }
                    }
                });
                let type = {
                    baoquan: "5",
                    lipei: "1",
                    lipeiCode: "2",
                    shbServe: "6",
                };
                // 调用后端API启动对话
                let data = {
                    roomId: roomId,
                    userId: userId,
                    robotId: robotId,
                    taskId: JSON.parse(localStorage.getItem("UserInfo")).phone,
                    type: type[this.title]
                },
                    res = await this.startAIConversation(JSON.stringify(data));
                this.roomId = roomId;
                this.userId = userId;
                this.robotId = robotId;

                // 进入房间

                await this.trtcClient.enterRoom({
                    roomId,
                    scene: 'rtc',
                    sdkAppId: res.data.sdkAppId,
                    userId,
                    userSig: res.data.userSig
                });
                // 启动本地音频
                await this.trtcClient.startLocalAudio();
                this.taskId = res.data.TaskId;
                this.sessionId = res.sessionId
                // 更新状态
                this.connected = true;
                this.isConnecting = false;

                // 切换到对话模式
                setTimeout(() => {
                    this.layoutMode = 'conversation';
                    setTimeout(() => {
                        this.showInsuranceButton = true;
                    }, 3000);
                    this.localAudioSwitch(false);
                }, 300);

            } catch (error) {
                console.error('启动对话失败:', error);
                this.stopConversation();
                this.isConnecting = false;
                alert('启动对话失败，请重试');
            }
        },
        // 调用后端API启动AI对话
        async startAIConversation(data) {
            const response = await axios({
                url: TRTC_API.START_CONVERSATION,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: data
            });

            if (!response.data.success) {
                throw new Error(`启动对话失败: ${response.status}`);
            }

            return await response.data;
        },

        // 停止AI对话
        async stopConversation() {
            try {
                console.log('[App.vue] 停止AI对话，当前布局模式:', this.layoutMode);

                // 停止对话
                if (this.taskId) {
                    await this.stopAIConversation(JSON.stringify({
                        taskId: this.taskId
                    }));
                    this.taskId = null;
                }

                // 退出房间
                if (this.trtcClient) {
                    await this.trtcClient.exitRoom();
                    this.trtcClient.destroy();
                    this.trtcClient = null;
                }

                // 停止嘴型动画
                this.stopMouthAnimation();

                // 更新状态
                this.connected = false;
                this.isConnecting = false;

                console.log('[App.vue] 切换回初始布局模式');
                this.layoutMode = 'initial';
                this.showInsuranceButton = false;

                // 清空消息列表
                this.messages = [];
                this.$emit('send-message', this.messages);
                console.log('[App.vue] 停止AI对话完成，当前布局模式:', this.layoutMode);
            } catch (error) {
                console.error('停止对话失败:', error);

                console.log('[App.vue] 出错后强制切换回初始布局模式');
                this.layoutMode = 'initial';
                this.showInsuranceButton = false;
            }
        },
        // 启动嘴型动画
        async startMouthAnimation(text, isStreaming) {
            console.log('[App.vue] Voice playback started. Starting mouth animation.');

            // 如果没有提供文本，则不执行任何操作
            if (!text) {
                console.warn('[App.vue] 没有提供文本，无法启动嘴型动画');
                return;
            }

            // 获取数字人实例
            if (!this.digitalHumanRef && this.videoAreaRef) {
                this.digitalHumanRef = this.videoAreaRef;
                console.log('[App.vue] 获取数字人实例:', this.digitalHumanRef ? '成功' : '失败');
            }

            // 如果是新的对话，或者reqId为空，则生成新的reqId
            if (!this.streamReqId) {
                this.streamReqId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
                this.streamSeq = 1; // 重置序列号
                console.log('[App.vue] 生成新的reqId:', this.streamReqId);
            }

            // 使用数字人实例发送流式文本，驱动嘴型动画
            if (this.digitalHumanRef) {
                try {
                    // 确定是否是最后一个片段
                    const isFinal = !isStreaming;

                    console.log('[App.vue] 发送流式文本到数字人:', text, '序列号:', this.streamSeq, 'reqId:', this.streamReqId, 'isFinal:', isFinal);

                    // 使用sendStreamText方法发送文本，驱动嘴型动画但不发声
                    if (typeof this.sendStreamText === 'function') {
                        // 传递seq、reqId和isFinal
                        await this.sendStreamText(text, this.streamSeq, this.streamReqId, isFinal);

                        // 增加序列号，为下一次调用做准备
                        this.streamSeq++;
                    } else {
                        console.error('[App.vue] 数字人实例没有sendStreamText方法');
                    }
                } catch (error) {
                    console.error('[App.vue] 发送流式文本到数字人失败:', error);

                    // 如果发送失败，重置reqId，下次将使用新的reqId
                    this.streamReqId = '';
                }
            } else {
                console.warn('[App.vue] 数字人实例不可用，无法启动嘴型动画');

                // 尝试通过DOM查找数字人实例
                const digitalHumanElement = document.querySelector('.digital-human-container');

                if (digitalHumanElement && digitalHumanElement.__vueParentComponent.ctx) {
                    console.log('[App.vue] 通过DOM找到数字人实例');
                    this.digitalHumanRef = digitalHumanElement.__vueParentComponent.ctx;

                    // 重试发送文本
                    try {
                        // 确定是否是最后一个片段
                        const isFinal = !isStreaming;

                        if (typeof this.sendStreamText === 'function') {
                            // 传递seq、reqId和isFinal
                            await this.sendStreamText(text, this.streamSeq, this.streamReqId, isFinal);

                            // 增加序列号，为下一次调用做准备
                            this.streamSeq++;
                        } else {
                            console.error('[App.vue] 数字人实例没有sendStreamText方法');
                        }
                    } catch (retryError) {
                        console.error('[App.vue] 重试发送文本失败:', retryError);

                        // 如果发送失败，重置reqId，下次将使用新的reqId
                        this.streamReqId = '';
                    }
                }
            }
        },
        // 获取数字人实例
        getDigitalHumanInstance() {
            if (this.videoAreaRef) {
                console.log('[InsuranceLayout] 返回数字人实例');
                return this.videoAreaRef;
            }
            console.warn('[InsuranceLayout] 数字人实例不可用，尝试重新获取');
            // 尝试通过DOM查找数字人实例
            const digitalHumanElement = document.querySelector('.digital-human-container');

            if (digitalHumanElement && digitalHumanElement.__vueParentComponent.ctx) {
                console.log('[InsuranceLayout] 通过DOM找到数字人实例');
                return digitalHumanElement.__vueParentComponent.ctx;
            }
            console.warn('[InsuranceLayout] 无法获取数字人实例');
            return null;

        },

        // 停止嘴型动画
        async stopMouthAnimation() {
            console.log('[App.vue] Voice playback ended. Stopping mouth animation.');

            // 重置reqId和seq，下次将使用新的reqId和seq
            this.streamReqId = '';
            this.streamSeq = 1;
            console.log('[App.vue] 重置reqId和seq');

            // 获取数字人实例
            if (!this.digitalHumanRef && this.videoAreaRef) {
                this.digitalHumanRef = this.getDigitalHumanInstance();
                console.log('[App.vue] 获取数字人实例:', this.digitalHumanRef ? '成功' : '失败');
            }

            // 使用数字人实例停止当前播放
            if (this.digitalHumanRef) {
                try {
                    console.log('[App.vue] 停止数字人动画');

                    // 检查stopPlaying方法是否存在
                    if (typeof this.stopPlaying === 'function') {
                        // 使用stopPlaying方法停止当前播放
                        await this.stopPlaying();
                    } else {
                        // 如果stopPlaying方法不存在，尝试直接调用IVH.stop
                        console.warn('[App.vue] stopPlaying方法不存在，尝试使用window.IVH.stop');
                        if (window.IVH && typeof window.IVH.stop === 'function') {
                            window.IVH.stop();
                        } else {
                            console.error('[App.vue] 无法停止数字人动画，没有可用的停止方法');
                        }
                    }
                } catch (error) {
                    console.error('[App.vue] 停止数字人动画失败:', error);
                }
            } else {
                console.warn('[App.vue] 数字人实例不可用，无法停止嘴型动画');

                // 尝试通过DOM查找数字人实例
                const digitalHumanElement = document.querySelector('.digital-human-container');

                if (digitalHumanElement && digitalHumanElement.__vueParentComponent.ctx) {
                    console.log('[App.vue] 通过DOM找到数字人实例');
                    this.digitalHumanRef = digitalHumanElement.__vueParentComponent.ctx;

                    // 重试停止播放
                    try {
                        if (typeof this.digitalHumanRef.stopPlaying === 'function') {
                            await this.stopPlaying();
                        } else if (window.IVH && typeof window.IVH.stop === 'function') {
                            window.IVH.stop();
                        }
                    } catch (retryError) {
                        console.error('[App.vue] 重试停止播放失败:', retryError);
                    }
                } else if (window.IVH && typeof window.IVH.stop === 'function') {
                    // 如果无法获取数字人实例，直接尝试使用全局IVH对象
                    console.log('[App.vue] 直接使用window.IVH.stop停止播放');
                    window.IVH.stop();
                }
            }
        },
        async startHuman() {
            this.videoAreaRef = this.$refs.videoAreaRef;
            console.log('TencentDigitalHuman组件已挂载');
            // 如果有配置参数，自动初始化并开始服务
            if (this.virtualmanKey && this.sign) {
                console.log('检测到数智人配置，自动初始化并开始服务');

                // 设置一个超时，确保加载遮罩不会一直显示
                const loadingTimeout = setTimeout(() => {
                    if (this.isLoading) {
                        console.log('加载超时，强制隐藏加载遮罩');
                        this.isLoading = false;
                        this.isInitialized = true;
                    }
                    clearTimeout(loadingTimeout);
                }, 10000); // 10秒后强制隐藏加载遮罩

                try {
                    // 初始化数字人
                    await this.initDigitalHuman();

                    // 监听canplay事件，在数字人准备好后自动开始会话
                    const canplayPromise = new Promise((resolve) => {
                        if (this.isInitialized) {
                            resolve();
                        } else {
                            const canplayHandler = () => {
                                console.log('数智人可以播放了，准备开始会话');
                                resolve();
                            };

                            window.IVH.on('canplay', canplayHandler);

                            // 5秒后如果还没有初始化完成，也继续执行
                            setTimeout(() => {
                                if (!this.isInitialized) {
                                    console.log('5秒后仍未初始化完成，但继续执行');
                                    this.isInitialized = true;
                                    this.isLoading = false;
                                    resolve();
                                }
                            }, 5000);
                        }
                    });

                    // 等待canplay事件或超时
                    await canplayPromise;

                    // 自动开始会话
                    console.log('数智人初始化完成，自动开始会话');
                    await this.startSession();

                    // 通知父组件数字人已初始化并开始服务
                    this.$emit('initialized', { sessionId: this.sessionId });

                } catch (error) {
                    console.error('初始化数智人或开始会话失败:', error);
                    this.isLoading = false;
                }
            }
        },
        // 初始化数智人
        async initDigitalHuman() {
            if (!this.videoAreaRef) { return; }

            try {
                // 检查全局IVH对象是否存在
                if (!window.IVH) {
                    console.error('腾讯数智人SDK未加载');
                    return;
                }

                console.log('开始初始化数智人，参数:', {
                    sign: this.sign,
                    virtualmanProjectId: this.virtualmanKey,
                    element: this.videoAreaRef
                });

                // 确保之前的会话已关闭
                try {
                    if (window.IVH && typeof window.IVH.closeSession === 'function') {
                        await window.IVH.closeSession();
                    }
                } catch (e) {
                    console.warn('关闭之前的会话失败，可能不存在:', e);
                }

                // 禁用全局报告功能，避免不必要的错误
                if (window.Global && typeof window.Global.isCanReport !== 'undefined') {
                    window.Global.isCanReport = false;
                }

                // 初始化SDK
                window.IVH.init({
                    sign: this.sign,
                    virtualmanProjectId: this.virtualmanKey,
                    element: this.videoAreaRef,
                    mute: true, // 设置静音
                    volume: 0 // 设置音量为0
                });

                // 设置事件监听
                window.IVH.on('canplay', async () => {
                    console.log('数智人可以播放了 - canplay事件触发');

                    // 立即设置初始化状态，这样其他地方可以知道数智人已经准备好了
                    this.isInitialized = true;

                    // 确保视频区域可见
                    if (this.videoAreaRef) {
                        const videoElements = this.videoAreaRef.querySelectorAll('video');

                        console.log('找到视频元素数量:', videoElements.length);

                        videoElements.forEach(video => {
                            // 设置视频元素样式
                            video.style.width = '100%';
                            video.style.height = '100%';
                            video.style.objectFit = 'contain';

                            // 确保视频元素静音
                            video.muted = true;
                            video.volume = 0;

                            console.log('设置视频元素样式和静音:', video);
                        });
                    }

                    // 强制隐藏加载遮罩
                    this.isLoading = false;
                    console.log('已设置isLoading为false');

                    // 通知父组件初始化完成
                    this.$emit('initialized', { sessionId: this.sessionId });
                });

                window.IVH.on('socket', (data) => {
                    console.log('数智人socket消息:', data);
                });

                window.IVH.on('error', (error) => {
                    console.error('数智人错误:', error);
                });

                // 建立会话
                console.log('开始创建数智人会话');
                const result = await window.IVH.createSession({
                    userId: this.generateUserId()
                });

                if (result) {
                    this.sessionId = result.sessionId;
                    console.log('数智人会话创建成功:', result);
                } else {
                    console.error('数智人会话创建失败，未返回结果');
                }
            } catch (error) {
                console.error('初始化数智人失败:', error);
            }
        },
        // 开始会话
        async startSession() {
            try {
                console.log('开始数智人会话...');

                // 检查会话是否存在，如果不存在则重新创建
                if (!this.sessionId) {
                    console.log('会话ID不存在，尝试重新创建会话');
                    try {
                        const createResult = await window.IVH.createSession({
                            userId: this.generateUserId()
                        });

                        if (createResult) {
                            this.sessionId = createResult.sessionId;
                            console.log('重新创建会话成功:', createResult);
                        } else {
                            console.error('重新创建会话失败，未返回结果');
                            return false;
                        }
                    } catch (createError) {
                        console.error('重新创建会话失败:', createError);
                        return false;
                    }
                }

                // 开始会话
                let result;

                try {
                    result = await window.IVH.startSession();
                } catch (startError) {
                    // 如果开始会话失败，可能是会话不存在，尝试重新创建会话
                    if (startError && (
                        startError.message?.includes('session不存在') ||
                        startError.code === 110018 ||
                        startError.message?.includes('SessionNotExist')
                    )) {
                        console.log('会话不存在，尝试重新创建会话');
                        try {
                            const createResult = await window.IVH.createSession({
                                userId: this.generateUserId()
                            });

                            if (createResult) {
                                this.sessionId = createResult.sessionId;
                                console.log('重新创建会话成功:', createResult);
                                // 再次尝试开始会话
                                result = await window.IVH.startSession();
                            } else {
                                console.error('重新创建会话失败，未返回结果');
                                return false;
                            }
                        } catch (recreateError) {
                            console.error('重新创建会话失败:', recreateError);
                            return false;
                        }
                    } else {
                        // 其他错误，直接抛出
                        throw startError;
                    }
                }

                if (result) {
                    console.log('数智人会话开始成功');

                    // 确保视频区域可见
                    if (this.videoAreaRef) {
                        this.videoAreaRef.style.display = 'block';

                        // 查找视频元素并确保其样式正确
                        const videoElements = this.videoAreaRef.querySelectorAll('video');

                        if (videoElements.length > 0) {
                            videoElements.forEach(video => {
                                // 设置视频元素样式
                                video.style.width = '100%';
                                video.style.height = '100vh';
                                video.style.objectFit = 'cover'; // 使用cover而不是contain

                                // 确保视频元素静音
                                video.muted = true;
                                video.volume = 0;

                                console.log('会话开始时设置视频元素样式和静音:', video);
                            });
                        } else {
                            console.warn('未找到视频元素');
                        }
                    }

                    // 强制隐藏加载遮罩
                    this.isLoading = false;
                    console.log('会话开始成功，强制隐藏加载遮罩');

                    return true;
                }
                return false;
            } catch (error) {
                console.error('开始数智人会话失败:', error);
                return false;
            }
        },
        // 发送文本
        async sendText(text, textDrive) {
            try {
                await window.IVH.play({
                    command: 'text',
                    data: text,
                    chatCommand: textDrive ? 'NotUseChat' : ''
                });
                return true;
            } catch (error) {
                console.error('发送文本失败:', error);
                return false;
            }
        },
        // 使用流式文本驱动数字人嘴型，但不发声
        async sendStreamText(text, seq, reqIdq, isFinal) {
            let reqId = reqIdq;

            try {
                console.log('发送流式文本到数字人:', text, '序列号:', seq, 'reqId:', reqId, 'isFinal:', isFinal);

                // 确保数字人静音
                if (this.videoAreaRef) {
                    const videoElements = this.videoAreaRef.querySelectorAll('video');

                    videoElements.forEach(video => {
                        // 设置视频元素静音
                        video.muted = true;
                        video.volume = 0;
                        console.log('设置数字人静音');
                    });
                }

                // 检查会话是否存在，如果不存在则重新创建并开始会话
                if (!this.sessionId) {
                    console.log('会话ID不存在，尝试重新创建并开始会话');
                    const sessionStarted = await this.startSession();

                    if (!sessionStarted) {
                        console.error('无法创建或开始会话，无法发送流式文本');
                        return false;
                    }
                }

                // 如果没有提供reqId，则生成一个新的
                if (!reqId) {
                    reqId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
                    console.log('未提供reqId，生成新的请求ID:', reqId);
                }

                try {
                    // 使用stream命令发送文本，驱动嘴型但不发声
                    await window.IVH.play({
                        command: 'stream',
                        data: text,
                        seq: seq,
                        isFinal: isFinal, // 使用传入的isFinal参数
                        smartActionEnabled: true,
                        isSentence: false,
                        isInsertSentence: false,
                        mute: true, // 添加静音参数
                        reqId: reqId // 使用请求ID
                    });
                } catch (streamError) {
                    // 如果发送失败，可能是会话不存在
                    if (streamError && (
                        streamError.message?.includes('session不存在') ||
                        streamError.code === 110018 ||
                        streamError.message?.includes('SessionNotExist')
                    )) {
                        console.log('发送流式文本时会话不存在，尝试重新创建并开始会话');
                        const sessionStarted = await this.startSession();

                        if (!sessionStarted) {
                            throw new Error('无法创建或开始会话，无法继续发送流式文本');
                        }

                        // 重新发送文本
                        await window.IVH.play({
                            command: 'stream',
                            data: text,
                            seq: seq,
                            isFinal: isFinal, // 使用传入的isFinal参数
                            smartActionEnabled: true,
                            isSentence: false,
                            isInsertSentence: false,
                            mute: true,
                            reqId: reqId
                        });
                    } else {
                        // 其他错误，直接抛出
                        throw streamError;
                    }
                }

                return true;
            } catch (error) {
                console.error('发送流式文本失败:', error);
                return false;
            }
        },
        // 停止当前播放
        async stopPlaying() {
            try {
                window.IVH.stop();
                console.log('停止播放成功:');
                return true;
            } catch (error) {
                console.error('停止播放失败:', error);
                return false;
            }
        },
        // 停止会话
        async stopSession() {
            try {
                if (window.IVH) {
                    window.IVH.stop();
                    await window.IVH.closeSession();
                    this.isInitialized = false;
                    this.sessionId = '';
                }
            } catch (error) {
                console.error('停止数智人会话失败:', error);
            }
        },
        // 挂断电话
        async handleCmdMessage() {
            await this.stopConversation();
        },
        // 处理结束通话按钮点击
        handleEndCall() {
            console.log('结束通话按钮被点击');

            // 停止当前播放
            this.stopPlaying();

            // 发送end-call事件给父组件
            this.$emit('end-call');
            this.stopConversation();
        },
        // 生成用户ID
        generateUserId() {
            const storageKey = 'apaas_stream_userId';
            let userId = localStorage.getItem(storageKey);

            if (!userId) {
                userId = window.uuidv4 ? window.uuidv4() : Math.random().toString(36).substring(2, 15);
                localStorage.setItem(storageKey, userId);
            }

            return userId;
        }
    }
};

</script>
<style scoped lang='less'>
.nav {
    position: relative;
    z-index: 12;
}

.human-content {
    position: relative;
    height: 100%;
}

.video-area {
    height: 100%;
}

.call-button {
    margin: 0 auto;
    display: block;
    margin-top: 60%;
    position: absolute;
    top: 60%;
    z-index: 11;
}

.ai_loading {
    position: absolute;
    top: 8px;
    z-index: 14;
}
</style>
