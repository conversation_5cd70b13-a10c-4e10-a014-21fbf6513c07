<template>
  <div class="page">
	<div id="main-window">
		<div class="button-icon">
			<van-image
				:src="require('@/assets/modular/home/<USER>')"
				style="width: 128px;height: 128px;margin-bottom: 20px;"
				@click="goHome"
			/>
			<van-image
				:src="require('@/assets/modular/home/<USER>')"
				style="width: 104px;height:104px;margin-bottom: 20px;"
				@click="stop"
			/>
			<van-image
				:src="require('@/assets/modular/home/<USER>')"
				style="width: 128px;height:128px;"
				@click="yuyin"
			/>
		</div>
		<div class="main-container_ai_human">
			<TencentDigitalHuman
				ref="digitalHumanInLayoutRef"
				:navBarShow="false"
				:isConnected="layoutMode !== 'initial'"
				:isConnecting="isConnecting"
				:layout="layoutMode === 'initial' ? 'full' : (layoutMode === 'conversation' ? 'left' : 'left-third')"
				class="h-full"
				:virtualmanKey="virtualmanKey"
				:sign="sign"
				@call="call"
				@end-call="endCall"
				@initialized="handleDigitalHumanInitialized"
				@send-message="sendMessage"
				title="baoquan"
			/>
		</div>
		<div class="main-container">
			<div class="content" ref="scrollRef">
				<van-pull-refresh v-show="dialogList.length > 0" v-model="refreshLoading" @refresh="onRefresh"
					:disabled="refreshDisable">
					<message-item-view
						ref="msgRef"
						:total="page.total"
						:bot="bot" 
						:loading.sync="loading"
						:dialogs="dialogList"
						title="baoquan"
						@eventStream="eventStreamMsg"
					></message-item-view>
				</van-pull-refresh>
			</div>
			<div class="bottom safe-area-inset-bottom">
				<DialogInput
					ref="inputRef"
					@onSendMsg="sendMsg"
					pageContent="保全服务"
					:loading.sync="loading"
					@onStopAnswer="stopAnswer"
				>
				</DialogInput>
			</div>
		</div>
	</div>
  </div>
</template>

<script>
	import { AI_HUMAN_TYPE } from '@/constant/index.js';
	import DialogInput from "@/components/DialogInput.vue"
	import MessageItemView from "@/components/MessageItemView.vue"
export default {
	name: 'Name',
	components: {
		DialogInput,
		MessageItemView,
	},
	mixins: [],
	props: {
	},
	data() {
		return {
			userInfo:{},
			// 0：正常状态， 1：在获取资料， 2：在回答
			loading: 0,
			// 对话中包含消息
			dialogList: [],
			bot: {
				botId: 16, //智能理赔botid固定为6
			},
			botList: [],
			page: {
				pageNo: 1,
				pageSize: 5,
				total: 0,
			},
			refreshLoading: false,
			showPopover: false,
			audioPermGranted: false,
            isConnecting: false,
			currentCom:'',
			workFlow:{},
			layoutMode: 'initial', // 'initial', 'conversation', 'insurance'
            accessToken: '2388ca9c732742d594545b6246708ad2',
            virtualmanKey: '729e23996897451a8569646371c96095', // 腾讯数智人项目ID - 这是一个示例值，需要替换为实际的值
            sign: 'BqmOZFOdSCgNcssO4OVvI4eXUP1LEhbZ0gXYsXLXK5sbOHDX8eTIoyND0cu3qpQ9MAl1vhCPuLUdMElPgtaIpRitjqxA+kp2lAuA668ziBl3tcKlMSABo/nmAZJFdDE1bhUlynIAXgjpAuVUgjLd8g==', // 腾讯数智人签名 - 这是一个示例值，需要替换为实际的值
			url: {
				chatList: 'talk/queryHistory',
				clearList: 'pc/session/clearList',
				clearLast: 'pc/session/clearLast',
				botList: 'listBots',
				logout: 'sys/logout'
			},
		}
	},
	computed: {
		refreshDisable() {
			return this.dialogList.length >= this.page.total;
		}
	},
	created(){
		console.log('先获取历史记录')
		this.userInfo = JSON.parse(localStorage.getItem("UserInfo")) || null;
		this.getChatList(true);
	},
	// 挂载完成（可以访问DOM元素）
	mounted() {
		
		
	},
  // 方法
  	methods: {
		yuyin(){
			this.$refs.digitalHumanInLayoutRef.call()
		},
		stop(){
			this.$refs.digitalHumanInLayoutRef.handleEndCall()
		},
		eventStreamMsg(msg){
			console.log('eventStreamMsg----', msg);
            //this.$emit('eventStream', msg);
			if (msg.type === 'switch-page') {
                console.log('eventStreamMsg', msg);
                // 中间内容展示的组件
                try {
                    if (!this.containsChinese(msg.workFlow.page)) {
                        this.currentCom = msg.workFlow.page;
                    }

                    this.workFlow = {
                        sessionId:msg.sessionId,
                        ...msg.workFlow
                    };
                    let { param } = msg.workFlow;

                    if (msg.workFlow && param.orderNo) {
                        this.orderNo = param.orderNo;
                    }
                } catch (error) {
                    this.currentCom ='';
                }
            }
        },
		call(data) {
            console.log('数字人call回调:', data);
        },
        endCall(data) {
            console.log('数字人endCall回调:', data);
        },
		 handleDigitalHumanInitialized(data) {
            console.log('数字人初始化完成:', data);
        },
		sendMessage(arr) {
			console.log('ai内容---arr,',arr)
			
            let msgList = [],
                ids = [],
                nArr = arr.map((e,index) => {
                    if (ids.indexOf(e.id) === -1) {
                        ids.push(e.id);
                    }
                    let obj = {
                        question: e.content,
                        questionType: e.type,
                        answer: '',
                        showToolView: true,
                        date: '',
                        messageId: e.id,
                        sessionId: `${e.id}--${index}`
                    };

                    return obj;
                });
				console.log('ai内容---nArr,',nArr)
            ids.forEach(id => {
                let obj = {};
                nArr.forEach(e => {
                    if (e.messageId === id) {
                        if (e.questionType === AI_HUMAN_TYPE.ai) {
                            obj.answer = e.question;
                        }
                        if (e.questionType === AI_HUMAN_TYPE.user) {
                            obj.question = e.question;
                        }
                        Object.assign(e,obj);
                    }
                });
                msgList.push(obj);
            });

            console.log('数字人会话记录---', msgList);
            //this.messages = arr;
            this.dialogList = [{
                sessionId: '222-aadd',
                talkTraceList: msgList
            }];
        },
		goHome(){
			this.$router.push({
				path:'/',
				query:this.$route.query
			})
		},
		stopAnswer() {
			this.$refs.msgRef.stopTalking();
		},
		getChatList(addNew) {
			let p = {
				userId: this.userInfo.phone,
				...this.page,
				...this.bot
			}
			this.$http.postAction(this.url.chatList, p).then(res => {
				this.refreshLoading = false;
				if (res.code == '200') {
					this.page.total = res.data.total;
					this.dialogList.unshift(...this.formatChatListField(res.data.records));
					console.log('历史记录000',this.dialogList)
					if (addNew == true) {
						this.newDialogClick();
					}
				} else {
					this.$toast(res.msg);
				}
			}).catch(res => {
				this.refreshLoading = false;
				if (res && res.msg) {
					this.$toast(res.msg)
				}
			})
		},
		formatChatListField(data) {
			 return data.map(item => {
                let msgList = item.talkTraceList || [],
                    fmtMsgList = msgList.map(msg => {
                        return {
                            ...msg,
                            id: msg.id,
                            question: msg.ask,
                            questionType: msg.type,
                            answer: msg.reply,
                            showToolView: true,
                            date: msg.dt,
                            messageId: msg.messageId,
                            sessionId: msg.sessionId
                        };
                    });

                return {
                    sessionId: item.sessionId,
                    talkTraceList: fmtMsgList
                };
            }).reverse();
		},
		newDialogClick(addNew) {
			this.dialogList.push({
				sessionId: '',
				talkTraceList: []
			});
			this.$refs.inputRef.sendMsg()
			this.$nextTick(() => {
				this.$refs.msgRef.toBottom();
			});
		},
		onRefresh() {
			this.page.pageNo++;

			setTimeout(() => {
				this.getChatList();
			}, 500);
		},
		sendMsg(e) {
			let p = {
				msg: e.data,
				fileArr: e.file || [],
				mode: e.mode[0]
			};
			this.$refs.msgRef.prepareSendMsg(p);
		},
		containsChinese(text) {
            return /[\u4e00-\u9fa5]/.test(text);
        }
	}
  }
</script>

<style lang='less' scoped>
.page{
	background: url('../../assets/modular/home/<USER>');
	background-size: cover;
	width: 100%;
	min-height: 100vh;
	position: relative;
	.button-icon{
		position: absolute;
		right: -20px;
		top: 454px;
		display: flex;
		flex-direction: column;
		z-index: 999;
		align-items: center;
	}
	.main-container_ai_human{
        width: 100%;
        height: calc(100vh - 46px);
    }
	.main-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		/*justify-content: start;*/
		position: absolute;
		width: 100%;
		bottom: 0;
		left: 0;
		height: 60vh;
	}
	.content {
		flex: 1;
		text-align: left;
		padding: 15px;
		padding-bottom: 10px;
		overflow-y: auto;
		width: 100%;
		box-sizing: border-box;

		.top-title {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			height: 80%;
			font-weight: 500;

			.title {
				font-size: 20px;
				color: #000000;
				height: 30px;
				line-height: 30px;
				margin-top: 10px;
				font-weight: 500;
				font-family: 'Microsoft YaHei';
			}
		}
	}
	.bottom {
		background-color: white;
		align-self: flex-end;
		width: 100%;
		position: sticky;
		bottom: 0;
	}
}
</style>
