<template>
  <div class="page">
	<div id="main-window">
		<div class="button-icon">
			<van-image
				:src="require('@/assets/modular/home/<USER>')"
				style="width: 128px;height: 128px;margin-bottom: 20px;"
				@click="goHome"
			/>
			<van-image
				:src="require('@/assets/modular/home/<USER>')"
				style="width: 104px;height:104px;margin-bottom: 20px;"
			/>
			<van-image
				:src="require('@/assets/modular/home/<USER>')"
				style="width: 128px;height:128px;"
			/>
		</div>
		<div class="main-container">
			<div class="content" ref="scrollRef">
				<van-pull-refresh v-show="dialogList.length > 0" v-model="refreshLoading" @refresh="onRefresh"
					:disabled="refreshDisable">
					<message-item-view
						ref="msgRef"
						:total="page.total"
						:bot="bot"
						:loading.sync="loading"
						:dialogs="dialogList"
						title="lipei"
					></message-item-view>
				</van-pull-refresh>
			</div>
			<div class="bottom safe-area-inset-bottom">
				<DialogInput
					ref="inputRef"
					@onSendMsg="sendMsg"
					pageContent="苏惠保2025理赔报案"
					:loading.sync="loading"
					title="lipei"
					@onStopAnswer="stopAnswer"
				>
				</DialogInput>
			</div>
	
		</div>
	</div>
  </div>
</template>

<script>
	import DialogInput from "@/components/DialogInput.vue"
	import MessageItemView from "@/components/MessageItemView.vue"
export default {
	name: 'Name',
	components: {
		DialogInput,
		MessageItemView,
	},
	mixins: [],
	props: {
	},
	data() {
		return {
			userInfo:{},
			// 0：正常状态， 1：在获取资料， 2：在回答
			loading: 0,
			// 对话中包含消息
			dialogList: [],
			bot: {
				botId: 16, //智能理赔botid固定为6
			},
			botList: [],
			page: {
				pageNo: 1,
				pageSize: 5,
				total: 0,
			},
			refreshLoading: false,
			showPopover: false,
			url: {
				chatList: 'talk/queryHistory',
				clearList: 'pc/session/clearList',
				clearLast: 'pc/session/clearLast',
				botList: 'listBots',
				logout: 'sys/logout'
			},
		}
	},
	computed: {
		refreshDisable() {
			return this.dialogList.length >= this.page.total;
		}
	},
	created(){
		console.log('先获取历史记录')
		this.userInfo = JSON.parse(localStorage.getItem("UserInfo")) || null;
		this.getChatList(true);
	},
	// 挂载完成（可以访问DOM元素）
	mounted() {
	},
  // 方法
  	methods: {
		goHome(){
			this.$router.push({
				path:'/',
				query:this.$route.query
			})
		},
		getChatList(addNew) {
			let p = {
				userId: this.userInfo.phone,
				...this.page,
				...this.bot
			}
			this.$http.postAction(this.url.chatList, p).then(res => {
				this.refreshLoading = false;
				if (res.code == '200') {
					this.page.total = res.data.total;
					this.dialogList.unshift(...this.formatChatListField(res.data.records));
					console.log('历史记录000',this.dialogList)
					if (addNew == true) {
						this.newDialogClick();
					}
				} else {
					this.$toast(res.msg);
				}
			}).catch(res => {
				this.refreshLoading = false;
				if (res && res.msg) {
					this.$toast(res.msg)
				}
			})
		},
		formatChatListField(data) {
			if (!data) return [];
			return data.map(item => {
				let msgList = item.talkTraceList || [];
				let fmtMsgList = msgList.map(msg => {
					let workFlow = null;
					if (msg.workFlow) {
						workFlow = JSON.parse(msg.workFlow)
						if (workFlow.option_cards) {
							workFlow.options = workFlow.option_cards.map(item => {
								return {
									title: item,
									value: item
								}
							})
						}
					}
					if(msg.reply){
						if (/^\{.*\}$/.test(msg.reply)) {
							//判断返回的历史记录是否为claim_process（进度查询）
							let reply = JSON.parse(msg.reply);
							if(reply.workFlow && reply.workFlow.type=='claim_process'){
								return {
									...msg,
									id: msg.id,
									question: msg.ask,
									questionType: msg.type,
									answer: '',
									showToolView: true,
									date: msg.dt,
									messageId: msg.messageId,
									sessionId: msg.sessionId,
									workFlow: JSON.parse(msg.reply).workFlow,
								}
							}
						} 
					}
					return {
						...msg,
						id: msg.id,
						question: msg.ask,
						questionType: msg.type,
						answer: msg.reply,
						showToolView: true,
						date: msg.dt,
						messageId: msg.messageId,
						sessionId: msg.sessionId,
						workFlow: workFlow,
					}
				})
				return {
					sessionId: item.sessionId,
					talkTraceList: fmtMsgList
				}
			}).reverse();
		},
		newDialogClick(addNew) {
			console.log('addNew',addNew)
			this.dialogList.push({
				sessionId: '',
				talkTraceList: []
			});
			this.$refs.inputRef.sendMsg()
			this.$nextTick(() => {
				this.$refs.msgRef.toBottom();
			});
		},
		onRefresh() {
			this.page.pageNo++;

			setTimeout(() => {
				this.getChatList();
			}, 500);
		},
		sendMsg(e) {
			let p = {
				msg: e.data,
				fileArr: e.file || [],
				mode: e.mode[0]
			};
			this.$refs.msgRef.prepareSendMsg(p);
		},
		stopAnswer() {
			this.$refs.msgRef.stopTalking();
		},
		clearList() {
			let param = {
				userId: this.userInfo.phone,
			}
			this.$http.getAction(this.url.clearList, param).then(res => {
				if (res.code == 200) {
					this.dialogList = [];
				} else {
					this.$toast(res.msg);
				}
			}).catch(() => {
				this.$toast('网络错误');
			})
		},
	}
  }
</script>
<style lang='less' scoped>
.page{
	background: url('../../assets/modular/home/<USER>');
	background-size: cover;
	width: 100%;
	min-height: 100vh;
	position: relative;
	.button-icon{
		position: absolute;
		right: -20px;
		top: 454px;
		display: flex;
		flex-direction: column;
		z-index: 999;
		align-items: center;
	}
	.main-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		/*justify-content: start;*/
		position: absolute;
		width: 100%;
		bottom: 0;
		left: 0;
		height: 60vh;
	}
	.content {
		flex: 1;
		text-align: left;
		padding: 15px;
		padding-bottom: 10px;
		overflow-y: auto;
		width: 100%;
		box-sizing: border-box;

		.top-title {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			height: 80%;
			font-weight: 500;

			.title {
				font-size: 20px;
				color: #000000;
				height: 30px;
				line-height: 30px;
				margin-top: 10px;
				font-weight: 500;
				font-family: 'Microsoft YaHei';
			}
		}
	}
	.bottom {
		background-color: white;
		align-self: flex-end;
		width: 100%;
		position: sticky;
		bottom: 0;
	}
}
</style>
