<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-06-23 13:54:08
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2025-05-29 09:46:05
 * @Description  : 证件照上传（身份证）
-->
<template>
	<div class="uploader-idcard">
			<div class="reader-popup-content">
				<div class="reader-popup-title" ref="popupTitle">
					请上传身份证
				</div>
			</div>
			<div class="uploader-box">
				<van-row gutter="20">
					<van-col span="1">
						<van-icon name="info" size="1.2rem"/>
					</van-col>
					<van-col span="23">
						<div class="theme-text-color">
							上传身份证后将为您自动填充投保信息。
						</div>
					</van-col>
				</van-row>
				<van-divider />
				<van-row class="uploader-file" gutter="20">
					<van-col span="12" style="position: relative;">
						<van-image class="show_img" width="100%" height="100%" :src="justImgUrl" v-show="justImgUrl!=''"/>
						<van-image class="delete_icon" :src="require('@/assets/service/icon_delete.png')" @click="deleImg('justImg')" v-show="justImgUrl!=''"/>
						<van-uploader
							:before-read="beforeReadF"
							:after-read="afterReadJust"
							:accept="'image/*'"
							v-show="justImgUrl==''"
                            v-model="fileJustList"
						>
							<van-image
								width="100%"
								height="100%"
								:src="require('@/assets/service/idcard_just.png')"
							/>
							<van-image class="img_style" width="24%" :src="require('@/assets/service/camera_img_icon.png')"/>
							<p class="upload_tips">点击拍照/上传</p>
						</van-uploader>
						<p class="upload_name">身份证人像面</p>
					</van-col>
					<van-col span="12" style="position: relative;">
						<van-image class="show_img" width="100%" height="100%" :src="backImgUrl" v-show="backImgUrl!=''"/>
						<van-image class="delete_icon right_icon" :src="require('@/assets/service/icon_delete.png')" @click="deleImg('backImg')" v-show="backImgUrl!=''"/>
						<van-uploader
                            :before-read="beforeReadB"
                            :after-read="afterReadBack"
                            v-model="fileBackList"
                            :accept="'image/*'"
                            v-show="backImgUrl==''"
                        >
							<van-image width="100%" height="100%" :src="require('@/assets/service/idcard_back.png')"/>
							<van-image class="img_style" width="24%" :src="require('@/assets/service/camera_img_icon.png')"/>
							<p class="upload_tips">点击拍照/上传</p>
						</van-uploader>
						<p class="upload_name">身份证国徽面</p>
					</van-col>
				</van-row>
			</div>
			<div class="spacing" v-show="orcResultIsShow"></div>
			<div class="uploader-box" v-if="orcResultIsShow">
				<van-row gutter="20">
					<van-col span="23">
						<p class="theme-text-title">
							证件信息识别结果
						</p>
					</van-col>
				</van-row>
				<van-divider />
				<ul class="show_cont_ul">
					<li>
						<p class="li_name">姓名</p>
						<p class="li_cont">{{name}}</p>
					</li>
					<li>
						<p class="li_name">证件号码</p>
						<p class="li_cont">{{idNum}}</p>
					</li>
					<li>
						<p class="li_name">性别</p>
						<p class="li_cont">{{sex}}</p>
					</li>
					<li>
						<p class="li_name">民族</p>
						<p class="li_cont">{{nation}}</p>
					</li>
					<li>
						<p class="li_name">户籍地址</p>
						<p class="li_cont">{{address}}</p>
					</li>
					<li>
						<p class="li_name">证件有效期</p>
						<p class="li_cont">{{validDate}}</p>
					</li>
				</ul>
				<van-row gutter="20" style="margin-top: 12px;">
					<van-col span="1">
						<van-icon name="info" size="18px"/>
					</van-col>
					<van-col span="23">
						<div class="theme-text-color">
							提示：请核对信息，确认无误后点击确定。
						</div>
					</van-col>
				</van-row>
				<div class="confirm_btn_case">
					<div class="confirm_btn_style" @click="upload">确 定</div>
				</div>
			</div>
			<div class="spacing"></div>
			<div class="uploader-box">
				<van-row gutter="20">
					<van-col span="23">
						<p class="theme-text-title">
							证件照要求
						</p>
					</van-col>
				</van-row>
				<p class="theme-text-p">请持本人有效二代身份证；</p>
				<p class="theme-text-p">拍摄时确保身份证<span class="span_color">边框完整，字体清晰，亮度均匀。</span></p>
				<div class="vague_case">
					<van-image class="vague_img" :src="require('@/assets/service/vague.png')"/>
				</div>

				<!--<van-button
					v-show="closeButtonShow"
					@click="closeButton"
					round
					type="default"
					size="large"
				>关闭</van-button>-->
			</div>
			<!--<van-button :loading="loading" class="margin-t2" type="info" block @click="upload">提   交</van-button>-->
		</div>
</template>

<script>
import { SEX_TYPE } from '@/constant/index.js';
//import { verifIdNum } from '@/api/apiPerson.js';
import Compressor from 'compressorjs';
export default {
    name: 'OcrIdcard',
    components: {
    },
    model: {
        event: 'uploaderList'
    },
    props: {
        customerType:String,
        realNameFlag:Boolean,
        ocrList:{
            type:Array,
            default:()=>[]
        },
        fileSource:{
            type:String,
            default:'1'
        }
    },
    data() {
        return {
            show:false,
            closeButtonShow:true,
            //人像面
            justImgUrl:'',
            fileJustList:[],
            //国徽面
            backImgUrl:'',
            fileBackList:[],
            orderNo:'',
            sessionId:'',
            agentCode:'',
            botId:'',
            process:'',
			taskId:'',
			token:'',
			loading:false,
            //识别结果是否显示
            orcResultIsShow: false,
            ocrArrayList:[{},{}],
            fontJust:{},
            backJust:{},
            ocrApiURL:'/check/newIdCarIndetification',
            saveOcrAttachApiUrl:'/check/saveOcrAttach',
            insertpartyApiUrl:'/doinsure/insertparty',
            url: {
                'lipei': '/intelligent/claim/idAttachUpload',
                'baoquan': '/intelligent/preserve/idAttachUpload',
				'lipeiCode':'/intelligent/claim/idAttachUpload',
				'idNoIdentify':'/intelligent/preserve/idNoIdentify'
            },
        };
    },
    computed:{
        validDate(){
            let data = this.backJust;
            //return`${data && data.validDate.split(':')[0]}至${data && data.validDate.split(':')[1]}`;
            return data.validDate;
        },
        name(){
            return this.fontJust.name;
        },
        idNum(){
            return this.fontJust.idNum;
        },
        sex(){
            return this.fontJust.sex;
        },
        nation(){
            return this.fontJust.nation;
        },
        address(){
            return this.fontJust.address;
        }
    },
    mounted() {
        let { title,sessionId,botId,process,taskId,Token,type} = this.$route.query;
        this.botId = botId;
        this.sessionId = sessionId;
        this.process = process;
        this.title =title
        this.taskId =taskId
        this.type =type
		localStorage.setItem("Token",Token)
		let userInfo = {
			phone:taskId
		}
		localStorage.setItem("UserInfo", JSON.stringify(userInfo));
    },
    methods: {
        closeButton(){
            this.show = false;
        },
        saveOcrApi(params){
            let data = {
                partyType:'1',
                cardSide:params.cardSide,
                orderNo:params.orderNo,
                partyId:params.partyId,
                imgBase64:params.imageBase64.split(';base64,')[1]
            };

            this.$http.postAction(this.saveOcrAttachApiUrl,data).then(res=>{

            }).catch(error=>{
                this.$toast.clear();
                this.$toast.fail({
                    message: error.msg || '影像保存失败',
                    forbidClick: true
                });
                console.log(error);
            });
        },
        upload() {
            if(this.fileJustList.length == 0) {
                this.$toast('请上传身份证人像面信息');
                return;
            }
            if(this.fileBackList.length == 0) {
                this.$toast('请上传身份证国徽面信息');
                return;
            }
			this.$toast.loading({
				duration: 0, 
				message: '正在提交...',
				forbidClick: true,
			});
            let p = new FormData();
            p.append('frontImage',this.fileJustList[0].file)
            p.append('backImage',this.fileBackList[0].file)
            let userInfo = JSON.parse(localStorage.getItem("UserInfo")) || {};
            //p.append('taskId', 'Tbc147286b2e84d7186a468481c549230');
            p.append('taskId', this.taskId);
            p.append('sessionId', this.sessionId);
            p.append('type',this.type || '')
            p.append('process',this.process || '')
            this.loading = true;
            let url =this.url[this.title]
            this.$http.postAction(url, p, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }).then(res=> {
				this.$toast.clear()
                this.$toast(res.msg);
                this.fontJust = {};
                this.backJust = {};
                this.justImgUrl = '';
                this.backImgUrl = '';
                this.fileJustList = [];
				this.fileBackList = [];
				this.orcResultIsShow=false;
            }).catch(res=> {
				this.$toast.clear()
                this.loading = false;
                this.$toast(res.msg ||res.message);
            });
        },
        // 保存身份信息
        
        //压缩人脸
        beforeReadF(file) {
            return new Promise((resolve) => {
                // compressorjs 默认开启 checkOrientation 选项
                // 会将图片修正为正确方向
                new Compressor(file, {
                    success: resolve,
                    quality: 0.2,
                    error(err) {
                        console.log(err.message);
                    }
                });
            });
        },
        //压缩人像
        beforeReadB(file) {
            return new Promise((resolve) => {
                // compressorjs 默认开启 checkOrientation 选项
                // 会将图片修正为正确方向
                new Compressor(file, {
                    success: resolve,
                    quality: 0.2
                });
            });
        },
        /*
		人像照上传
		openId和ocrBusinessNo都是从缓存获取
		*/
        afterReadJust(file){
            this.justImgUrl=file.content;
			let p = new FormData();
			p.append('image',file.file)
			p.append('cardSide','FRONT')
			this.$toast.loading({
                message: '读取中...',
                forbidClick: true
            });
			this.$http.postAction(this.url.idNoIdentify,p).then(res=>{
				console.log('读取人像面信息',res)
				this.fontJust = res.data;
				this.orcResultIsShowFun()
			}).catch(error=>{
                this.$toast.clear();
                this.$toast.fail({
                    message: error.msg || '读取失败',
                    forbidClick: true
                });
                this.justImgUrl = '';
				this.fileJustList = []
                console.log(error);
            });
        },
        //国徽面上传
        afterReadBack(file){
            console.log(file,this.fileBackList);
            this.backImgUrl=file.content;
			let p = new FormData();
			p.append('cardSide','BACK')
			p.append('image',file.file)
			this.$toast.loading({
                message: '读取中...',
                forbidClick: true
            });
			this.$http.postAction(this.url.idNoIdentify,p).then(res=>{
				console.log('读取人像面信息',res)
				this.backJust = res.data;
				this.orcResultIsShowFun()
			}).catch(error=>{
                this.$toast.clear();
                this.$toast.fail({
                    message: error.msg || '读取失败',
                    forbidClick: true
                });
                this.backImgUrl = '';
				this.fileBackList = []
                console.log(error);
            });
        },
		orcResultIsShowFun(){
            console.log('22222');
            if(this.justImgUrl !== '' || this.backImgUrl !== ''){
                //this.$emit('input',this.ocrArrayList);
                this.orcResultIsShow = true;
                this.$toast.clear();
                this.$toast.success('读取成功');
                this.closeButtonShow=false;
            }else{
                this.orcResultIsShow=false;
            }
        },
		deleImg(deleImg){
            if(deleImg == 'justImg'){
                this.justImgUrl='';
                this.ocrArrayList[0]={};
                this.fileJustList = [];
				
            }else if(deleImg == 'backImg'){
                this.backImgUrl='';
                this.ocrArrayList[1]={};
                this.fileBackList = []
            }
        },
      
        showPopup(){
            this.show = true;
        },
        closePopup(){
            this.show = false;
        }
    }
};
</script>

<style lang="less" scoped>
	.reader-popup-content .reader-popup-title {
		padding-top: 0.9375rem;
		padding-bottom: 0.625rem;
		font-size: 1.125rem;
		font-weight: bold;
		margin-left: 1rem;
		margin-right: 1rem;
	}
	.money, .theme-text-color {
		color: #F4682C;
	}

	.forms-item-right {
		justify-content: center;
		align-content: center;
		width: 40px;
		text-align: center;
		border-left: 1px solid @theme-border-color;

	.block {
		display: block;
	}
	.iconfont.active {
		color: #6DA7E0;
	}
}

.uploader-idcard {
	.uploader-box {
		padding: 0 11px;
		margin-top: 4px;

		.theme-text-title {
			font-size: 16px;
			margin: 0 0;
			padding: 8px 0 4px 0;
			color: #333333;
		}
		.van-uploader{
			max-height:110px;
		}
		.theme-text-p {
			font-size: 13px;
			margin: 8px 0 0 0;
			padding: 0 0;
			position: relative;

			.span_color {
				margin-left: 6px;
				color: #DA6E34;
			}
		}

		.vague_case {
			-webkit-box-sizing: border-box;
			box-sizing: border-box;
			width: 100%;
			margin: 30px 0;
			padding: 0 0;
			position: relative;

			.vague_img {
				width: 100%;
			}
		}
	}

	.show_cont_ul {
		width: 100%;
		margin: 0 0;
		padding: 0 0;
		position: relative;

		li {
			width: 100%;
			margin: 0 0;
			padding: 6px 0;
			display: -webkit-flex;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-wrap: wrap;
			position: relative;

			.li_name {
				width: 70px;
				font-size: 14px;
				line-height: 22px;
				text-align: left;
				color: #333333;
				margin: 0 0;
				position: 0 0;
				opacity: 0.6;
				position: relative;
			}

			.li_cont {
				flex: 1;
				font-size: 14px;
				line-height: 22px;
				text-align: left;
				color: #333333;
				margin: 0 0 0 12px;
				position: 0 0;
				position: relative;
			}
		}
	}

	.confirm_btn_case {
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		width: 100%;
		height: 44px;
		margin: 30px 0;
		padding: 0 16px;
		position: relative;

		.confirm_btn_style {
			width: 100%;
			height: 44px;
			margin: 0 0;
			padding: 0 0;
			-moz-border-radius: 22px !important;
			-webkit-border-radius: 22px !important;
			border-radius: 22px !important;
			background: linear-gradient(90deg, #F0562A, #FB8730);
			font-size: 16px;
			line-height: 44px;
			text-align: center;
			color: #FFFFFF;
			position: relative;
		}
	}
	
	.van-uploader__preview-delete {
		position: absolute;
		top: -5px;
		right: -5px;
		width: 22px;
		height: 22px;
		text-align: center;
		background-color: #f51f10;
		border-radius: 16px;
	}

	.van-uploader__preview-delete-icon {
		font-size: 26px;
		font-weight: 800;
	}

	.van-uploader__upload-icon {
		padding: 15px;
		border-radius: 120px;
		opacity: 0.5;
	}
}

.uploader-idcard {
	.van-uploader__preview {
		position: absolute;
		z-index: 9;
		width: 100%;
		height: 100%;
	}

	.van-uploader__preview-image {
		width: 100%;
		height: 100%;
	}

	.uploader-file {
		img {
			width: 100%;
			max-height: 106px;
		}

		.file-title {
			text-align: left;
			padding-bottom: 15px;
			margin-top: 10px;
		}

		.img_style {
			position: absolute;
			top: 18%;
			left: 50%;
			transform: translate(-50%, 0);
			-webkit-transform: translate(-50%, 0);
		}

		.upload_tips {
			position: absolute;
			width: 100%;
			left: 0;
			top: 50%;
			color: #F4682C;
			text-align: center;
			margin-top:16px;
		}

		.upload_name {
			margin: 0 0;
			padding: 8px 0 10px 0;
			text-align:center;
		}

		.show_img {
			-moz-border-radius: 10px !important;
			-webkit-border-radius: 10px !important;
			border-radius: 10px !important;
			overflow: hidden;
		}

		.delete_icon {
			width: 18px;
			height: 18px;
			position: absolute;
			top: 0;
			right: 10px;

			&.right_icon {
				right: 0 !important;
			}
		}
	}

	.footer-text {
		margin-top: 10px;
		margin-left: 15px;
	}
}
.margin-t2 {
	margin-top:50px;
}
</style>
