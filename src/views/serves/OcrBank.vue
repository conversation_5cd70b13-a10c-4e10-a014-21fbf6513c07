<template>
  	<div>
		<div class="title">拍摄时请确保银行卡边框完整、字迹清晰、亮度均衡，识别内容有误时请点击重拍。</div>
		<div class="flex-start margin-t">
			<van-uploader accept=".png, .jpg, .jepg" image-fit="contain" preview-size="250" :max-size="4 * 1024 * 1024" @oversize="onOversize"  style="width: 250px;height: 148px;" v-model="fileList" :max-count="1" :after-read="afterRead">
				<!-- <template #default> -->
					<van-image width="250" height="148" :src="require('@/assets/images/ico-bank-upload.png')"></van-image>
				<!-- </template> -->
			</van-uploader>
			<div>点击拍摄银行卡影像信息</div>
			
			<van-button :loading="loading" class="margin-t2" type="info" block @click="confirm">提   交</van-button>
		</div>
  	</div>
</template>

<script>
export default {
  name: 'Name',
  components: { },
  mixins: [],
  props: {
  },
 data() {
		return {
			fileList: [],
			loading: false,
			url: {
				uploadBankPic: 'intelligent/claim/bankAttachUpload'
			},
			sessionId:'',
			process:'',
			botId:'',
			title:'',
		};
	},
	mounted() {
		let { title,sessionId,botId,process,taskId,Token,type} = this.$route.query;
        this.botId = botId;
        this.sessionId = sessionId;
        this.process = process;
        this.title =title;
		this.taskId =taskId
        this.type =type
		localStorage.setItem("Token",Token)
		let userInfo = {
			phone:taskId
		}
		localStorage.setItem("UserInfo", JSON.stringify(userInfo));
	},
	methods: {
		cancel() {
			this.fileList = [];
			this.loading = false;
		},
		afterRead(e) {
			console.log(e)
		},
		onOversize() {
			this.$toast('图片大小不能超过4MB');
		},
		confirm() {
			if(this.fileList.length == 0) {
				this.$toast('请上传银行卡信息');
				return;
			}
			this.$toast.loading({
				duration: 0, 
				message: '正在提交...',
				forbidClick: true,
			});
			let p = new FormData();
			this.fileList.forEach(item => {
				p.append('image', item.file);
			});
			let userInfo = JSON.parse(localStorage.getItem("UserInfo")) || {};
			p.append('taskId', userInfo.phone);
			p.append('sessionId', this.sessionId);
			p.append('type',this.type || '')
			this.loading = true;
			this.$http.postAction(this.url.uploadBankPic, p, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			}).then(res=> {
				this.$toast.clear()
				this.$toast(res.msg);
				this.cancel();
			}).catch(res=> {
				this.$toast.clear()
				this.loading = false;
				this.$toast(res.msg);
			});
		},
	},
};
</script>

<style lang='less' scoped>
.title {
	color: #EF895D;
	background-color: #FDF2E9;
	padding: 10px;
	font-size: 14px;
	font-weight: normal;
}

.flex-start {
	/* display: flex; */
	/* flex-direction: column; */
	/* justify-content: center;
	align-items: center; */
	padding: 20px;
	/* flex-wrap: wrap; */
	text-align: center;
}

.margin-t {
	margin-top: 30px;
}

.margin-t2 {
	margin-top:50px;
}

/* /deep/ .van-uploader__preview {
	position: absolute;
	
	.van-uploader__preview-image {
		width: auto;
		max-height: 148px !important;
		object-fit: cover; 
	}
} */
</style>
