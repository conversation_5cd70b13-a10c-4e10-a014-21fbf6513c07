<template>
  <div>
    <div class="page" id="main-window">
      <!-- 测试按钮需要删除 -->
      <!--<div @click="test" style="color: red">测试按钮</div>-->
      <div class="button-icon">
        <van-image
          :src="require('@/assets/modular/home/<USER>')"
          style="width: 128px; height: 128px; margin-bottom: 20px"
          @click="refresh"
        />
        <van-image
          :src="require('@/assets/modular/home/<USER>')"
          style="width: 104px; height: 104px; margin-bottom: 20px"
          @click="stop"
        />
        <van-image
          v-show="!countDownShow"
          :src="require('@/assets/modular/home/<USER>')"
          style="width: 128px; height: 128px"
          @click="yuyin"
        />
        <div
            class="count-down"
            v-show="countDownShow"
            @click="yuyin"
        >
            <div class="block">
                {{ time }}S
            </div>
        </div>
      </div>
      <div class="main-container_ai_human">
        <TencentDigitalHuman
          ref="digitalHumanInLayoutRef"
          :navBarShow="false"
          :isConnected="layoutMode !== 'initial'"
          :isConnecting="isConnecting"
          :layout="
            layoutMode === 'initial'
              ? 'full'
              : layoutMode === 'conversation'
              ? 'left'
              : 'left-third'
          "
          class="h-full"
          :virtualmanKey="virtualmanKey"
          :sign="sign"
          :title="title"
          @call="call"
          @end-call="endCall"
          @initialized="handleDigitalHumanInitialized"
          @send-message="sendMessage"
        />
      </div>
      <div class="tab-box">
        <div class="flex-row">
          <div
            @click="tagChange('shbServe')"
            :class="['tag', title == 'shbServe' ? 'active' : 'unactive']"
          >
            <van-image :src="require('@/assets/modular/home/<USER>')" class="icon" />
            苏惠保服务咨询
          </div>
          <div
            @click="tagChange('lipei')"
            :class="['tag', title == 'lipei' ? 'active' : 'unactive']"
          >
            <van-image :src="require('@/assets/modular/home/<USER>')" class="icon" />
            苏惠保智能快赔
          </div>
          <div
            @click="tagChange('lipeiCode')"
            :class="['tag', title == 'lipeiCode' ? 'active' : 'unactive']"
          >
            <van-image :src="require('@/assets/modular/home/<USER>')" class="icon" />
            苏惠保理赔查询
          </div>
        </div>
        <!--<div class="tip">Hi,我是东吴小智，请选择以上服务</div>-->
      </div>
      <!-- <ProcessDialog
        class="dialog-container"
        v-if="!showProcessDialog"
        :total="totalMoney"
        :claimNo="claimNo"
        :insuName="name"
      ></ProcessDialog> -->
      <ProcessDialog
        class="dialog-container"
        v-if="showProcessDialog"
        :total="totalMoney"
        :claimNo="claimNo"
        :insuName="name"
      ></ProcessDialog>
      <div class="main-container">

        <div class="content" ref="scrollRef">
          <van-pull-refresh
            v-show="!showProcessDialog"
            v-model="refreshLoading"
            @refresh="onRefresh"
            :disabled="refreshDisable"
          >
            <div class="purple" v-if="title=='shbServe'||title=='lipei'">
              Hi,我是东吴小智，欢迎使用苏惠保服务咨询，很高兴为您服务。如果您有关于苏惠保的任何问题或需要帮助，欢迎随时向我提问，我会尽快为您提供解答和帮助。
            </div>
            <message-item-view
              ref="msgRef"
              :total="page.total"
              :bot="bot"
              :loading.sync="loading"
              :dialogs="dialogList"
              :title="title"
              @getInfo="getInfo"
              @eventStream="eventStreamMsg"
            ></message-item-view>
          </van-pull-refresh>
          <div id="msg_end" style="height: 1px; width: 1px"></div>
        </div>
        <div class="bottom safe-area-inset-bottom">
          <DialogInput
            v-if="title"
            ref="inputRef"
            @onSendMsg="sendMsg"
            pageContent="保全服务"
            :loading.sync="loading"
            @onStopAnswer="stopAnswer"
          >
          </DialogInput>
        </div>
      </div>
      <van-button round class="szr" @click="szr">数值人</van-button>
    </div>
  </div>
</template>

<script>
import { AI_HUMAN_TYPE } from "@/constant/index.js";
import MessageItemView from "@/components/MessageItemView.vue";
import DialogInput from "@/components/DialogInput.vue";
import ProcessDialog from "@/components/ProcessDialog.vue";
export default {
  name: "Home",
  components: { MessageItemView, DialogInput, ProcessDialog },
  mixins: [],
  props: {},
  data() {
    return {
      numTime:null,
      dialogList: [],
      page: {
        pageNo: 1,
        pageSize: 5,
        total: 0,
      },
      bot: {
        botId: 16, //智能理赔botid固定为6
      },
      loading: 0,
      currentCom: "",
      showProcessDialog: false,
      workFlow: {},
      refreshLoading: false,
      audioPermGranted: false,
      isConnecting: false,
      layoutMode: "initial", // 'initial', 'conversation', 'insurance'
      accessToken: "2388ca9c732742d594545b6246708ad2",
      virtualmanKey: "fcfed56a8d25433dbcf5de1e4ec36ab9", // 腾讯数智人项目ID - 这是一个示例值，需要替换为实际的值
      sign:
        "049hjmt3L9f67uGNlwmNPQVl5kUPXdEVVLyVh1/OMa4bOHDX8eTIoyND0cu3qpQ9MAl1vhCPuLUdMElPgtaIpRitjqxA+kp2lAuA668ziBl3tcKlMSABo/nmAZJFdDE1bhUlynIAXgjpAuVUgjLd8g==", // 腾讯数智人签名 - 这是一个示例值，需要替换为实际的值
      title: "",
      tag: "",
      isStart:false,
      url: {
        refresh: "/intelligent/preserve/refresh",
      },
      showProcessDialog: false,
      name: "", //姓名
      claimNo: "", //赔案号
      totalMoney: 0,
      countDownShow:false,
      time:1,
    };
  },
  created() {
    //this.refresh()
  },
  // 挂载完成（可以访问DOM元素）
  mounted() {
    this.checkAudioPermission();
    console.log("环境------", process.env.NODE_ENV);
    let taskId = this.$route.query.taskId,
      token = this.$route.query.token;
    if (taskId && token) {
      let userInfo = {
        phone: taskId,
      };
      console.log("query.taskId------", taskId);
      localStorage.setItem("UserInfo", JSON.stringify(userInfo));
      localStorage.setItem("Token", token);
    }
    this.$bus.$on('isSpeakFlag', (boo) => {
      this.countDownPause();
      console.log('isSpeakFlag调用')
    });
  },
  computed: {
    refreshDisable() {
      return this.dialogList.length >= this.page.total;
    },
  },
  beforeDestroy() {
    this.$refs.digitalHumanInLayoutRef.handleEndCall();
  },
  // 方法
  methods: {
    countDownStart() {
          //this.$refs.countDown.start();
          this.numTime = setInterval(() => {
              this.time = this.time + 1;
          },1000);
    },
      countDownPause() {
        //this.$refs.countDown.pause();
        this.time = 1;
        clearInterval(this.numTime);
        this.countDownShow = false;
        // this.countDownReset();
        //this.$bus.$emit('stopLocalAudio');
      },
    //获取info信息
    getInfo(info) {
      console.log(info);

      this.showProcessDialog = info.showProcessDialog;
      this.claimNo = info.claimNo;
      this.name = info.name;
      this.totalMoney = info.totalMoney;
    },
    refresh() {
      this.$refs.digitalHumanInLayoutRef.handleEndCall()
      let UserInfo = JSON.parse(localStorage.getItem("UserInfo")),
        param = {
          taskId: UserInfo.phone,
          token: localStorage.getItem("Token"),
        };
      this.$http.postAction(this.url.refresh, param).then((res) => {
        if (res.code == "200") {
          this.dialogList = [];
          let taskId = res.data.taskId,
            token = res.data.token;
          let userInfo = {
            phone: taskId,
          };
          console.log("res.data.taskId------", res.data);
          localStorage.setItem("UserInfo", JSON.stringify(userInfo));
          localStorage.setItem("Token", token);
          //this.$nextTick(()=>{
          //  this.szr()
          //})
        }
      });
    },
    yuyin() {
      //this.$refs.digitalHumanInLayoutRef.handleStartCmdMessage();
      console.log('.stop');
      this.countDownShow = !this.countDownShow;
      if (this.countDownShow) {
        this.countDownStart();
        this.$bus.$emit('startLocalAudio');
      } else {
        this.countDownPause();
        this.$bus.$emit('stopLocalAudio');
      }
    },
    stop() {
      //this.$refs.digitalHumanInLayoutRef.handleCmdMessage();
      this.$bus.$emit('sendCustomMessage');
    },
    szr(){
      this.title = 'lipei'
      this.$refs.digitalHumanInLayoutRef.call();
    },
    tagChange(tag) {
      this.showProcessDialog = false;
      switch (tag) {
        case "shbServe":
          this.title = "shbServe";
          this.tag = "苏惠保服务咨询";
          break;
        case "lipei":
          this.title = "lipei";
          this.tag = "苏惠保2025理赔报案";
          break;
        case "lipeiCode":
          this.title = "lipeiCode";
          this.tag = "理赔进度查询";
          break;
        default:
          break;
      }
      let e = {
        data: this.tag,
        isTabChange: true,
      };
      this.$nextTick(()=>{
        //this.$refs.digitalHumanInLayoutRef.call();
        //his.$refs.digitalHumanInLayoutRef.handleStartCmdMessage()
        this.sendMsg(e);
      });
    },

    sendMsg(e) {
      let p = {
        msg: e.data,
        fileArr: [],
        mode: [],
        isTabChange: e.isTabChange || false,
      };
      this.$refs.msgRef.prepareSendMsg(p);
    },
    stopAnswer() {
      this.$refs.msgRef.stopTalking();
    },
    // 处理数字人初始化完成事件
    handleDigitalHumanInitialized(data) {
      console.log("数字人初始化完成:", data);
    },
    eventStreamMsg(msg) {
      //this.$emit('eventStream', msg);
      if (msg.type === "switch-page") {
        console.log("eventStreamMsg", msg);
        // 中间内容展示的组件
        try {
          if (!this.containsChinese(msg.workFlow.page)) {
            this.currentCom = msg.workFlow.page;
          }
          this.workFlow = {
            sessionId: msg.sessionId,
            ...msg.workFlow,
          };
          let { param } = msg.workFlow;

          if (msg.workFlow && param.orderNo) {
            this.orderNo = param.orderNo;
          }
        } catch (error) {
          this.currentCom = "";
        }
      }
    },
    sendMessage(e) {
      if (e.type == AI_HUMAN_TYPE.ai && this.dialogList.length != 0) {
        return;
      }
      if (this.dialogList.length == 0) {
        this.dialogList = [{
          sessionId: '',
          talkTraceList: []
        }]
      }

      let obj = {
        question: '',
        questionType: e.type,
        answer: '',
        showToolView: false,
        date: '',
        // messageId: e.id,
        // sessionId: e.id
      };
      if (e.type == AI_HUMAN_TYPE.ai) {
        obj.answer = e.content;
      }
      if (e.type === AI_HUMAN_TYPE.user) {
        obj.question = e.content;
        this.loading = 1;
        // 发送问题
        // let p = {
        //     data: e.content
        // };

        // this.sendMsg(p);
      }
      this.dialogList[this.dialogList.length - 1].talkTraceList.push(obj);
    },
    call(data) {
      console.log("数字人call回调:", data);
    },
    endCall(data) {
      console.log("数字人endCall回调:", data);
    },
    async checkAudioPermission() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        this.audioPermGranted = true;
        stream.getTracks().forEach((track) => track.stop());
      } catch (error) {
        this.audioPermGranted = false;
        alert("请允许访问麦克风权限以便能够与AI助手进行语音交流");
        console.error("麦克风权限获取失败:", error);
      }
    },
    onRefresh() {
      this.page.pageNo++;

      setTimeout(() => {
        this.getChatList();
      }, 500);
    },
    getH5UUId() {
      var originStr = "xxxxxx-xxxxxx-4xxxxx-xxxxxx",
        originChar = "ABCDEFGHIJKLMNOPQRS",
        len = originChar.length;
      let uuid = originStr.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x68;
        return v.toString(16);
      });
      uuid += originChar.charAt(Math.floor(Math.random() * len));
      let taskId = "T" + uuid.replace(/-/g, "") + new Date().getTime();

      let userInfo = {
        phone: taskId,
      };
      console.log("taskId------", taskId);
      localStorage.setItem("UserInfo", JSON.stringify(userInfo));
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background: url("../assets/modular/home/<USER>");
  background-size: cover;
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  position: relative;
  overflow-y: scroll;

  .dialog-container{
    position: absolute;
    top: 772px;
    left: 50px;
    z-index: 1000;
  }

  .button-icon {
    position: absolute;
    right: -20px;
    top: 300px;
    display: flex;
    flex-direction: column;
    z-index: 999;
    align-items: center;
  }
  .main-container_ai_human {
    width: 100%;
    height: 100%;
  }
  .main-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    /*justify-content: start;*/
    position: absolute;
    width: 100%;
    top: 800px;
    left: 0;
    overflow-y: scroll;
    height: 800px;
  }
  .content {
    flex: 1;
    text-align: left;
    padding: 15px 70px 0;
    padding-bottom: 10px;
    overflow-y: auto;
    width: 100%;
    box-sizing: border-box;
  }
  .tab-box {
    position: absolute;
    top: 32%;
    left: 50%;
    transform: translate(-50%);
    width: 90%;
    box-sizing: border-box;
    padding: 0 15px;
  }
  .flex-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  .tag {
    min-width: 228px;
    height: 88px;

    color: #ffffff;
    padding: 0 15px;
    border-radius: 44px;
    display: flex;
    justify-content: center;
    line-height: 88px;
    font-size: 28px;
    box-sizing: border-box;
  }
  .unactive {
    background: linear-gradient(to right, #7bbcf4, #305bef);
  }
  .active {
    background: linear-gradient(to right, #111212, #305bef);
  }
  .icon {
    width: 40px;
    height: 40px;
    margin-top: 24px;
    margin-right: 10px;
  }
  .purple {
    background: #ffffff;
    color: #2f3133;
    opacity: 0.95;
    border-radius: 0 20px 20px 20px;
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
    padding: 10px;
    font-size: 28px !important;
    font-style: normal;
  }
}
.count-down{
    width: 74px;
    height: 74px;
    background-color: #3772FF;
    border-radius: 100%;
    display: flex;
    justify-items: center;
    align-items: center;
    text-align: center;
    justify-content: center;
    margin: 22px;
    box-sizing: border-box;
    .block{
        color: #ffffff;
        font-weight: bold;
        font-size: 20px;
    }
}
.bottom {
  /*background-color: white;*/
  align-self: flex-end;
  width: 100%;
  padding:0 70px;
  box-sizing: border-box;
  position: sticky;
  bottom: 0;
}
.szr{
  background-color: #4a728a;
  border:1px solid #4a728a;
  width: 150px;
  color: #2e81b5;
  position: absolute;
    bottom: 20px;
    left: 20px;
}
</style>
