<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-09-24 14:16:00
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2025-04-28 13:05:21
 * @Description  : 项目总入口
-->
<template>
	<div>
		<div id="main-window">
			<van-nav-bar class="nav">
				<template #left>
					<div class="flex-start">
						<van-image width="32" height="32" :src="require('@/assets/images/ico-logo.png')" />
						<span class="title">智能理赔</span>
					</div>
				</template>
				<template #right>
					<van-popover v-model="showPopover" :offset="[20,15]" placement="bottom-end" trigger="click"
						:actions="actions" @select="onSelect">
						<template #reference>
							<van-icon name="ellipsis" color="#000" />
						</template>
					</van-popover>

				</template>
			</van-nav-bar>

			<div class="main-container">
				<div class="content" ref="scrollRef">
					<div class="top-title" v-show="dialogList.length == 0">
						<van-image width="80" height="80" :src="require('@/assets/images/ico-logo.png')" />
						<div class="title">智能理赔</div>
					</div>
					<van-pull-refresh v-show="dialogList.length > 0" v-model="refreshLoading" @refresh="onRefresh"
						:disabled="refreshDisable">
						<message-item-view ref="msgRef" :total="page.total" :bot="bot" :loading.sync="loading"
							:dialogs="dialogList"></message-item-view>
					</van-pull-refresh>
				</div>

				<!-- 底部 -->
				<div class="bottom safe-area-inset-bottom">
					<DialogInput ref="inputRef" :check-list="botList" :loading.sync="loading" @onBotChange="botChange"
						@onSendMsg="sendMsg" @onStopAnswer="stopAnswer">
					</DialogInput>
				</div>

			</div>
		</div>
		<!-- <PDFModal :showPDFModal="true" /> -->

	</div>
</template>

<script>
	import DialogInput from "@/components/DialogInput.vue"
	import MessageItemView from "@/components/MessageItemView.vue"
	import PDFModal from '@/components/PDFModal'
	import Sign from '@/components/Sign.vue'

	export default {
		name: "App",
		components: {
			DialogInput,
			MessageItemView,
			Sign,
			PDFModal,
		},
		data() {
			return {
				// 0：正常状态， 1：在获取资料， 2：在回答
				loading: 0,
				// 对话中包含消息
				dialogList: [],
				url: {
					chatList: 'talk/queryHistory',
					clearList: 'pc/session/clearList',
					clearLast: 'pc/session/clearLast',
					botList: 'listBots',
					logout: 'sys/logout'
				},
				bot: {
					botId: 6, //智能理赔botid固定为6
				},
				botList: [],
				page: {
					pageNo: 1,
					pageSize: 5,
					total: 0,
				},
				refreshLoading: false,
				showPopover: false,
				actions: [{
					index: 1,
					text: '清除上下文关联'
				}, {
					index: 2,
					text: '清除历史对话记录'
				}, {
					index: 3,
					text: '退出登录'
				}],
			};
		},
		watch: {},
		computed: {
			refreshDisable() {
				return this.dialogList.length >= this.page.total;
			}
		},
		created() {

		},
		mounted() {
			this.userInfo = JSON.parse(localStorage.getItem("UserInfo")) || null;
			this.getChatList(true);
		},
		methods: {
			getBot() {
				let param = {
					userId: this.userInfo.phone,
					system: 'dw'
				}
				this.$http.getAction(this.url.botList, param).then(res => {
					if (res.code == 200 && res.data && res.data.length > 0) {

						let arr = res.data.filter(item => {
							return item.botId == 6;
						})
						if (arr.length > 0) {
							this.bot = arr[0];
						}
						this.getChatList(true);

						this.$nextTick(() => {
							this.$refs.msgRef.toBottom();
						});
					} else {
						this.$toast('未获取到智能体');
					}
				})
			},
			formatChatListField(data) {
				if (!data) return [];

				return data.map(item => {
					let msgList = item.talkTraceList || [];
					let fmtMsgList = msgList.map(msg => {
						let workFlow = null;
						if (msg.workFlow) {
							workFlow = JSON.parse(msg.workFlow)
							if (workFlow.option_cards) {
								workFlow.options = workFlow.option_cards.map(item => {
									return {
										title: item,
										value: item
									}
								})
							}
						}
						if(msg.reply){
							if (/^\{.*\}$/.test(msg.reply)) {
								//判断返回的历史记录是否为claim_process（进度查询）
								let reply = JSON.parse(msg.reply);
								if(reply.workFlow && reply.workFlow.type=='claim_process'){
									return {
										...msg,
										id: msg.id,
										question: msg.ask,
										questionType: msg.type,
										answer: '',
										showToolView: true,
										date: msg.dt,
										messageId: msg.messageId,
										sessionId: msg.sessionId,
										workFlow: JSON.parse(msg.reply).workFlow
									}
								}
							} 
						}
						return {
							...msg,
							id: msg.id,
							question: msg.ask,
							questionType: msg.type,
							answer: msg.reply,
							showToolView: true,
							date: msg.dt,
							messageId: msg.messageId,
							sessionId: msg.sessionId,
							workFlow: workFlow
						}
					})
					return {
						sessionId: item.sessionId,
						talkTraceList: fmtMsgList
					}
				}).reverse();
			},
			getChatList(addNew) {
				let p = {
					userId: this.userInfo.phone,
					...this.page,
					...this.bot
				}
				this.$http.postAction(this.url.chatList, p).then(res => {
					this.refreshLoading = false;
					if (res.code == '200') {
						this.page.total = res.data.total;
						this.dialogList.unshift(...this.formatChatListField(res.data.records));
						if (addNew == true) {
							this.newDialogClick();
						}
					} else {
						this.$toast(res.msg);
					}
				}).catch(res => {
					this.refreshLoading = false;
					if (res && res.msg) {
						this.$toast(res.msg)
					}
				})
			},
			botChange(e) {
				this.bot = e.data[0];
			},
			newDialogClick(addNew) {
				this.dialogList.push({
					sessionId: '',
					talkTraceList: []
				});
				
				this.$nextTick(() => {
					this.$refs.msgRef.toBottom();
				});
			},
			/// 清空所有会话
			clearList() {
				let param = {
					userId: this.userInfo.phone,
				}
				this.$http.getAction(this.url.clearList, param).then(res => {
					if (res.code == 200) {
						this.dialogList = [];
					} else {
						this.$toast(res.msg);
					}
				}).catch(() => {
					this.$toast('网络错误');
				})
			},
			stopAnswer() {
				this.$refs.msgRef.stopTalking();
			},
			sendMsg(e) {
				let p = {
					msg: e.data,
					fileArr: e.file || [],
					mode: e.mode[0]
				};
				this.$refs.msgRef.prepareSendMsg(p);
			},
			toMsgBottom() {
				this.$refs.msgRef.toBottom();
			},
			onRefresh() {
				this.page.pageNo++;

				setTimeout(() => {
					this.getChatList();
				}, 500);
			},
			onSelect(action) {
				console.log(action)
				if (action.index == 1) {
					// 清空关联上下文
					if (this.dialogList.length == 0) {
						this.$toast('没有消息记录');
						return;
					}

					let lastD = this.dialogList[this.dialogList.length - 1];
					let param = {
						userId: this.userInfo.phone,
						sessionId: lastD.sessionId
					}
					this.$http.postAction(this.url.clearLast, param).then(res => {
						if (res.code == 200) {
							this.newDialogClick(true);
						} else {
							this.$toast(res.msg);
						}
					}).catch(() => {
						this.$toast('网络错误');
					})

				} else if (action.index == 2) {
					if (this.dialogList.length == 0) {
						this.$toast('没有消息记录');
						return;
					}
					this.$dialog.confirm({
						title: '确定要清空历史对话记录吗？',
						message: '将清空当前会话的所有纪录，并清空上下文关联',
					}).then(() => {
						this.clearList();
					}).catch(() => {

					});
				} else if (action.index == 3) {
					this.$dialog.confirm({
						title: '提示',
						message: '确定要退出吗？',
					}).then(() => {
						this.$http.postAction(this.url.logout, {}).then(res => {
							if (res.code == 200) {
								localStorage.removeItem("Token");
								localStorage.removeItem("UserInfo");
								this.$router.push({
									name: "login"
								})
							} else {
								this.$toast(res.msg);
							}
						}).catch(res => {
							this.$toast('网络错误');
						})

					}).catch(() => {

					});
				}
			}
		}
	};
</script>

<style scoped lang='less'>
	.nav {
		opacity: 0.8 !important;

		.title {
			height: 32px;
			line-height: 32px;
			color: #000;
			font-weight: 500;
		}
	}

	.main-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: start;
		height: calc(100vh - 46px);
	}

	.content {
		flex: 1;
		text-align: left;
		background: #F4F7FC 100%;
		padding: 15px;
		padding-bottom: 10px;
		overflow-y: auto;
		width: 100%;
		box-sizing: border-box;

		.top-title {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			height: 80%;
			font-weight: 500;

			.title {
				font-size: 20px;
				color: #000000;
				height: 30px;
				line-height: 30px;
				margin-top: 10px;
				font-weight: 500;
				font-family: 'Microsoft YaHei';
			}
		}
	}

	.content-normal-height {
		height: calc(100vh - 140px);
	}

	.content-up-height {
		height: calc(100vh - 180px);
	}

	.bottom {
		background-color: white;
		align-self: flex-end;
		width: 100%;
		position: sticky;
		bottom: 0;
	}

	.margin-t {
		margin-top: 10px;
	}

	.flex-start {
		display: flex;
		justify-content: start;
		align-items: center;
	}
</style>