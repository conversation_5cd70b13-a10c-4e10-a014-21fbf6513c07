<template>
	<div>
		<div class="login-page">
			<div class="top-side">
				<div class="flex-center">
					<van-image class="left-image" :src="require('@/assets/images/ico-title-logo.png')"></van-image>
					<span class="left-title">东吴天枢大模型知识引擎</span>
				</div>
			</div>
			<div class="bottom-side">
				<div class="login-title">欢迎登录智能理赔</div>
				<div class="login-subtitle">请登录后继续使用</div>
				<van-form @submit="handleSubmit" style="margin-top: 40px;padding: 00px 20px;">
					<van-field maxlength="11" center v-model="form.phone" type="tel" placeholder="请输入手机号" clearable
						:rules="phoneRule" />
					<van-field maxlength="6" center v-model="form.code" clearable type="number" name="code"
						placeholder="请输入验证码" :rules="[{ required: true, message: '请填写验证码' }]">
						<template #button>
							<van-button type="default" size="small" slot="append" @click="sendVerificationCode"
								:disabled="isCountingDown || !checkPhone(form.phone)">
								{{ isCountingDown ? `${countdown}s 后重新发送` : '发送验证码' }}
							</van-button>
						</template>
					</van-field>
					<van-button :loading="loading" style="margin-top: 30px;" type="info" block
						native-type="submit">登录</van-button>
				</van-form>
			</div>
		</div>
		<van-popup v-model="dialogVisible">
			<MultiVerify style="width: 100%;" :show="dialogVisible" @success="verifySuccess" :showButton="false"
				:type="4"></MultiVerify>
		</van-popup>
	</div>
</template>

<script>
	import {
		isPhone
	} from "@/utils/vaild.js"
	import MultiVerify from '@/components/MultiVerify/MultiVerify.vue'

	export default {
		components: {
			MultiVerify,
		},
		data() {
			return {
				loading: false,
				form: {
					phone: '',
					code: ''
				},
				dialogVisible: false,
				agreeToTerms: false,
				countdown: 60, // 倒计时时长
				isCountingDown: false, // 是否正在倒计时
				phoneRule: [{
					required: true,
					message: (value) => {
						if (value == '') {
							return '请输入手机号'
						} else if (!isPhone(value)) {
							return '请输入正确的手机号'
						} else {
							return ''
						}
					},
					validator: (value) => {
						if (value === '') {
							return false;
						} else {
							if (isPhone(value)) {
								return true;
							} else {
								return false;
							}
						}
					}
				}, ],
				url: {
					sendCode: 'sys/sendVerifyCode',
					login: 'sys/phoneLoginNew',
				},
			};
		},
		mounted() {
			 console.log('登录页--')
			if(this.$route.query.token && this.$route.query.taskId) {
				console.log(this.$route.query)
				localStorage.setItem("Token", this.$route.query.token);
				let userInfo = {
					phone: this.$route.query.taskId
				}
				localStorage.setItem("UserInfo", JSON.stringify(userInfo));
			}
			if(localStorage.getItem("Token") && localStorage.getItem("UserInfo")) {
				this.toChat();
			}
		},
		methods: {
			toChat() {
				this.$router.push({
					name: 'home',
					query: this.$route.query
				})
			},
			checkPhone(text) {
				return isPhone(text);
			},
			handleSubmit() {
				console.log(this.form)
				this.loading = true;

				let param = {
					mobile: this.form.phone,
					verifyCod: this.form.code
				}
				this.$http.postAction(this.url.login, param).then(res => {
					setTimeout(() => {
						this.loading = false;
					}, 2000)

					console.log(res)
					if (res.code == 200) {
						localStorage.setItem("Token", res.result.token);
						localStorage.setItem("UserInfo", JSON.stringify(res.result.userInfo));
						this.toChat();
					} else {
						this.$toast(res.message);
					}
				}).catch(res => {
					this.loading = false;
					this.$toast(res.message);
				})
			},
			sendVerificationCode() {
				// this.dialogVisible = true;

				this.verifySuccess();
			},
			clearCountdown(timer) {
				if(timer) {
					clearInterval(timer);
				}
				this.isCountingDown = false;
				this.countdown = 60; // 重置倒计时
			},
			verifySuccess() {
				this.dialogVisible = false;
				if (this.isCountingDown) return;

				this.isCountingDown = true;
				const timer = setInterval(() => {
					this.countdown--;
					if (this.countdown <= 0) {
						this.clearCountdown(timer);
					}
				}, 1000);

				let param = {
					mobile: this.form.phone
				}
				this.$http.getAction(this.url.sendCode, param).then(res => {
					console.log(res)
					if (res.code == 200) {} else {
						this.clearCountdown();
						this.$toast(res.message);
					}
				}).catch(res => {
					this.clearCountdown();
					this.$toast(res.message);
				})
			}
		}
	};
</script>

<style scoped lang="less">
	.login-page {
		position: relative;
		height: 100vh;
		width: 100%;
	}

	.top-side {
		box-sizing: border-box;
		padding: 20px;
		width: 100%;
		height: 250px;
		background: linear-gradient(135deg, #89f, #def);
		background: url("../../assets/images/login-bg.png") no-repeat right top / cover;

		.left-image {
			width: 30%;
		}
		
		.left-title {
			font-weight: 500;
			font-size: 16px;
			color: #FFFFFF;
			line-height: 36px;
			text-shadow: 0px 1px 4px #3870DD;
			-webkit-appearance: none;
			text-align: right;
			font-style: normal;
		}
	}
	
	

	.bottom-side {
		position: absolute;
		box-sizing: border-box;
		top: 130px;
		padding: 20px;
		text-align: center;
		width: 100%;

		.login-title {
			font-size: 24px;
			font-weight: 500;
			height: 40px;
			line-height: 40px;
		}

		.login-subtitle {
			color: #9A9DAC;
			font-weight: 400;
			font-size: 14px;
			margin-top: 10px;
		}
	}

	.flex-center {
		margin-top: 60px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.flex-start {
		display: flex;
		justify-content: start;
		align-items: center;
	}

	.custom-border {
		border: 1px solid #ebedf0;
		/* 设置边框的颜色 */
		border-radius: 4px;
		/* 可选：设置圆角 */
	}

	.margin-t {
		margin-top: 15px;
	}
</style>