<!--
 * <AUTHOR> a大师兄
 * @Date         : 2021-05-24 10:34:04
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-08-20 17:24:47
 * @Description  : 404页面处理
-->
<template>
	<van-empty
		image="network"
		description="404"
		:caBack="() => { console.log('1111') }"
	>
		<van-button
			round
			type="danger"
			class="bottom-button"
			@click="goHome"
		>返回首页</van-button>
	</van-empty>
</template>

<script>

export default {
	name: 'ErrPage',
	methods: {
		goHome() {
			this.$router.push({
				path: "/",
				query: {},
			});
		}
	}
};
</script>

<style scoped lang="less">
.center {
	font-size: 25px;
}
.bottom-button {
	width: 160px;
	height: 40px;
}
</style>
