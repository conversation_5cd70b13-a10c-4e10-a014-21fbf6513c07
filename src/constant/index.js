/*
 * <AUTHOR> a大师兄
 * @Date         : 2025-03-24 16:17:41
 * @LastEditors  : a大师兄
 * @LastEditTime : 2025-05-26 13:43:44
 * @Description  : 常量定义
 */

// 渠道

const INS_QUESTIONNAIRE_INPUT_TYPE = {
	checkbox: '1',
	radio: '0'
},

	// 数字人消息类型
	AI_HUMAN_TYPE = {
		user: 'user',
		ai: 'ai'
	},
	SEX_TYPE = {
		male: '0',
		female: '1'
	},
	SEX_TYPE_TEXT = {
		male: '男',
		female: '女'
	},
	PROPOSAL_PAGE_NAME = 'InsureProposal';

export {
	INS_QUESTIONNAIRE_INPUT_TYPE,
	AI_HUMAN_TYPE,
	SEX_TYPE,
	SEX_TYPE_TEXT,
	PROPOSAL_PAGE_NAME
};