/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-12-03 15:59:12
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-08-27 14:47:23
 * @Description  : 入口页
 */
// import '@/utils/loadScript.js';
import Vue from 'vue'
import VueClipboard from 'vue-clipboard2'
import App from './App.vue'
import config from '@/env.config.js'
import router from './router/router.index'
import animated from 'animate.css'
import wxJsSdk from '@/utils/wx.js';

import '@/utils/filter.js';
import '@/vantui/vantUi.js'
import '@/utils/resize.js';
import '@/utils/sensors.js';
import '@/utils/dictCode.js'
import videojs from 'video.js';
import "video.js/dist/video-js.min.css";
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import store from '@/store/index.js';
import MComponents from '@/components/index';
// import 'lib-flexible'
Vue.use(VXETable)

import {
	httpAction,
	getAction,
	postAction,
	postActionHeaders,
	getActionHeaders,
} from '@/utils/request.js'

window.videojs = videojs;
Vue.use(VueClipboard)
//环境配置文件
Vue.prototype.$CONFIG = config;
Vue.use(MComponents);

// 字典码值表
Vue.prototype.$dicsCode = window.dictionaries;
//将微信工具类添加到Vue静态方法方便调用
Vue.prototype.$wxJsSdk = wxJsSdk;





//防多次点击，重复提交
Vue.directive('preventReClick', {
    inserted: function (el, binding) {
        el.addEventListener('click', () => {
            if (!el.disabled) {
                el.disabled = true
                setTimeout(() => {
                    el.disabled = false
                }, binding.value || 1000)
            }
        })
    }
});


Vue.prototype.concentScrollTop = function (top) {
	document.body.scrollTop = top;
	document.documentElement.scrollTop = top;
}


Vue.prototype.$objSetWeappOptions = function (obj) {
	let urlp = '';
	let p = obj;
	Object.keys(p).forEach((e, i) => {
		urlp += i === 0 ? `?${e}=${p[e]}` : `&${e}=${p[e]}`
	})
	return urlp;
}



Vue.prototype.$getHref = function () {
	let path = window.location.href;
	if (path.split('?')[1]) {
		let href = path.split('?')[1].split('&');
		console.log('href---', href);
		href.map((item, index) => {
			if (item.split('=')[0] == 'code') {
				href.splice(index, 1);

			}
		})
		let newPath = path.split('?')[0] + '?' + href.join('&')
		console.log('newPath--', newPath);
		return newPath
	}
	
	
};
Vue.prototype.$http = {
	httpAction,
	getAction,
	postAction,
	getActionHeaders,
	postActionHeaders
};

Vue.use(animated)
Vue.config.productionTip = false
// Vue.config.ignoredElements = ['wx-open-launch-weapp']

const bus = new Vue()
Vue.prototype.$bus = bus

if (config.isVConsole) {
	const VConsole = require('vconsole');
	new VConsole({
		target: document.getElementById('vConsole-box')
	});
}

new Vue({
	router,
	store,
	render: h => h(App),
	mounted() {
		//预渲染
		document.dispatchEvent(new Event('render-event'))
	},
}).$mount('#app')