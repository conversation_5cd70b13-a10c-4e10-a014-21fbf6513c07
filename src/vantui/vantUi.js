/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-10-22 11:11:33
 * @LastEditors  : a大师兄
 * @LastEditTime : 2023-06-02 18:48:10
 * @Description  : 
 */
import Vue from 'vue';
// vant本地引用图标资源
import 'vant/lib/icon/local.css';
import {
	NavBar,
	Empty,
	Button,
	Popup,
	Icon,
	Cascader,
	Form,
	NumberKeyboard,
	Col,
	Row,
	Cell,
	CellGroup,
	Card,
	Dialog,
	Notify,
	Lazyload,
	Tab,
	Tabs,
	AddressEdit,
	Field,
	Swipe,
	SwipeItem,
	Tabbar,
	TabbarItem,
	Divider,
	Radio,
	RadioGroup,
	CountDown,
	DatetimePicker,
	Picker,
	Search,
	Uploader,
	List,
	Tag,
	Grid,
	GridItem,
	SubmitBar,
	Checkbox,
	CheckboxGroup,
	Collapse,
	CollapseItem,
	Popover,
	Switch,
	Skeleton,
	NoticeBar,
	IndexBar,
	IndexAnchor,
	Area,
	Image as VanImage,
	PullRefresh,
	Sticky,
	ImagePreview,
	Overlay,
	Toast,
	Loading,
	ActionSheet
} from 'vant';

Vue.use(Lazyload, {
	lazyComponent: true,
});
Vue.use(NavBar);
Vue.use(Loading);
Vue.use(Toast);
Vue.use(Overlay);
Vue.use(ImagePreview);
Vue.use(Sticky);
Vue.use(Area);
Vue.use(IndexBar);
Vue.use(IndexAnchor);
Vue.use(NoticeBar);
Vue.use(Popover);
Vue.use(Skeleton);
Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(Switch);
Vue.use(Picker);
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.use(SubmitBar);
Vue.use(Grid);
Vue.use(GridItem);
Vue.use(Tag);
Vue.use(List);
Vue.use(Uploader);
Vue.use(Search);
Vue.use(DatetimePicker);
Vue.use(Icon);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(CountDown);
Vue.use(Divider);
Vue.use(Tabbar);
Vue.use(TabbarItem);
Vue.use(Empty);
Vue.use(Swipe);
Vue.use(SwipeItem);
Vue.use(Card);
Vue.use(Cell);
Vue.use(CellGroup);
Vue.use(Notify);
Vue.use(Lazyload);
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(Field);
Vue.use(AddressEdit);
Vue.use(Col);
Vue.use(Row);
Vue.use(NumberKeyboard);
Vue.use(Form);
Vue.use(Button);
Vue.use(Cascader);
Vue.use(Popup);
Vue.use(VanImage);
Vue.use(PullRefresh);
Vue.use(Dialog);
Vue.use(ActionSheet);

Toast.setDefaultOptions({ duration: 3000 });
Toast.allowMultiple();