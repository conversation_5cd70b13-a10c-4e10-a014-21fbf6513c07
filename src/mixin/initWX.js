/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-11-16 16:16:41
 * @LastEditors  : a大师兄
 * @LastEditTime : 2022-01-19 09:55:24
 * @Description  :微信渠道相关mixins
 */
export default {
	methods: {
		isWeChatBrowser() {
			var ua = navigator.userAgent.toLowerCase();
			// 判断当前浏览器是否是微信
			return ua.match(/MicroMessenger/i) === "micromessenger";
		}
	},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			console.log("终端", to.meta.client);
			if (vm.isWeChatBrowser()) {
				//微信
				console.log('微信静默授权', from);
				console.log('code: to.query.code', to.query.code)
				let openId = localStorage.getItem('openId');
				if (!openId) {
					// 获取openId
					//const getOpenId = vm.$wxJsSdk.wxGetOpenId({
					//	code: to.query.code
					//});
					//getOpenId.then((res) => {
					//	openId = res
					//})
				}
				console.log(openId);
				//调用微信自定义分享
				//vm.$wxJsSdk.wxShareInit({});
			}
		});
	},
}