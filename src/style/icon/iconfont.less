@font-face {
    font-family: 'iconfont';
    /* Project id 2921595 */
    src: url('./iconfont.woff2?t=1636351057488') format('woff2'),
        url('./iconfont.woff?t=1636351057488') format('woff'),
        url('./iconfont.ttf?t=1636351057488') format('truetype');
}

.iconfont {
    font-family: 'iconfont' !important;
    font-size: 24px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.iconfont-paizhao:before {
  content: "\e622";
}

.iconfont-sanjiaobiao:before {
  content: "\e668";
}

.iconfont.active {
    color: @theme-text-color1;
}

.iconfont-iphone:before {
    content: '\e620';
}

.iconfont-iphone_ma:before {
    content: '\e621';
}

.icon-yinhang-z<PERSON><PERSON>gy<PERSON>hang:before {
    content: '\e600';
}

.icon-zhifu-weixinzhifu:before {
    content: '\e828';
}

.iconfont-zhifu-_zhifubaozhifu:before {
    content: '\e6b3';
}

.icon-banyuan:before {
    content: '\e616';
}

.iconfont-home_1:before {
    content: '\e613';
}

.iconfont-foot02_1:before {
    content: '\e615';
}

.iconfont-home:before {
    content: '\e617';
}

.iconfont-my1:before {
    content: '\e618';
}

.iconfont-foot02_11:before {
    content: '\e61a';
}

.iconfont-home_11:before {
    content: '\e61b';
}

.iconfont-paizhaobeifen:before {
    content: '\e61c';
}

.iconfont-foot03_1:before {
    content: '\e61d';
}

.iconfont-dingwei003:before {
    content: '\e61e';
}

.iconfont-shaixuan:before {
    content: '\e61f';
}