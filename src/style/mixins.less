@font-face {
	font-family: 'HarmonyOS';
	src:url('./font/HarmonyOS_Sans_SC_Regular.ttf') format('truetype');
	font-weight: normal;
	font-style: normal;

	font-family: 'Roboto';
	src:url('./font/Roboto-Regular.ttf') format('truetype');
	font-weight: normal;
	font-style: normal;
}
body{
	background-color: #fff;
	font-size: 14px;
	font-family: HarmonyOS,Roboto !important;
}
.roboto-font{
	font-family: Roboto !important;
}
// 手机端最大宽度
.phone-adaptation {
	max-width: @max-width-phone-adaptation;
	min-width: @min-width-phone-adaptation;
	margin: 0 auto;
}
.app-content {
	max-width: @max-width-phone-adaptation;
	min-width: @min-width-phone-adaptation;
	margin: 0 auto;
	box-sizing: border-box;
	min-height: 100%;
	box-sizing: border-box;
	color: @theme-text-color3;
	background-color: @pages-background-color;
	// overflow: hidden;
	width: 100%;
	overflow: hidden;
	img {
		width: 100%;
	}
	.centent-card {
		border-radius: 0.625rem;
		overflow: hidden;
		background-color: #fff;
	}

	.fixed-centent {
		left: 0;
		right: 0;
	}

	.van-popup,
	.van-tabbar--fixed,
	.van-submit-bar,
	.van-sticky--fixed,
	.footer {
		.phone-adaptation();
		.fixed-centent()
	}
	.van-popup--center{
		left: 50%;
		width: 85%;
	}
}

.color1{
	color: @theme-text-color1;
}
.color12{
	color: @theme-text-color12;
}
.content {
	padding: 0 12px;
	margin: 0 auto;
}
.text-align-r{
	text-align: right;
}
.spacing {
	height: 10px;
	background-color: @spacing-background-color;
}
.theme-notice {
	margin-bottom: 30px;
	background-color: #FFF6EE;
	padding: 10px;
	line-height: 24px;
	margin: 0 12px;
	text-align: left;
	border-radius: 10px;
}
.act-color{
	color: @theme-text-color8;
}
.money,
.theme-text-color {
	color: @theme-text-color1;
}
//文字
.font-xs {
	font-size: @text-size-xs;
}

.font-sm {
	font-size: @text-size-sm;
}

.font-md {
	font-size: @text-size-md;
}

.font-lg {
	font-size: @text-size-lg;
}

.font-blod {
	font-weight: bold;
}
.box-shadow{
	box-shadow: 0px 4px 20px -4px #888888;
}
.title{
	font-weight: bold;
	font-size: @text-size-md;
}
.box-border-radius {
	border-radius: 6px;
}

//文字显示方向
.align-right {
	text-align: right;
}

.align-left {
	text-align: left;
}

.align-center {
	text-align: center;
}
.width100{
	width: 100%;
}
//边距
.margin-top-xs {
	margin-top: 10px;
}



/* 间隔 */
.spacing-5{ margin-top: 5px; }
.spacing-8{ margin-top: 8px; }
.spacing-10{ margin-top: 10px; }
.spacing-12{ margin-top: 12px; }
.spacing-15{ margin-top: 15px; }
.spacing-20{ margin-top: 20px; }
.spacing-40{ margin-top: 40px; }
/* 间隔槽 */
.gap-5{ height: 5px;}
.gap-10{ height: 10px;}
.gap-12{ height: 12px;}
.gap-15{ height: 15px}
.gap-20{ height: 20px;}
.gap-25{ height: 25px;}


//上下padding
.padding-row-20{
	padding-top: 20px;
	padding-bottom: 20px;
}
.padding-row-10{
	padding-top: 10px;
	padding-bottom: 10px;
}
.padding-row-15{
	padding-top: 15px;
	padding-bottom: 15px;
}
.padding-row-5{
	padding-top: 5px;
	padding-bottom: 5px;
}

// 左右padding
.padding-horizontal-5{
	padding-left: 5px;
	padding-right: 5px;
}
.padding-horizontal-10{
	padding-left: 10px;
	padding-right: 10px;
}
.padding-horizontal-15{
	padding-left: 15px;
	padding-right: 15px;
}
.padding-horizontal-20{
	padding-left: 20px;
	padding-right: 20px;
}

.margin-top-sm {
	margin-top: 15px;
}

.margin-top-md {
	margin-top: 20px;
}

.margin-top-lg {
	margin-top: 25px;
}

.product-title{
	font-size: 16px;
	font-weight: bold;
	color: #000;
}
//1px border
.van-after-border {
	position: relative;
}

.van-after-border::after {
	position: absolute;
	box-sizing: border-box;
	content: ' ';
	pointer-events: none;
	right: 1rem;
	bottom: 0;
	left: 1rem;
	border-bottom: 0.0625rem solid #ebedf0;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
}


//vant样式替换

.van-button--info{
	width: 100%;
}

.default-theme{
	.van-icon{
		color: @theme-text-color1;
	}
}

.van-button--primary{
	background: @button-primary-background-color;
	//border-radius: 0 !important
}
.van-button--warning {
	background: @button-warning-background-color;
}

.entry-title {
	font-size: 16px;
	padding: 15px 12px 5px;

	.entry-title-leftIcon {
		font-size: 15px;
		padding: 1px 6px;
		background-color: @theme-background-color3;
		border-radius: 4px;
		color: #fff;
	}
}

.custom-cell{
	.van-cell {
		padding: 0 15px;
		min-height: 45px;
		line-height: 45px;
	}
	.van-field__value {
		overflow: hidden;
	}
}
.no-cell-padding {
	.van-cell {
		padding-left: 0;
		padding-right: 0;
	}
	.van-cell::after {
		position: absolute;
		box-sizing: border-box;
		content: ' ';
		pointer-events: none;
		right: 1 rem;
		bottom: 0;
		left: 0;
		border-bottom: 0rem solid #ebedf0;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}
}
.field-radio.van-cell {
	padding-top: 0;
	padding-bottom: 0;
	min-height: 50px;
	line-height: 50px;
}
.custom-cell-box {
	margin-top: 10px;
	padding: 0 15px;
	margin-bottom: 10px;
	line-height: 30px;
	.label {
		font-size: @font-size-lg;
		font-weight: @theme-text-color5;
		color: @theme-text-color5;
	}

	.desc {
		text-align: right;
		color: @theme-text-color4
	}
}



.custom-popup {
	position: relative;
}
.reader-popup-content {
	height:100%;
	.reader-popup-title {
		padding-top: 15px;
		padding-bottom: 10px;
		font-size: @line-height-sm;
		font-weight: @font-weight-bold;
		margin-left: 1rem;
		margin-right: 1rem;
	}
}
.footer {
	box-sizing: border-box;
	position: fixed;
	padding: 5px 5% 8px;
	bottom: 0;
	left: 0;
	box-shadow: 3px 3px 14px @theme-text-color6;
	border-top: 1px solid @theme-text-color6;
	background-color: #fff;
	z-index: 888;
}

.cell-title{
	color: #000;
	font-size: 14px;
	font-weight: bold;
}

.tabbar-ht {
	height: 50px;
	background-color: transparent;
}
.footer-ht{
	height: 58px;
	background-color: transparent;
}
.divider {
	height: 1px;
	background-color: @theme-text-color6;
}


.custom-title{
	padding: 10px 0;
	.left-icon{
		width: 6px;
		background-color: @custom-title-leftIcon-color;
		display: inline-block;
		height: 20px;
	}
	.mar-right {
		margin-right: 12px;
	}
	.title{
		font-size: 16px;
		font-weight: bold;
		color: #000;
	}
	.details {
		color: @details-color;
		text-align: right;
	}
}

// 按钮

.custom-button {
	width: 100%;
	height: 35px;
	line-height: 35px;
	border-radius: 6px;
	text-align: center;
	margin: 10px auto;
	color: @theme-text-color2;
	border: 1px solid @theme-text-color2;
}

.custom-button-primary {
	background-color: @theme-background-color1;
	border: 1px solid @theme-background-color1;
	color: #fff;
}
.van-field__value{
	font-size: 14px;
}

.van-tabs {
	position:static;
}
.entry-right-contact {
    color: #FAB53A;
}
/**
 * 适配 iphone 底部安全区（指示条，配合index.html <meta name="viewport" ... />使用
 */
@supports (bottom: env(safe-area-inset-bottom)) {
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
}

// 滚动条样式
::-webkit-scrollbar { width: 1px;}

