/*
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @Date         : 2021-11-19 14:40:24
 * @LastEditors  : wangzhenzhen
 * @LastEditTime : 2023-06-15 15:41:21
 * @Description  :订单API
 */
import { postAction, getAction, postActionHeaders, getActionHeaders } from '../utils/request.js'


/**
 *
 * @param {签到详情} parameter
 * @returns
 */
const getCodeImg = (parameter) => getAction(
    '/captchaImage', parameter,
);

/**
 *
 * @param {登录} parameter
 * @returns
 */
const login = (parameter) => postAction(
    `/login`, parameter,
);


/**
 *
 * @param {创建活动} parameter
 * @returns
 */
const addAct = (parameter) => postAction(
    `/act/admin/add`, parameter,
);

/**
 *
 * @param {更新活动} parameter
 * @returns
 */
const updateAct = (parameter) => postAction(
    `/act/admin/update`, parameter,
);

/**
 *
 * @param {活动列表} parameter
 * @returns
 */
const getActList = (parameter, page) => postAction(
    `/act/applet/list?pageNum=${page.pageNum}&pageSize=${page.pageSize}`, parameter,
);

/**
 *
 * @param {生成二维码} parameter
 * @returns
 */
const createQRCode = (parameter) => postAction('/act/applet/creatQrCode', parameter);

/**
 *
 * @param {上传图片} parameter
 * @returns
 */
const uploadPic = (parameter) => postAction('/act/applet/picUpload', parameter, {
      'Content-Type': 'multipart/form-data'
    });

/**
 *
 * @param {签到} parameter
 * @returns
 */
const actSign = (parameter) => postAction('/act/applet/sign', parameter);

/**
 *
 * @param {签到详情} parameter
 * @returns
 */
const getActDetail = (parameter) => getAction(
    '/act/applet/actDetailByCode', parameter,
);

/**
 *
 * @param {获取图片} parameter
 * @returns
 */
const getActImage = (parameter) => postAction(
    '/act/applet/activityInit', parameter,
);

/**
 *
 * @param {上传现场图片} parameter
 * @returns
 */
const uploadActPic = (parameter) => postAction('/act/applet/activityUpload', parameter);

/**
 *
 * @param {获取字典数据} parameter
 * @returns
 */
const getDict = (parameter) => getAction(
    '/act/common/initDict', parameter,
);

/**
 *
 * @param {获取机构部门数据} parameter
 * @returns
 */
const getDeptTreeList = (parameter) => getAction(
    '/act/common/queryTreeList', parameter,
);

export {
	getCodeImg,
	addAct,
	updateAct,
    getActList,
	createQRCode,
	uploadPic,
	actSign,
	getActDetail,
	getDict,
	login,
	getActImage,
	uploadActPic,
	getDeptTreeList
}


