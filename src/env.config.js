/*
 * <AUTHOR> a大师兄
 * @Date         : 2022-10-18 10:18:54
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-09-14 20:00:40
 * @Description  :
 */

/*************************** 全局变量配置文件 ***************************/
const CONFIG = {};

CONFIG.dev = {
    env:"dev",
    isEcback: false,
    isVConsole: false,
    isSensors: false,
    appId: 'wxda27ca9ad6afdfcd',
    szdAppid: "c22616fcd7054788",
    baseURL: 'https://wxcomdat.soochowlife.net:8800/preserve/',
    // baseURL: 'http://***********:18188/',//zhangxiwang
    portalURL: 'https://crmuat.soochowlife.net/semith5/',//h5入口地址
    coreH5URL: 'https://crmtest.soochowlife.net',//核心h5url
    pdfUrl: "http://localhost:3000/pdf",
    ePlicyUrl: 'https://crmuat.soochowlife.net/semith5/',//电子保单pdf地址
    ecBackApiurl:'https://crmuat.soochowlife.net/semit/ecback/h5/pushData',
    mdApiurl:'https://crmuat.soochowlife.net/semit/ecback/h5/md/dataReceive',//埋点数据推送地址
    digitalDW:'http://***********'
}

CONFIG.uat = {
    env: "uat",
    isEcback: false, //可回溯
    isVConsole: false,
    isSensors: false, //埋点
    appId: 'wxda27ca9ad6afdfcd',
    baseURL: 'https://crmuat.soochowlife.net/preserve/', //演示版本
	//baseURL: 'https://crmuat.soochowlife.net/claimTalk/',
    // baseURL: 'http://************:8888/semitUat',
    portalURL: 'http://************:8888/semith5Uat/',//h5入口地址
    coreH5URL: 'https://crmuat.soochowlife.net',//核心h5url
    pdfUrl: "http://crmuat.soochowlife.net/semith5Uat/pdf/",
    ecBackApiurl: 'https://crmuat.soochowlife.net/semitUat/ecback/h5/pushData',
    mdApiurl: 'https://crmuat.soochowlife.net/semitUat/ecback/h5/md/dataReceive',//埋点数据推送地址
    qiyeKeFuUrl: 'https://work.weixin.qq.com/kfid/kfccdfe5a45c735e195',//企业客服链接
    digitalDW:'http://***********'

}

CONFIG.dat = {
    env: "dat",
    isEcback: false,
    isVConsole: false,
    isSensors: false,
    appId: 'wxda27ca9ad6afdfcd',
    szdAppid:"c22616fcd7054788",
	baseURL: 'http://10.1.217.68:18189/claimTalk/',
    //baseURL: 'http://10.1.217.70:3005/talk/',
    portalURL: 'https://crmuat.soochowlife.net/semith5/',//h5入口地址
    coreH5URL: 'https://crmtest.soochowlife.net',//核心h5url
    pdfUrl: "https://crmuat.soochowlife.net/semith5/pdf/",
    ePlicyUrl:'https://crmuat.soochowlife.net/semith5/',//电子保单pdf地址
    ecBackApiurl: 'https://crmuat.soochowlife.net/semit/ecback/h5/pushData',
    mdApiurl: 'https://crmuat.soochowlife.net/semit/ecback/h5/md/dataReceive',//埋点数据推送地址
    qiyeKeFuUrl: 'https://work.weixin.qq.com/kfid/kfccdfe5a45c735e195',
    digitalDW:'http://***********'

}

CONFIG.prd = {
    env: "prd",
    isEcback: false,
    isVConsole: false,
    isSensors: false,//埋点开关
    appId: 'wxda27ca9ad6afdfcd',
    baseURL: 'https://dwai.soochowlife.net:3000/preserve/',
    portalURL: 'https://wx.e-soochowlife.com/semith5/',//h5入口地址
    coreH5URL: 'https://wffcrm.soochowlife.net',//核心h5url
    pdfUrl: "https://wx.e-soochowlife.com/semith5/pdf/",
    ePlicyUrl: 'https://wx.e-soochowlife.com/upload/',//电子保单pdf地址
    ecBackApiurl: 'https://crmuat.soochowlife.net/semit/ecback/h5/pushData',
    mdApiurl: 'https://wx.e-soochowlife.com/semit/mdApi/h5/md/dataReceive',//埋点数据推送地址
    qiyeKeFuUrl:'https://work.weixin.qq.com/kfid/kfccdfe5a45c735e195',
    digitalDW:'http://***********'

}

console.log(`欢迎进入${process.env.VUE_APP_UNIT}环境`);
export default CONFIG[process.env.VUE_APP_UNIT];

