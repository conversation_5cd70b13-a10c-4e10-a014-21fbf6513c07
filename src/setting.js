/*
 * <AUTHOR> a大师兄
 * @Date         : 2023-10-14 18:27:50
 * @LastEditors  : a大师兄
 * @LastEditTime : 2024-08-20 15:22:01
 * @Description  : 更新配色方法
 */
import Vue from 'vue'

let dProductCode = '';
// 不同产品配置不同主题色
function setTheme(to) {
    const { productCode } = to.query;
    if (dProductCode !== productCode && productCode) {
        dProductCode = productCode;
        try {
            let directory = "";
            if (to.meta.productDirectory) {
                directory = to.meta.productDirectory
            } else if (productCode) {
                directory = `DW_${productCode}`
            } else {
                directory = '407A40';
            }
            const theme = require(`@/views/product/views/${directory}/theme.js`)
            console.log('主题色配置>>>>>', theme.default)
            Vue.prototype.$theme = theme.default;

        } catch (error) {
            const theme = require(`@/views/product/theme.js`);
            console.log('默认主题色配置>>>>>', theme.default)
            // 默认主题 
            Vue.prototype.$theme = theme.default;

        }
    }

}

export {
    setTheme
};