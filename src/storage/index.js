import encrypt from './encrypt';

const storagePrefix = process.env.VUE_APP_STORAGE_PREFIX + '_' || '',
    isDevelopment = process.env.VUE_APP_UNIT === 'dev',
    storage = {
    // localStorage 存储
        setLocal(name, value) {
        // 解决undefined会被转成字符串存储问题
            if (!value && value != 0) {
                value = '';
            }
            // 本地调试不加密
            if (isDevelopment) {
                return localStorage.setItem(storagePrefix + name, JSON.stringify(value));
            }
            return localStorage.setItem(storagePrefix + name, encrypt.aesEncrypt(JSON.stringify(value)));
        },
        // localStorage 取值
        getLocal(name) {
            let getItem = localStorage.getItem(storagePrefix + name);

            if (!getItem) {
                return '';
            }
            // 本地调试不加密
            if (isDevelopment) {
                return JSON.parse(getItem);
            }
            return JSON.parse(encrypt.aesDecrypt(getItem));
        },
        // localStorage 移除
        removeLocal(name) {
            localStorage.removeItem(storagePrefix + name);
        },
        // localStorage 清空
        clearLocal() {
            localStorage.clear();
        },

        // sessionStorage 存储
        setSession(name, value) {
        // 解决undefined会被转成字符串存储问题
            if (!value && value != 0) {
                value = '';
            }
            // 本地调试不加密
            if (isDevelopment) {
                return sessionStorage.setItem(storagePrefix + name, JSON.stringify(value));
            }
            return sessionStorage.setItem(storagePrefix + name, encrypt.aesEncrypt(JSON.stringify(value)));
        },
        // sessionStorage 取值
        getSession(name) {
            let getItem = sessionStorage.getItem(storagePrefix + name);

            if (!getItem) {
                return '';
            }
            // 本地调试不加密
            if (isDevelopment) {
                return JSON.parse(getItem);
            }
            return JSON.parse(encrypt.aesDecrypt(getItem));
        },
        // sessionStorage 移除
        removeSession(name) {
            return sessionStorage.removeItem(storagePrefix + name);
        },
        // sessionStorage 清空
        clearSession() {
            return sessionStorage.clear();
        }
    };

export default storage;
