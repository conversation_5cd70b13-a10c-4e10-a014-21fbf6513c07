import JSEncrypt from 'jsencrypt';
import { AES, mode, pad, enc, SHA256, MD5 } from 'crypto-js';

// rsa 加密公钥
const rsaPublicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCdXgsaIKMCYuUO8CjHP32dh68cHboPZma2kBlP1HUTWIYizqVIa2+WwqJF6OgSIUtao4x3KEAqusFk6zjxjbkAslPAkcXG52fYhFmFGObw8SjsyzdRItlt/aU9FUOr0MYxdb9SgYScjDGXfAN4VB7kA+FkblPfhOLoSLqIuh6hcwIDAQAB',
    // rsa 实例
    rsa = new JSEncrypt(),

    // aes 密钥
    aesDefaultKey = 'CSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3hU1Q9vH';

rsa.setPublicKey(rsaPublicKey);

export default {
    // rsa 加密
    rsaEncrypt(str) {
        return rsa.encrypt(str);
    },
    // aes 加密（CBC 模式）
    aesEncrypt(str, aesKey, modeStr = 'CBC') {
        return AES.encrypt(str, aesKey || aesKey === 0 ? aesKey : aesDefaultKey, {
            mode: mode[modeStr],
            padding: pad.Pkcs7
        }).toString();
    },
    // aes 加密（ECB 模式）
    aesEncryptByModeEcb(str, aesKey, modeStr = 'ECB') {
        str = enc.Utf8.parse(str);
        aesKey = enc.Utf8.parse(aesKey);
        return this.aesEncrypt(str, aesKey, modeStr);
    },
    // aes 解密
    aesDecrypt(str, aesKey, modeStr = 'CBC') {
        if (!str) {
            return;
        }
        return AES.decrypt(str, aesKey || aesKey === 0 ? aesKey : aesDefaultKey, {
            mode: mode[modeStr],
            padding: pad.Pkcs7
        }).toString(enc.Utf8);
    },
    // md5 摘要
    md5Encrypt(str) {
        return MD5(str).toString();
    },
    // sha256 摘要
    sha256Encrypt(str) {
        return SHA256(str).toString();
    }
};
