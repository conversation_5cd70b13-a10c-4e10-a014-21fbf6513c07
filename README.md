
[toc]
## 1. 项目概述
**项目是基于vue-cil4脚手架搭建的vue项目基板，UI我们选择了vant 方便更便捷快速开发。
该项目基于vue2.0版本，vant2.0版本。**


## 2. 软件架构
主要是用MVVM架构

## 3. 运行环境/依赖、安装和构建
> 1. **安装依赖**
```
npm install  或  yarn
```


> 2. **构建、热部署本地服务器**
```
npm run serve
```

> 3. **打包 （项目设置了多个环境的打包方式,通过命令区分生产包）**
```
dat  -------- npm run build-dat
uat  -------- npm run build-uat
prd  -------- npm run build
```


## 4. 功能

[使用方式详情见:pdf.js的使用实例](https://www.jianshu.com/p/9cd4840f9323)

## 5. 目录结构
```
env.config/                     # 环境配置文件 --根据自己环境定义配置文件
src/
  api/                      # 项目接口管理
  store/                    # vuex 状态管理
  assets/                   # 资源/图片、音频文件等
  components/               # 页面级别展示组件
  vantui/                   # vant按需引入
  router/                   # 路由，页面注册
  style/                    # css,less文件
  utils/                    # 项目工具目录
    validate                # 校验工具类
    bankCardAttribution.js  # 银行卡号判断所属银行工具类
    storage.js              # 浏览器缓存处理工具类
    js-sdk.js               # 微信分享
    filterPipe.js           # 过滤器
    request.js              # http 请求交互
    util.js                 # 常用工具类
    resize.js               # 不同机型字体大小自适应
  views/                    # 组件视图
    app.vue                 # 模块的跟入口vue视图
  ...
  App.vue                   # 项目入口视图
  main.js                   # 项目入口文件
```


## 6. 提交历史
[时间]:2020-01-05 09:45  
[描述]: 
文件目录调整

----------------------------------------

[时间]:2020-01-05 10:30  
[描述]:  
增加pdf.js   
增加微信分享集成

----------------------------------------

[时间]:2020-01-06 09:48
[描述]:
1. 配置文件修改
----------------------------------------

[时间]:2020-01-08 12:00
[描述]:
1. 添加zip打包方式
2.新增打包名字自定义方法
----------------------------------------


[时间]:2020-01-08 12:00
[描述]:
1. README编写
----------------------------------------


[时间]:2020-05-24 11:18
[描述]:
1. 添加404页面处理
----------------------------------------

## 联系方式

**<EMAIL>**