
/**
 * 通过时间计算出年龄
 * yyyy-mm-dd
 *
 */
(function (win) {
    var Utils = Utils || {};
    Utils.getAge = function (putDate) {
        if (!putDate) {
            return '';
        }
        // 根据出生日期算出年龄*/
        let returnAge;
        let strBirthdayArr = putDate.split('-');
        let birthYear = strBirthdayArr[0];
        let birthMonth = strBirthdayArr[1];
        let birthDay = strBirthdayArr[2];
        let d = new Date();
        let nowYear = d.getFullYear();
        let nowMonth = d.getMonth() + 1;
        let nowDay = d.getDate();
        if (nowYear === +birthYear) {
            returnAge = 0; // 同年 则为0岁
        } else {
            let ageDiff = nowYear - birthYear; // 年之差
            if (ageDiff > 0) {
                if (nowMonth === +birthMonth) {
                    let dayDiff = nowDay - birthDay; // 日之差
                    if (dayDiff < 0) {
                        returnAge = ageDiff - 1;
                    } else {
                        returnAge = ageDiff;
                    }
                } else {
                    let monthDiff = nowMonth - birthMonth; // 月之差
                    if (monthDiff < 0) {
                        returnAge = ageDiff - 1;
                    } else {
                        returnAge = ageDiff;
                    }
                }
            } else {
                returnAge = -1; // 返回-1 表示出生日期输入错误 晚于今天
            }
        }
        return Number(returnAge); // 返回周岁年龄
    };

    Utils.certnoGetInfo = function () {
        var id = arguments[0];
        let certno = id.toUpperCase();
        var org_birthday = certno.substring(6, 12);
        var org_gender = certno.substring(14, 15);
        var reg = /(^\d{15}$)|(^\d{17}([0-9]|X)$)/; //验证身份证号的正则
        if (reg.test(certno)) {
            // 如果身份证号格式正确
            var birthday = '';
            var gender = '';
            var sexText = '';
            var perinfo = {};
            if (certno.length == 15) {
                org_birthday = certno.substring(6, 12);
                org_gender = certno.substring(14, 15);
                birthday =
                    '19' +
                    org_birthday.substring(0, 2) +
                    '-' +
                    org_birthday.substring(2, 4) +
                    '-' +
                    org_birthday.substring(4, 6);
                gender = org_gender % 2 == 1 ? 'male' : 'female';
            } else if (certno.length == 18) {
                org_birthday = certno.substring(6, 14);
                org_gender = certno.substring(16, 17);
                birthday =
                    org_birthday.substring(0, 4) +
                    '-' +
                    org_birthday.substring(4, 6) +
                    '-' +
                    org_birthday.substring(6, 8);
                gender = org_gender % 2 == 1 ? '0' : '1';
                sexText = org_gender % 2 == 1 ? '男' : '女';
            }
            //perinfo.age = getAge(birthday)
            perinfo.birthday = birthday;
            perinfo.sex = gender;
            perinfo.sexText = sexText;
            perinfo.age = this.getAge(birthday);
            return perinfo; // 将生日和性别以对象的形式返回
        } else {
            return false;
        }
    }

    Utils.deepCopy = function (p, c) {

        var d = c || {};

        for (var i in p) {

            if (typeof p[i] === 'object') {

                d[i] = (p[i].constructor === Array) ? [] : {};

                this.deepCopy(p[i], d[i]);

            } else {

                d[i] = p[i];

            }
        }

        return d;
    }
    /**
     * @b64Data:  [解密密钥]
     */
    Utils.unzip = function (b64Data) {
        var strData = atob(b64Data);
        // Convert binary string to character-number array
        var charData = strData.split('').map(function (x) { return x.charCodeAt(0); });
        // Turn number array into byte-array
        var binData = new Uint8Array(charData);
        // // unzip
        var data = window.pako.inflate(binData);
        // Convert gunzipped byteArray back to ascii string:
        strData = String.fromCharCode.apply(null, new Uint16Array(data));
        return decodeURIComponent(strData);
    }
    /**
     * @str: [String] [加密字符串]
     * @test: [String/Number/Boolean] [参数名]
     * @Description: 加密
     * @param {*} str
     */
    Utils.zip = function (str) {
        var binaryString = window.pako.gzip(encodeURIComponent(str), { to: 'string' })
        return btoa(binaryString);
    }
    //暴露给window
    win['Utils'] = Utils;
})(window);
