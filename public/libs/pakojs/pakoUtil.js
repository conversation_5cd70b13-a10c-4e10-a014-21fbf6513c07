(function (win) {
    var pako = win.pako || {};
    /**
     * @b64Data:  [解密密钥]
     */
    pako.unzip = function (b64Data) {
        var strData = atob(b64Data);
        // Convert binary string to character-number array
        var charData = strData.split('').map(function (x) { return x.charCodeAt(0); });
        // Turn number array into byte-array
        var binData = new Uint8Array(charData);
        // // unzip
        var data = window.pako.inflate(binData);
        // Convert gunzipped byteArray back to ascii string:
        strData = String.fromCharCode.apply(null, new Uint16Array(data));
        return decodeURIComponent(strData);
    }
    /**
     * @str: [String] [加密字符串]
     * @test: [String/Number/Boolean] [参数名]
     * @Description: 加密
     * @param {*} str
     */
    pako.zip = function (str) {
        var binaryString = window.pako.gzip(encodeURIComponent(str), { to: 'string' })
        return btoa(binaryString);
    }
    //暴露给window
    win['pako'] = pako;
})(window);
