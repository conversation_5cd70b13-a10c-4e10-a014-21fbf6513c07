!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.videojs=e()}(this,function(){function t(t,e){return e={exports:{}},t(e,e.exports),e.exports}function e(t){return t.replace(/\n\r?\s*/g,"")}function i(t,e){Ue(t).forEach(function(i){return e(t[i],i)})}function r(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return Ue(t).reduce(function(i,r){return e(i,t[r],r)},i)}function n(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return Object.assign?Object.assign.apply(Object,[t].concat(r)):(r.forEach(function(e){e&&i(e,function(e,i){t[i]=e})}),t)}function s(t){return!!t&&"object"===(void 0===t?"undefined":Pe(t))}function a(t){return s(t)&&"[object Object]"===Re.call(t)&&t.constructor===Object}function o(t,e){if(!t||!e)return"";if("function"==typeof ve.getComputedStyle){var i=ve.getComputedStyle(t);return i?i[e]:""}return""}function u(t){return"string"==typeof t&&/\S/.test(t)}function l(t){if(/\s/.test(t))throw new Error("class has illegal whitespace characters")}function c(t){return new RegExp("(^|\\s)"+t+"($|\\s)")}function h(){return ke===ve.document}function d(t){return s(t)&&1===t.nodeType}function p(){try{return ve.parent!==ve.self}catch(t){return!0}}function f(t){return function(e,i){if(!u(e))return ke[t](null);u(i)&&(i=ke.querySelector(i));var r=d(i)?i:ke;return r[t]&&r[t](e)}}function m(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments[3],n=ke.createElement(t);return Object.getOwnPropertyNames(e).forEach(function(t){var i=e[t];-1!==t.indexOf("aria-")||"role"===t||"type"===t?(Le.warn(Oe(Ne,t,i)),n.setAttribute(t,i)):"textContent"===t?g(n,i):n[t]=i}),Object.getOwnPropertyNames(i).forEach(function(t){n.setAttribute(t,i[t])}),r&&R(n,r),n}function g(t,e){return void 0===t.textContent?t.innerText=e:t.textContent=e,t}function y(t,e){e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t)}function v(t,e){return l(e),t.classList?t.classList.contains(e):c(e).test(t.className)}function _(t,e){return t.classList?t.classList.add(e):v(t,e)||(t.className=(t.className+" "+e).trim()),t}function b(t,e){return t.classList?t.classList.remove(e):(l(e),t.className=t.className.split(/\s+/).filter(function(t){return t!==e}).join(" ")),t}function T(t,e,i){var r=v(t,e);if("function"==typeof i&&(i=i(t,e)),"boolean"!=typeof i&&(i=!r),i!==r)return i?_(t,e):b(t,e),t}function S(t,e){Object.getOwnPropertyNames(e).forEach(function(i){var r=e[i];null===r||void 0===r||!1===r?t.removeAttribute(i):t.setAttribute(i,!0===r?"":r)})}function k(t){var e={};if(t&&t.attributes&&t.attributes.length>0)for(var i=t.attributes,r=i.length-1;r>=0;r--){var n=i[r].name,s=i[r].value;"boolean"!=typeof t[n]&&-1===",autoplay,controls,playsinline,loop,muted,default,defaultMuted,".indexOf(","+n+",")||(s=null!==s),e[n]=s}return e}function E(t,e){return t.getAttribute(e)}function C(t,e,i){t.setAttribute(e,i)}function w(t,e){t.removeAttribute(e)}function A(){ke.body.focus(),ke.onselectstart=function(){return!1}}function L(){ke.onselectstart=function(){return!0}}function O(t){if(t&&t.getBoundingClientRect&&t.parentNode){var e=t.getBoundingClientRect(),i={};return["bottom","height","left","right","top","width"].forEach(function(t){void 0!==e[t]&&(i[t]=e[t])}),i.height||(i.height=parseFloat(o(t,"height"))),i.width||(i.width=parseFloat(o(t,"width"))),i}}function P(t){var e=void 0;if(t.getBoundingClientRect&&t.parentNode&&(e=t.getBoundingClientRect()),!e)return{left:0,top:0};var i=ke.documentElement,r=ke.body,n=i.clientLeft||r.clientLeft||0,s=ve.pageXOffset||r.scrollLeft,a=e.left+s-n,o=i.clientTop||r.clientTop||0,u=ve.pageYOffset||r.scrollTop,l=e.top+u-o;return{left:Math.round(a),top:Math.round(l)}}function I(t,e){var i={},r=P(t),n=t.offsetWidth,s=t.offsetHeight,a=r.top,o=r.left,u=e.pageY,l=e.pageX;return e.changedTouches&&(l=e.changedTouches[0].pageX,u=e.changedTouches[0].pageY),i.y=Math.max(0,Math.min(1,(a-u+s)/s)),i.x=Math.max(0,Math.min(1,(l-o)/n)),i}function x(t){return s(t)&&3===t.nodeType}function D(t){for(;t.firstChild;)t.removeChild(t.firstChild);return t}function M(t){return"function"==typeof t&&(t=t()),(Array.isArray(t)?t:[t]).map(function(t){return"function"==typeof t&&(t=t()),d(t)||x(t)?t:"string"==typeof t&&/\S/.test(t)?ke.createTextNode(t):void 0}).filter(function(t){return t})}function R(t,e){return M(e).forEach(function(e){return t.appendChild(e)}),t}function U(t,e){return R(D(t),e)}function N(t){return void 0===t.button&&void 0===t.buttons||(0===t.button&&void 0===t.buttons||0===t.button&&1===t.buttons)}function B(){return He++}function j(t){var e=t[qe];return e||(e=t[qe]=B()),Ve[e]||(Ve[e]={}),Ve[e]}function F(t){var e=t[qe];return!!e&&!!Object.getOwnPropertyNames(Ve[e]).length}function H(t){var e=t[qe];if(e){delete Ve[e];try{delete t[qe]}catch(e){t.removeAttribute?t.removeAttribute(qe):t[qe]=null}}}function V(t,e){var i=j(t);0===i.handlers[e].length&&(delete i.handlers[e],t.removeEventListener?t.removeEventListener(e,i.dispatcher,!1):t.detachEvent&&t.detachEvent("on"+e,i.dispatcher)),Object.getOwnPropertyNames(i.handlers).length<=0&&(delete i.handlers,delete i.dispatcher,delete i.disabled),0===Object.getOwnPropertyNames(i).length&&H(t)}function q(t,e,i,r){i.forEach(function(i){t(e,i,r)})}function W(t){function e(){return!0}function i(){return!1}if(!t||!t.isPropagationStopped){var r=t||ve.event;t={};for(var n in r)"layerX"!==n&&"layerY"!==n&&"keyLocation"!==n&&"webkitMovementX"!==n&&"webkitMovementY"!==n&&("returnValue"===n&&r.preventDefault||(t[n]=r[n]));if(t.target||(t.target=t.srcElement||ke),t.relatedTarget||(t.relatedTarget=t.fromElement===t.target?t.toElement:t.fromElement),t.preventDefault=function(){r.preventDefault&&r.preventDefault(),t.returnValue=!1,r.returnValue=!1,t.defaultPrevented=!0},t.defaultPrevented=!1,t.stopPropagation=function(){r.stopPropagation&&r.stopPropagation(),t.cancelBubble=!0,r.cancelBubble=!0,t.isPropagationStopped=e},t.isPropagationStopped=i,t.stopImmediatePropagation=function(){r.stopImmediatePropagation&&r.stopImmediatePropagation(),t.isImmediatePropagationStopped=e,t.stopPropagation()},t.isImmediatePropagationStopped=i,null!==t.clientX&&void 0!==t.clientX){var s=ke.documentElement,a=ke.body;t.pageX=t.clientX+(s&&s.scrollLeft||a&&a.scrollLeft||0)-(s&&s.clientLeft||a&&a.clientLeft||0),t.pageY=t.clientY+(s&&s.scrollTop||a&&a.scrollTop||0)-(s&&s.clientTop||a&&a.clientTop||0)}t.which=t.charCode||t.keyCode,null!==t.button&&void 0!==t.button&&(t.button=1&t.button?0:4&t.button?1:2&t.button?2:0)}return t}function G(t,e,i){if(Array.isArray(e))return q(G,t,e,i);var r=j(t);if(r.handlers||(r.handlers={}),r.handlers[e]||(r.handlers[e]=[]),i.guid||(i.guid=B()),r.handlers[e].push(i),r.dispatcher||(r.disabled=!1,r.dispatcher=function(e,i){if(!r.disabled){e=W(e);var n=r.handlers[e.type];if(n)for(var s=n.slice(0),a=0,o=s.length;a<o&&!e.isImmediatePropagationStopped();a++)try{s[a].call(t,e,i)}catch(t){Le.error(t)}}}),1===r.handlers[e].length)if(t.addEventListener){var n=!1;We&&Ge.indexOf(e)>-1&&(n={passive:!0}),t.addEventListener(e,r.dispatcher,n)}else t.attachEvent&&t.attachEvent("on"+e,r.dispatcher)}function z(t,e,i){if(F(t)){var r=j(t);if(r.handlers){if(Array.isArray(e))return q(z,t,e,i);var n=function(t,e){r.handlers[e]=[],V(t,e)};if(void 0!==e){var s=r.handlers[e];if(s){if(!i)return void n(t,e);if(i.guid)for(var a=0;a<s.length;a++)s[a].guid===i.guid&&s.splice(a--,1);V(t,e)}}else for(var o in r.handlers)Object.prototype.hasOwnProperty.call(r.handlers||{},o)&&n(t,o)}}}function X(t,e,i){var r=F(t)?j(t):{},n=t.parentNode||t.ownerDocument;if("string"==typeof e?e={type:e,target:t}:e.target||(e.target=t),e=W(e),r.dispatcher&&r.dispatcher.call(t,e,i),n&&!e.isPropagationStopped()&&!0===e.bubbles)X.call(null,n,e,i);else if(!n&&!e.defaultPrevented){var s=j(e.target);e.target[e.type]&&(s.disabled=!0,"function"==typeof e.target[e.type]&&e.target[e.type](),s.disabled=!1)}return!e.defaultPrevented}function Y(t,e,i){if(Array.isArray(e))return q(Y,t,e,i);var r=function r(){z(t,e,r),i.apply(this,arguments)};r.guid=i.guid=i.guid||B(),G(t,e,r)}function $(t,e){e&&(Ye=e),ve.setTimeout($e,t)}function K(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.eventBusKey;if(i){if(!t[i].nodeName)throw new Error('The eventBusKey "'+i+'" does not refer to an element.');t.eventBusEl_=t[i]}else t.eventBusEl_=m("span",{className:"vjs-event-bus"});return n(t,li),t.on("dispose",function(){t.off(),ve.setTimeout(function(){t.eventBusEl_=null},0)}),t}function J(t,e){return n(t,ci),t.state=n({},t.state,e),"function"==typeof t.handleStateChanged&&ii(t)&&t.on("statechanged",t.handleStateChanged),t}function Q(t){return"string"!=typeof t?t:t.charAt(0).toUpperCase()+t.slice(1)}function Z(t,e){return Q(t)===Q(e)}function tt(){for(var t={},e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return r.forEach(function(e){e&&i(e,function(e,i){if(!a(e))return void(t[i]=e);a(t[i])||(t[i]={}),t[i]=tt(t[i],e)})}),t}function et(t,e,i){if("number"!=typeof e||e<0||e>i)throw new Error("Failed to execute '"+t+"' on 'TimeRanges': The index provided ("+e+") is non-numeric or out of bounds (0-"+i+").")}function it(t,e,i,r){return et(t,r,i.length-1),i[r][e]}function rt(t){return void 0===t||0===t.length?{length:0,start:function(){throw new Error("This TimeRanges object is empty")},end:function(){throw new Error("This TimeRanges object is empty")}}:{length:t.length,start:it.bind(null,"start",0,t),end:it.bind(null,"end",1,t)}}function nt(t,e){return Array.isArray(t)?rt(t):void 0===t||void 0===e?rt():rt([[t,e]])}function st(t,e){var i=0,r=void 0,n=void 0;if(!e)return 0;t&&t.length||(t=nt(0,0));for(var s=0;s<t.length;s++)r=t.start(s),n=t.end(s),n>e&&(n=e),i+=n-r;return i/e}function at(t){if(t instanceof at)return t;"number"==typeof t?this.code=t:"string"==typeof t?this.message=t:s(t)&&("number"==typeof t.code&&(this.code=t.code),n(this,t)),this.message||(this.message=at.defaultMessages[this.code]||"")}function ot(t,e){var i,r=null;try{i=JSON.parse(t,e)}catch(t){r=t}return[r,i]}function ut(t){return void 0!==t&&null!==t&&"function"==typeof t.then}function lt(t){ut(t)&&t.then(null,function(t){})}function ct(t){var e=dr.call(t);return"[object Function]"===e||"function"==typeof t&&"[object RegExp]"!==e||"undefined"!=typeof window&&(t===window.setTimeout||t===window.alert||t===window.confirm||t===window.prompt)}function ht(t,e,i){if(!hr(e))throw new TypeError("iterator must be a function");arguments.length<3&&(i=this),"[object Array]"===mr.call(t)?dt(t,e,i):"string"==typeof t?pt(t,e,i):ft(t,e,i)}function dt(t,e,i){for(var r=0,n=t.length;r<n;r++)gr.call(t,r)&&e.call(i,t[r],r,t)}function pt(t,e,i){for(var r=0,n=t.length;r<n;r++)e.call(i,t.charAt(r),r,t)}function ft(t,e,i){for(var r in t)gr.call(t,r)&&e.call(i,t[r],r,t)}function mt(){for(var t={},e=0;e<arguments.length;e++){var i=arguments[e];for(var r in i)br.call(i,r)&&(t[r]=i[r])}return t}function gt(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}function yt(t,e,i){var r=t;return hr(e)?(i=e,"string"==typeof t&&(r={uri:t})):r=_r(e,{uri:t}),r.callback=i,r}function vt(t,e,i){return e=yt(t,e,i),_t(e)}function _t(t){function e(){4===o.readyState&&setTimeout(n,0)}function i(){var t=void 0;if(t=o.response?o.response:o.responseText||bt(o),g)try{t=JSON.parse(t)}catch(t){}return t}function r(t){return clearTimeout(c),t instanceof Error||(t=new Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,a(t,y)}function n(){if(!l){var e;clearTimeout(c),e=t.useXDR&&void 0===o.status?200:1223===o.status?204:o.status;var r=y,n=null;return 0!==e?(r={body:i(),statusCode:e,method:d,headers:{},url:h,rawRequest:o},o.getAllResponseHeaders&&(r.headers=vr(o.getAllResponseHeaders()))):n=new Error("Internal XMLHttpRequest Error"),a(n,r,r.body)}}if(void 0===t.callback)throw new Error("callback argument missing");var s=!1,a=function(e,i,r){s||(s=!0,t.callback(e,i,r))},o=t.xhr||null;o||(o=t.cors||t.useXDR?new vt.XDomainRequest:new vt.XMLHttpRequest);var u,l,c,h=o.url=t.uri||t.url,d=o.method=t.method||"GET",p=t.body||t.data,f=o.headers=t.headers||{},m=!!t.sync,g=!1,y={body:void 0,headers:{},statusCode:0,method:d,url:h,rawRequest:o};if("json"in t&&!1!==t.json&&(g=!0,f.accept||f.Accept||(f.Accept="application/json"),"GET"!==d&&"HEAD"!==d&&(f["content-type"]||f["Content-Type"]||(f["Content-Type"]="application/json"),p=JSON.stringify(!0===t.json?p:t.json))),o.onreadystatechange=e,o.onload=n,o.onerror=r,o.onprogress=function(){},o.onabort=function(){l=!0},o.ontimeout=r,o.open(d,h,!m,t.username,t.password),m||(o.withCredentials=!!t.withCredentials),!m&&t.timeout>0&&(c=setTimeout(function(){if(!l){l=!0,o.abort("timeout");var t=new Error("XMLHttpRequest timeout");t.code="ETIMEDOUT",r(t)}},t.timeout)),o.setRequestHeader)for(u in f)f.hasOwnProperty(u)&&o.setRequestHeader(u,f[u]);else if(t.headers&&!gt(t.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in t&&(o.responseType=t.responseType),"beforeSend"in t&&"function"==typeof t.beforeSend&&t.beforeSend(o),o.send(p||null),o}function bt(t){if("document"===t.responseType)return t.responseXML;var e=t.responseXML&&"parsererror"===t.responseXML.documentElement.nodeName;return""!==t.responseType||e?null:t.responseXML}function Tt(){}function St(t,e){this.name="ParsingError",this.code=t.code,this.message=e||t.message}function kt(t){function e(t,e,i,r){return 3600*(0|t)+60*(0|e)+(0|i)+(0|r)/1e3}var i=t.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return i?i[3]?e(i[1],i[2],i[3].replace(":",""),i[4]):i[1]>59?e(i[1],i[2],0,i[4]):e(0,i[1],i[2],i[4]):null}function Et(){this.values=Dr(null)}function Ct(t,e,i,r){var n=r?t.split(r):[t];for(var s in n)if("string"==typeof n[s]){var a=n[s].split(i);if(2===a.length){var o=a[0],u=a[1];e(o,u)}}}function wt(t,e,i){function r(){var e=kt(t);if(null===e)throw new St(St.Errors.BadTimeStamp,"Malformed timestamp: "+s);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function n(){t=t.replace(/^\s+/,"")}var s=t;if(n(),e.startTime=r(),n(),"--\x3e"!==t.substr(0,3))throw new St(St.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+s);t=t.substr(3),n(),e.endTime=r(),n(),function(t,e){var r=new Et;Ct(t,function(t,e){switch(t){case"region":for(var n=i.length-1;n>=0;n--)if(i[n].id===e){r.set(t,i[n].region);break}break;case"vertical":r.alt(t,e,["rl","lr"]);break;case"line":var s=e.split(","),a=s[0];r.integer(t,a),r.percent(t,a)&&r.set("snapToLines",!1),r.alt(t,a,["auto"]),2===s.length&&r.alt("lineAlign",s[1],["start","middle","end"]);break;case"position":s=e.split(","),r.percent(t,s[0]),2===s.length&&r.alt("positionAlign",s[1],["start","middle","end"]);break;case"size":r.percent(t,e);break;case"align":r.alt(t,e,["start","middle","end","left","right"])}},/:/,/\s/),e.region=r.get("region",null),e.vertical=r.get("vertical",""),e.line=r.get("line","auto"),e.lineAlign=r.get("lineAlign","start"),e.snapToLines=r.get("snapToLines",!0),e.size=r.get("size",100),e.align=r.get("align","middle"),e.position=r.get("position",{start:0,left:0,middle:50,end:100,right:100},e.align),e.positionAlign=r.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},e.align)}(t,e)}function At(t,e){function i(t){return Mr[t]}for(var r,n=t.document.createElement("div"),s=n,a=[];null!==(r=function(){if(!e)return null;var t=e.match(/^([^<]*)(<[^>]*>?)?/);return function(t){return e=e.substr(t.length),t}(t[1]?t[1]:t[2])}());)if("<"!==r[0])s.appendChild(t.document.createTextNode(function(t){for(;l=t.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)t=t.replace(l[0],i);return t}(r)));else{if("/"===r[1]){a.length&&a[a.length-1]===r.substr(2).replace(">","")&&(a.pop(),s=s.parentNode);continue}var o,u=kt(r.substr(1,r.length-2));if(u){o=t.document.createProcessingInstruction("timestamp",u),s.appendChild(o);continue}var l=r.match(/^<([^.\s\/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!l)continue;if(!(o=function(e,i){var r=Rr[e];if(!r)return null;var n=t.document.createElement(r);n.localName=r;var s=Ur[e];return s&&i&&(n[s]=i.trim()),n}(l[1],l[3])))continue;if(!function(t,e){return!Nr[e.localName]||Nr[e.localName]===t.localName}(s,o))continue;l[2]&&(o.className=l[2].substr(1).replace("."," ")),a.push(l[1]),s.appendChild(o),s=o}return n}function Lt(t){for(var e=0;e<Br.length;e++){var i=Br[e];if(t>=i[0]&&t<=i[1])return!0}return!1}function Ot(t){function e(t,e){for(var i=e.childNodes.length-1;i>=0;i--)t.push(e.childNodes[i])}function i(t){if(!t||!t.length)return null;var r=t.pop(),n=r.textContent||r.innerText;if(n){var s=n.match(/^.*(\n|\r)/);return s?(t.length=0,s[0]):n}return"ruby"===r.tagName?i(t):r.childNodes?(e(t,r),i(t)):void 0}var r,n=[],s="";if(!t||!t.childNodes)return"ltr";for(e(n,t);s=i(n);)for(var a=0;a<s.length;a++)if(r=s.charCodeAt(a),Lt(r))return"rtl";return"ltr"}function Pt(t){if("number"==typeof t.line&&(t.snapToLines||t.line>=0&&t.line<=100))return t.line;if(!t.track||!t.track.textTrackList||!t.track.textTrackList.mediaElement)return-1;for(var e=t.track,i=e.textTrackList,r=0,n=0;n<i.length&&i[n]!==e;n++)"showing"===i[n].mode&&r++;return-1*++r}function It(){}function xt(t,e,i){var r=/MSIE\s8\.0/.test(navigator.userAgent),n="rgba(255, 255, 255, 1)",s="rgba(0, 0, 0, 0.8)";r&&(n="rgb(255, 255, 255)",s="rgb(0, 0, 0)"),It.call(this),this.cue=e,this.cueDiv=At(t,e.text);var a={color:n,backgroundColor:s,position:"relative",left:0,right:0,top:0,bottom:0,display:"inline"};r||(a.writingMode=""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",a.unicodeBidi="plaintext"),this.applyStyles(a,this.cueDiv),this.div=t.document.createElement("div"),a={textAlign:"middle"===e.align?"center":e.align,font:i.font,whiteSpace:"pre-line",position:"absolute"},r||(a.direction=Ot(this.cueDiv),a.writingMode=""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl".stylesunicodeBidi="plaintext"),this.applyStyles(a),this.div.appendChild(this.cueDiv);var o=0;switch(e.positionAlign){case"start":o=e.position;break;case"middle":o=e.position-e.size/2;break;case"end":o=e.position-e.size}""===e.vertical?this.applyStyles({left:this.formatStyle(o,"%"),width:this.formatStyle(e.size,"%")}):this.applyStyles({top:this.formatStyle(o,"%"),height:this.formatStyle(e.size,"%")}),this.move=function(t){this.applyStyles({top:this.formatStyle(t.top,"px"),bottom:this.formatStyle(t.bottom,"px"),left:this.formatStyle(t.left,"px"),right:this.formatStyle(t.right,"px"),height:this.formatStyle(t.height,"px"),width:this.formatStyle(t.width,"px")})}}function Dt(t){var e,i,r,n,s=/MSIE\s8\.0/.test(navigator.userAgent);if(t.div){i=t.div.offsetHeight,r=t.div.offsetWidth,n=t.div.offsetTop;var a=(a=t.div.childNodes)&&(a=a[0])&&a.getClientRects&&a.getClientRects();t=t.div.getBoundingClientRect(),e=a?Math.max(a[0]&&a[0].height||0,t.height/a.length):0}this.left=t.left,this.right=t.right,this.top=t.top||n,this.height=t.height||i,this.bottom=t.bottom||n+(t.height||i),this.width=t.width||r,this.lineHeight=void 0!==e?e:t.lineHeight,s&&!this.lineHeight&&(this.lineHeight=13)}function Mt(t,e,i,r){var n=new Dt(e),s=e.cue,a=Pt(s),o=[];if(s.snapToLines){var u;switch(s.vertical){case"":o=["+y","-y"],u="height";break;case"rl":o=["+x","-x"],u="width";break;case"lr":o=["-x","+x"],u="width"}var l=n.lineHeight,c=l*Math.round(a),h=i[u]+l,d=o[0];Math.abs(c)>h&&(c=c<0?-1:1,c*=Math.ceil(h/l)*l),a<0&&(c+=""===s.vertical?i.height:i.width,o=o.reverse()),n.move(d,c)}else{var p=n.lineHeight/i.height*100;switch(s.lineAlign){case"middle":a-=p/2;break;case"end":a-=p}switch(s.vertical){case"":e.applyStyles({top:e.formatStyle(a,"%")});break;case"rl":e.applyStyles({left:e.formatStyle(a,"%")});break;case"lr":e.applyStyles({right:e.formatStyle(a,"%")})}o=["+y","-x","+x","-y"],n=new Dt(e)}var f=function(t,e){for(var n,s=new Dt(t),a=1,o=0;o<e.length;o++){for(;t.overlapsOppositeAxis(i,e[o])||t.within(i)&&t.overlapsAny(r);)t.move(e[o]);if(t.within(i))return t;var u=t.intersectPercentage(i);a>u&&(n=new Dt(t),a=u),t=new Dt(s)}return n||s}(n,o);e.move(f.toCSSCompatValues(i))}function Rt(){}function Ut(t){return"string"==typeof t&&(!!Hr[t.toLowerCase()]&&t.toLowerCase())}function Nt(t){return"string"==typeof t&&(!!Vr[t.toLowerCase()]&&t.toLowerCase())}function Bt(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)t[r]=i[r]}return t}function jt(t,e,i){var r=this,n=/MSIE\s8\.0/.test(navigator.userAgent),s={};n?r=document.createElement("custom"):s.enumerable=!0,r.hasBeenReset=!1;var a="",o=!1,u=t,l=e,c=i,h=null,d="",p=!0,f="auto",m="start",g=50,y="middle",v=50,_="middle";if(Object.defineProperty(r,"id",Bt({},s,{get:function(){return a},set:function(t){a=""+t}})),Object.defineProperty(r,"pauseOnExit",Bt({},s,{get:function(){return o},set:function(t){o=!!t}})),Object.defineProperty(r,"startTime",Bt({},s,{get:function(){return u},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");u=t,this.hasBeenReset=!0}})),Object.defineProperty(r,"endTime",Bt({},s,{get:function(){return l},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");l=t,this.hasBeenReset=!0}})),Object.defineProperty(r,"text",Bt({},s,{get:function(){return c},set:function(t){c=""+t,this.hasBeenReset=!0}})),Object.defineProperty(r,"region",Bt({},s,{get:function(){return h},set:function(t){h=t,this.hasBeenReset=!0}})),Object.defineProperty(r,"vertical",Bt({},s,{get:function(){return d},set:function(t){var e=Ut(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");d=e,this.hasBeenReset=!0}})),Object.defineProperty(r,"snapToLines",Bt({},s,{get:function(){return p},set:function(t){p=!!t,this.hasBeenReset=!0}})),Object.defineProperty(r,"line",Bt({},s,{get:function(){return f},set:function(t){if("number"!=typeof t&&t!==Fr)throw new SyntaxError("An invalid number or illegal string was specified.");f=t,this.hasBeenReset=!0}})),Object.defineProperty(r,"lineAlign",Bt({},s,{get:function(){return m},set:function(t){var e=Nt(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");m=e,this.hasBeenReset=!0}})),Object.defineProperty(r,"position",Bt({},s,{get:function(){return g},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");g=t,this.hasBeenReset=!0}})),Object.defineProperty(r,"positionAlign",Bt({},s,{get:function(){return y},set:function(t){var e=Nt(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");y=e,this.hasBeenReset=!0}})),Object.defineProperty(r,"size",Bt({},s,{get:function(){return v},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");v=t,this.hasBeenReset=!0}})),Object.defineProperty(r,"align",Bt({},s,{get:function(){return _},set:function(t){var e=Nt(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");_=e,this.hasBeenReset=!0}})),r.displayState=void 0,n)return r}function Ft(t){return"string"==typeof t&&(!!Wr[t.toLowerCase()]&&t.toLowerCase())}function Ht(t){return"number"==typeof t&&t>=0&&t<=100}function Vt(){var t=100,e=3,i=0,r=100,n=0,s=100,a="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!Ht(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return e},set:function(t){if("number"!=typeof t)throw new TypeError("Lines must be set to a number.");e=t}},regionAnchorY:{enumerable:!0,get:function(){return r},set:function(t){if(!Ht(t))throw new Error("RegionAnchorX must be between 0 and 100.");r=t}},regionAnchorX:{enumerable:!0,get:function(){return i},set:function(t){if(!Ht(t))throw new Error("RegionAnchorY must be between 0 and 100.");i=t}},viewportAnchorY:{enumerable:!0,get:function(){return s},set:function(t){if(!Ht(t))throw new Error("ViewportAnchorY must be between 0 and 100.");s=t}},viewportAnchorX:{enumerable:!0,get:function(){return n},set:function(t){if(!Ht(t))throw new Error("ViewportAnchorX must be between 0 and 100.");n=t}},scroll:{enumerable:!0,get:function(){return a},set:function(t){var e=Ft(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");a=e}}})}function qt(t,e,i,r){var n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},s=t.textTracks();n.kind=e,i&&(n.label=i),r&&(n.language=r),n.tech=t;var a=new xr.text.TrackClass(n);return s.addTrack(a),a}function Wt(t,e){Yr[t]=Yr[t]||[],Yr[t].push(e)}function Gt(t,e,i){t.setTimeout(function(){return te(e,Yr[e.type],i,t)},1)}function zt(t,e){t.forEach(function(t){return t.setTech&&t.setTech(e)})}function Xt(t,e,i){return t.reduceRight(Kt(i),e[i]())}function Yt(t,e,i,r){return e[i](t.reduce(Kt(i),r))}function $t(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,n="call"+Q(i),s=t.reduce(Kt(n),r),a=s===Kr,o=a?null:e[i](s);return Jt(t,i,o,a),o}function Kt(t){return function(e,i){return e===Kr?Kr:i[t]?i[t](e):e}}function Jt(t,e,i,r){for(var n=t.length-1;n>=0;n--){var s=t[n];s[e]&&s[e](r,i)}}function Qt(t){$r[t.id()]=null}function Zt(t,e){var i=$r[t.id()],r=null;if(void 0===i||null===i)return r=e(t),$r[t.id()]=[[e,r]],r;for(var n=0;n<i.length;n++){var s=i[n],a=s[0],o=s[1];a===e&&(r=o)}return null===r&&(r=e(t),i.push([e,r])),r}function te(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments[2],r=arguments[3],s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[],a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],o=e[0],u=e.slice(1);if("string"==typeof o)te(t,Yr[o],i,r,s,a);else if(o){var l=Zt(r,o);l.setSource(n({},t),function(e,n){if(e)return te(t,u,i,r,s,a);s.push(l),te(n,t.type===n.type?u:Yr[n.type],i,r,s,a)})}else u.length?te(t,u,i,r,s,a):a?i(t,s):te(t,Yr["*"],i,r,s,!0)}function ee(t){var e=ur(t.src),i=tn[e.toLowerCase()];return!t.type&&i&&(t.type=i),t}function ie(t,e){return"rgba("+parseInt(t[1]+t[1],16)+","+parseInt(t[2]+t[2],16)+","+parseInt(t[3]+t[3],16)+","+e+")"}function re(t,e,i){try{t.style[e]=i}catch(t){return}}function ne(t){fn=t}function se(){fn=pn}function ae(t,e){if(e&&(t=e(t)),t&&"none"!==t)return t}function oe(t,e){return ae(t.options[t.options.selectedIndex].value,e)}function ue(t,e,i){if(e)for(var r=0;r<t.options.length;r++)if(ae(t.options[r].value,i)===e){t.selectedIndex=r;break}}function le(t,e,i){var r=le.getPlayer(t);if(r)return e&&Le.warn('Player "'+t+'" is already initialised. Options will not be applied.'),i&&r.ready(i),r;var n="string"==typeof t?Be("#"+Fs(t)):t;if(!d(n))throw new TypeError("The element or ID supplied is not valid. (videojs)");ke.body.contains(n)||Le.warn("The element supplied is not included in the DOM"),e=e||{},le.hooks("beforesetup").forEach(function(t){var i=t(n,tt(e));if(!s(i)||Array.isArray(i))return void Le.error("please return an object in beforesetup hooks");e=tt(e,i)});var a=hi.getComponent("Player");return r=new a(n,e,i),le.hooks("setup").forEach(function(t){return t(r)}),r}function ce(t,e){return function(i){var r=this;if(!e)return new ul(t);if(ul&&!i){var n=e.toString().replace(/^function.+?{/,"").slice(0,-1),s=he(n);return this[nl]=new ul(s),de(this[nl],s),this[nl]}var a={postMessage:function(t){r.onmessage&&setTimeout(function(){r.onmessage({data:t,target:a})})}};e.call(a),this.postMessage=function(t){setTimeout(function(){a.onmessage({data:t,target:r})})},this.isThisThread=!0}}function he(t){try{return ol.createObjectURL(new Blob([t],{type:sl}))}catch(i){var e=new al;return e.append(t),ol.createObjectURL(e.getBlob(type))}}function de(t,e){if(t&&e){var i=t.terminate;t.objURL=e,t.terminate=function(){t.objURL&&ol.revokeObjectURL(t.objURL),i.call(t)}}}function pe(){}var fe,me="7.0.0-alpha.1",ge="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};fe="undefined"!=typeof window?window:void 0!==ge?ge:"undefined"!=typeof self?self:{};var ye,ve=fe,_e={},be=(Object.freeze||Object)({default:_e}),Te=be&&_e||be,Se=void 0!==ge?ge:"undefined"!=typeof window?window:{};"undefined"!=typeof document?ye=document:(ye=Se["__GLOBAL_DOCUMENT_CACHE@4"])||(ye=Se["__GLOBAL_DOCUMENT_CACHE@4"]=Te);var ke=ye,Ee=void 0,Ce="info",we=[],Ae=function(t,e){var i=Ee.levels[Ce],r=new RegExp("^("+i+")$");if("log"!==t&&e.unshift(t.toUpperCase()+":"),we&&we.push([].concat(e)),e.unshift("VIDEOJS:"),ve.console){var n=ve.console[t];n||"debug"!==t||(n=ve.console.info||ve.console.log),n&&i&&r.test(t)&&n[Array.isArray(e)?"apply":"call"](ve.console,e)}};Ee=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];Ae("log",e)},Ee.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:Ce},Ee.level=function(t){if("string"==typeof t){if(!Ee.levels.hasOwnProperty(t))throw new Error('"'+t+'" in not a valid log level');Ce=t}return Ce},Ee.history=function(){return we?[].concat(we):[]},Ee.history.clear=function(){we&&(we.length=0)},Ee.history.disable=function(){null!==we&&(we.length=0,we=null)},Ee.history.enable=function(){null===we&&(we=[])},Ee.error=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return Ae("error",e)},Ee.warn=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return Ae("warn",e)},Ee.debug=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return Ae("debug",e)};var Le=Ee,Oe=function(t){for(var i="",r=0;r<arguments.length;r++)i+=e(t[r])+(arguments[r+1]||"");return i},Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ie=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},xe=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},De=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},Me=function(t,e){return t.raw=e,t},Re=Object.prototype.toString,Ue=function(t){return s(t)?Object.keys(t):[]},Ne=Me(["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."],["Setting attributes in the second argument of createEl()\n                has been deprecated. Use the third argument instead.\n                createEl(type, properties, attributes). Attempting to set "," to ","."]),Be=f("querySelector"),je=f("querySelectorAll"),Fe=(Object.freeze||Object)({isReal:h,isEl:d,isInFrame:p,createEl:m,textContent:g,prependTo:y,hasClass:v,addClass:_,removeClass:b,toggleClass:T,setAttributes:S,getAttributes:k,getAttribute:E,setAttribute:C,removeAttribute:w,blockTextSelection:A,unblockTextSelection:L,getBoundingClientRect:O,findPosition:P,getPointerPosition:I,isTextNode:x,emptyEl:D,normalizeContent:M,appendContent:R,insertContent:U,isSingleLeftClick:N,$:Be,$$:je}),He=1,Ve={},qe="vdata"+(new Date).getTime(),We=!1;!function(){try{var t=Object.defineProperty({},"passive",{get:function(){We=!0}});ve.addEventListener("test",null,t),ve.removeEventListener("test",null,t)}catch(t){}}();var Ge=["touchstart","touchmove"],ze=(Object.freeze||Object)({fixEvent:W,on:G,off:z,trigger:X,one:Y}),Xe=!1,Ye=void 0,$e=function(){if(h()){var t=Array.prototype.slice.call(ke.getElementsByTagName("video")),e=Array.prototype.slice.call(ke.getElementsByTagName("audio")),i=Array.prototype.slice.call(ke.getElementsByTagName("video-js")),r=t.concat(e,i)
;if(r&&r.length>0)for(var n=0,s=r.length;n<s;n++){var a=r[n];if(!a||!a.getAttribute){$(1);break}if(void 0===a.player){var o=a.getAttribute("data-setup");null!==o&&Ye(a)}}else Xe||$(1)}};h()&&"complete"===ke.readyState?Xe=!0:Y(ve,"load",function(){Xe=!0});var Ke=function(t){var e=ke.createElement("style");return e.className=t,e},Je=function(t,e){t.styleSheet?t.styleSheet.cssText=e:t.textContent=e},Qe=function(t,e,i){e.guid||(e.guid=B());var r=function(){return e.apply(t,arguments)};return r.guid=i?i+"_"+e.guid:e.guid,r},Ze=function(t,e){var i=Date.now();return function(){var r=Date.now();r-i>=e&&(t.apply(void 0,arguments),i=r)}},ti=function(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:ve,n=void 0;return function(){var s=this,a=arguments,o=function(){n=null,o=null,i||t.apply(s,a)};!n&&i&&t.apply(s,a),r.clearTimeout(n),n=r.setTimeout(o,e)}},ei=function(){};ei.prototype.allowedEvents_={},ei.prototype.on=function(t,e){var i=this.addEventListener;this.addEventListener=function(){},G(this,t,e),this.addEventListener=i},ei.prototype.addEventListener=ei.prototype.on,ei.prototype.off=function(t,e){z(this,t,e)},ei.prototype.removeEventListener=ei.prototype.off,ei.prototype.one=function(t,e){var i=this.addEventListener;this.addEventListener=function(){},Y(this,t,e),this.addEventListener=i},ei.prototype.trigger=function(t){var e=t.type||t;"string"==typeof t&&(t={type:e}),t=W(t),this.allowedEvents_[e]&&this["on"+e]&&this["on"+e](t),X(this,t)},ei.prototype.dispatchEvent=ei.prototype.trigger;var ii=function(t){return t instanceof ei||!!t.eventBusEl_&&["on","one","off","trigger"].every(function(e){return"function"==typeof t[e]})},ri=function(t){return"string"==typeof t&&/\S/.test(t)||Array.isArray(t)&&!!t.length},ni=function(t){if(!t.nodeName&&!ii(t))throw new Error("Invalid target; must be a DOM node or evented object.")},si=function(t){if(!ri(t))throw new Error("Invalid event type; must be a non-empty string or array.")},ai=function(t){if("function"!=typeof t)throw new Error("Invalid listener; must be a function.")},oi=function(t,e){var i=e.length<3||e[0]===t||e[0]===t.eventBusEl_,r=void 0,n=void 0,s=void 0;return i?(r=t.eventBusEl_,e.length>=3&&e.shift(),n=e[0],s=e[1]):(r=e[0],n=e[1],s=e[2]),ni(r),si(n),ai(s),s=Qe(t,s),{isTargetingSelf:i,target:r,type:n,listener:s}},ui=function(t,e,i,r){ni(t),t.nodeName?ze[e](t,i,r):t[e](i,r)},li={on:function(){for(var t=this,e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];var n=oi(this,i),s=n.isTargetingSelf,a=n.target,o=n.type,u=n.listener;if(ui(a,"on",o,u),!s){var l=function(){return t.off(a,o,u)};l.guid=u.guid;var c=function(){return t.off("dispose",l)};c.guid=u.guid,ui(this,"on","dispose",l),ui(a,"on","dispose",c)}},one:function(){for(var t=this,e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];var n=oi(this,i),s=n.isTargetingSelf,a=n.target,o=n.type,u=n.listener;if(s)ui(a,"one",o,u);else{var l=function e(){for(var i=arguments.length,r=Array(i),n=0;n<i;n++)r[n]=arguments[n];t.off(a,o,e),u.apply(null,r)};l.guid=u.guid,ui(a,"one",o,l)}},off:function(t,e,i){if(!t||ri(t))z(this.eventBusEl_,t,e);else{var r=t,n=e;ni(r),si(n),ai(i),i=Qe(this,i),this.off("dispose",i),r.nodeName?(z(r,n,i),z(r,"dispose",i)):ii(r)&&(r.off(n,i),r.off("dispose",i))}},trigger:function(t,e){return X(this.eventBusEl_,t,e)}},ci={state:{},setState:function(t){var e=this;"function"==typeof t&&(t=t());var r=void 0;return i(t,function(t,i){e.state[i]!==t&&(r=r||{},r[i]={from:e.state[i],to:t}),e.state[i]=t}),r&&ii(this)&&this.trigger({changes:r,type:"statechanged"}),r}},hi=function(){function t(e,i,r){if(Ie(this,t),!e&&this.play?this.player_=e=this:this.player_=e,this.options_=tt({},this.options_),i=this.options_=tt(this.options_,i),this.id_=i.id||i.el&&i.el.id,!this.id_){var n=e&&e.id&&e.id()||"no_player";this.id_=n+"_component_"+B()}this.name_=i.name||null,i.el?this.el_=i.el:!1!==i.createEl&&(this.el_=this.createEl()),!1!==i.evented&&K(this,{eventBusKey:this.el_?"el_":null}),J(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},!1!==i.initChildren&&this.initChildren(),this.ready(r),!1!==i.reportTouchActivity&&this.enableTouchActivity()}return t.prototype.dispose=function(){if(this.trigger({type:"dispose",bubbles:!1}),this.children_)for(var t=this.children_.length-1;t>=0;t--)this.children_[t].dispose&&this.children_[t].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),H(this.el_),this.el_=null),this.player_=null},t.prototype.player=function(){return this.player_},t.prototype.options=function(t){return Le.warn("this.options() has been deprecated and will be moved to the constructor in 6.0"),t?(this.options_=tt(this.options_,t),this.options_):this.options_},t.prototype.el=function(){return this.el_},t.prototype.createEl=function(t,e,i){return m(t,e,i)},t.prototype.localize=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=this.player_.language&&this.player_.language(),n=this.player_.languages&&this.player_.languages(),s=n&&n[r],a=r&&r.split("-")[0],o=n&&n[a],u=i;return s&&s[t]?u=s[t]:o&&o[t]&&(u=o[t]),e&&(u=u.replace(/\{(\d+)\}/g,function(t,i){var r=e[i-1],n=r;return void 0===r&&(n=t),n})),u},t.prototype.contentEl=function(){return this.contentEl_||this.el_},t.prototype.id=function(){return this.id_},t.prototype.name=function(){return this.name_},t.prototype.children=function(){return this.children_},t.prototype.getChildById=function(t){return this.childIndex_[t]},t.prototype.getChild=function(t){if(t)return t=Q(t),this.childNameIndex_[t]},t.prototype.addChild=function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.children_.length,n=void 0,s=void 0;if("string"==typeof e){s=Q(e);var a=i.componentClass||s;i.name=s;var o=t.getComponent(a);if(!o)throw new Error("Component "+a+" does not exist");if("function"!=typeof o)return null;n=new o(this.player_||this,i)}else n=e;if(this.children_.splice(r,0,n),"function"==typeof n.id&&(this.childIndex_[n.id()]=n),s=s||n.name&&Q(n.name()),s&&(this.childNameIndex_[s]=n),"function"==typeof n.el&&n.el()){var u=this.contentEl().children,l=u[r]||null;this.contentEl().insertBefore(n.el(),l)}return n},t.prototype.removeChild=function(t){if("string"==typeof t&&(t=this.getChild(t)),t&&this.children_){for(var e=!1,i=this.children_.length-1;i>=0;i--)if(this.children_[i]===t){e=!0,this.children_.splice(i,1);break}if(e){this.childIndex_[t.id()]=null,this.childNameIndex_[t.name()]=null;var r=t.el();r&&r.parentNode===this.contentEl()&&this.contentEl().removeChild(t.el())}}},t.prototype.initChildren=function(){var e=this,i=this.options_.children;if(i){var r=this.options_,n=function(t){var i=t.name,n=t.opts;if(void 0!==r[i]&&(n=r[i]),!1!==n){!0===n&&(n={}),n.playerOptions=e.options_.playerOptions;var s=e.addChild(i,n);s&&(e[i]=s)}},s=void 0,a=t.getComponent("Tech");s=Array.isArray(i)?i:Object.keys(i),s.concat(Object.keys(this.options_).filter(function(t){return!s.some(function(e){return"string"==typeof e?t===e:t===e.name})})).map(function(t){var r=void 0,n=void 0;return"string"==typeof t?(r=t,n=i[r]||e.options_[r]||{}):(r=t.name,n=t),{name:r,opts:n}}).filter(function(e){var i=t.getComponent(e.opts.componentClass||Q(e.name));return i&&!a.isTech(i)}).forEach(n)}},t.prototype.buildCSSClass=function(){return""},t.prototype.ready=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t)return this.isReady_?void(e?t.call(this):this.setTimeout(t,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(t))},t.prototype.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var t=this.readyQueue_;this.readyQueue_=[],t&&t.length>0&&t.forEach(function(t){t.call(this)},this),this.trigger("ready")},1)},t.prototype.$=function(t,e){return Be(t,e||this.contentEl())},t.prototype.$$=function(t,e){return je(t,e||this.contentEl())},t.prototype.hasClass=function(t){return v(this.el_,t)},t.prototype.addClass=function(t){_(this.el_,t)},t.prototype.removeClass=function(t){b(this.el_,t)},t.prototype.toggleClass=function(t,e){T(this.el_,t,e)},t.prototype.show=function(){this.removeClass("vjs-hidden")},t.prototype.hide=function(){this.addClass("vjs-hidden")},t.prototype.lockShowing=function(){this.addClass("vjs-lock-showing")},t.prototype.unlockShowing=function(){this.removeClass("vjs-lock-showing")},t.prototype.getAttribute=function(t){return E(this.el_,t)},t.prototype.setAttribute=function(t,e){C(this.el_,t,e)},t.prototype.removeAttribute=function(t){w(this.el_,t)},t.prototype.width=function(t,e){return this.dimension("width",t,e)},t.prototype.height=function(t,e){return this.dimension("height",t,e)},t.prototype.dimensions=function(t,e){this.width(t,!0),this.height(e)},t.prototype.dimension=function(t,e,i){if(void 0!==e)return null!==e&&e===e||(e=0),-1!==(""+e).indexOf("%")||-1!==(""+e).indexOf("px")?this.el_.style[t]=e:this.el_.style[t]="auto"===e?"":e+"px",void(i||this.trigger("componentresize"));if(!this.el_)return 0;var r=this.el_.style[t],n=r.indexOf("px");return-1!==n?parseInt(r.slice(0,n),10):parseInt(this.el_["offset"+Q(t)],10)},t.prototype.currentDimension=function(t){var e=0;if("width"!==t&&"height"!==t)throw new Error("currentDimension only accepts width or height value");if("function"==typeof ve.getComputedStyle){var i=ve.getComputedStyle(this.el_);e=i.getPropertyValue(t)||i[t]}if(0===(e=parseFloat(e))){var r="offset"+Q(t);e=this.el_[r]}return e},t.prototype.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},t.prototype.currentWidth=function(){return this.currentDimension("width")},t.prototype.currentHeight=function(){return this.currentDimension("height")},t.prototype.focus=function(){this.el_.focus()},t.prototype.blur=function(){this.el_.blur()},t.prototype.emitTapEvents=function(){var t=0,e=null,i=void 0;this.on("touchstart",function(r){1===r.touches.length&&(e={pageX:r.touches[0].pageX,pageY:r.touches[0].pageY},t=(new Date).getTime(),i=!0)}),this.on("touchmove",function(t){if(t.touches.length>1)i=!1;else if(e){var r=t.touches[0].pageX-e.pageX,n=t.touches[0].pageY-e.pageY,s=Math.sqrt(r*r+n*n);s>10&&(i=!1)}});var r=function(){i=!1};this.on("touchleave",r),this.on("touchcancel",r),this.on("touchend",function(r){if(e=null,!0===i){(new Date).getTime()-t<200&&(r.preventDefault(),this.trigger("tap"))}})},t.prototype.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var t=Qe(this.player(),this.player().reportUserActivity),e=void 0;this.on("touchstart",function(){t(),this.clearInterval(e),e=this.setInterval(t,250)});var i=function(i){t(),this.clearInterval(e)};this.on("touchmove",t),this.on("touchend",i),this.on("touchcancel",i)}},t.prototype.setTimeout=function(t,e){var i=this;t=Qe(this,t);var r=ve.setTimeout(t,e),n=function(){return i.clearTimeout(r)};return n.guid="vjs-timeout-"+r,this.on("dispose",n),r},t.prototype.clearTimeout=function(t){ve.clearTimeout(t);var e=function(){};return e.guid="vjs-timeout-"+t,this.off("dispose",e),t},t.prototype.setInterval=function(t,e){var i=this;t=Qe(this,t);var r=ve.setInterval(t,e),n=function(){return i.clearInterval(r)};return n.guid="vjs-interval-"+r,this.on("dispose",n),r},t.prototype.clearInterval=function(t){ve.clearInterval(t);var e=function(){};return e.guid="vjs-interval-"+t,this.off("dispose",e),t},t.prototype.requestAnimationFrame=function(t){var e=this;if(this.supportsRaf_){t=Qe(this,t);var i=ve.requestAnimationFrame(t),r=function(){return e.cancelAnimationFrame(i)};return r.guid="vjs-raf-"+i,this.on("dispose",r),i}return this.setTimeout(t,1e3/60)},t.prototype.cancelAnimationFrame=function(t){if(this.supportsRaf_){ve.cancelAnimationFrame(t);var e=function(){};return e.guid="vjs-raf-"+t,this.off("dispose",e),t}return this.clearTimeout(t)},t.registerComponent=function(e,i){if("string"!=typeof e||!e)throw new Error('Illegal component name, "'+e+'"; must be a non-empty string.');var r=t.getComponent("Tech"),n=r&&r.isTech(i),s=t===i||t.prototype.isPrototypeOf(i.prototype);if(n||!s){var a=void 0;throw a=n?"techs must be registered using Tech.registerTech()":"must be a Component subclass",new Error('Illegal component, "'+e+'"; '+a+".")}e=Q(e),t.components_||(t.components_={});var o=t.getComponent("Player");if("Player"===e&&o&&o.players){var u=o.players,l=Object.keys(u);if(u&&l.length>0&&l.map(function(t){return u[t]}).every(Boolean))throw new Error("Can not register Player component after player has been created.")}return t.components_[e]=i,i},t.getComponent=function(e){if(e)return e=Q(e),t.components_&&t.components_[e]?t.components_[e]:void 0},t}();hi.prototype.supportsRaf_="function"==typeof ve.requestAnimationFrame&&"function"==typeof ve.cancelAnimationFrame,hi.registerComponent("Component",hi);for(var di=ve.navigator&&ve.navigator.userAgent||"",pi=/AppleWebKit\/([\d.]+)/i.exec(di),fi=pi?parseFloat(pi.pop()):null,mi=/iPad/i.test(di),gi=/iPhone/i.test(di)&&!mi,yi=/iPod/i.test(di),vi=gi||mi||yi,_i=function(){var t=di.match(/OS (\d+)_/i);return t&&t[1]?t[1]:null}(),bi=/Android/i.test(di),Ti=function(){var t=di.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!t)return null;var e=t[1]&&parseFloat(t[1]),i=t[2]&&parseFloat(t[2]);return e&&i?parseFloat(t[1]+"."+t[2]):e||null}(),Si=bi&&Ti<5&&fi<537,ki=/Firefox/i.test(di),Ei=/Edge/i.test(di),Ci=!Ei&&/Chrome/i.test(di),wi=function(){var t=di.match(/Chrome\/(\d+)/);return t&&t[1]?parseFloat(t[1]):null}(),Ai=function(){var t=/MSIE\s(\d+)\.\d/.exec(di),e=t&&parseFloat(t[1]);return!e&&/Trident\/7.0/i.test(di)&&/rv:11.0/.test(di)&&(e=11),e}(),Li=/Safari/i.test(di)&&!Ci&&!bi&&!Ei,Oi=Li||vi,Pi=(h()&&("ontouchstart"in ve||ve.DocumentTouch&&ve.document instanceof ve.DocumentTouch)),Ii=(Object.freeze||Object)({IS_IPAD:mi,IS_IPHONE:gi,IS_IPOD:yi,IS_IOS:vi,IOS_VERSION:_i,IS_ANDROID:bi,ANDROID_VERSION:Ti,IS_NATIVE_ANDROID:Si,IS_FIREFOX:ki,IS_EDGE:Ei,IS_CHROME:Ci,CHROME_VERSION:wi,IE_VERSION:Ai,IS_SAFARI:Li,IS_ANY_SAFARI:Oi,TOUCH_ENABLED:Pi}),xi={},Di=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],Mi=Di[0],Ri=void 0,Ui=0;Ui<Di.length;Ui++)if(Di[Ui][1]in ke){Ri=Di[Ui];break}if(Ri)for(var Ni=0;Ni<Ri.length;Ni++)xi[Mi[Ni]]=Ri[Ni];at.prototype.code=0,at.prototype.message="",at.prototype.status=null,at.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],at.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var Bi=0;Bi<at.errorTypes.length;Bi++)at[at.errorTypes[Bi]]=Bi,at.prototype[at.errorTypes[Bi]]=Bi;var ji=ot,Fi=function(t){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(e,i,r){return t[i]&&(e[i]=t[i]),e},{cues:t.cues&&Array.prototype.map.call(t.cues,function(t){return{startTime:t.startTime,endTime:t.endTime,text:t.text,id:t.id}})})},Hi=function(t){var e=t.$$("track"),i=Array.prototype.map.call(e,function(t){return t.track});return Array.prototype.map.call(e,function(t){var e=Fi(t.track);return t.src&&(e.src=t.src),e}).concat(Array.prototype.filter.call(t.textTracks(),function(t){return-1===i.indexOf(t)}).map(Fi))},Vi=function(t,e){return t.forEach(function(t){var i=e.addRemoteTextTrack(t).track;!t.src&&t.cues&&t.cues.forEach(function(t){return i.addCue(t)})}),e.textTracks()},qi={textTracksToJson:Hi,jsonToTextTracks:Vi,trackToJson_:Fi},Wi="vjs-modal-dialog",Gi=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.opened_=n.hasBeenOpened_=n.hasBeenFilled_=!1,n.closeable(!n.options_.uncloseable),n.content(n.options_.content),n.contentEl_=m("div",{className:Wi+"-content"},{role:"document"}),n.descEl_=m("p",{className:Wi+"-description vjs-control-text",id:n.el().getAttribute("aria-describedby")}),g(n.descEl_,n.description()),n.el_.appendChild(n.descEl_),n.el_.appendChild(n.contentEl_),n}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},e.prototype.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,t.prototype.dispose.call(this)},e.prototype.buildCSSClass=function(){return Wi+" vjs-hidden "+t.prototype.buildCSSClass.call(this)},e.prototype.handleKeyPress=function(t){27===t.which&&this.closeable()&&this.close()},e.prototype.label=function(){return this.localize(this.options_.label||"Modal Window")},e.prototype.description=function(){var t=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(t+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),t},e.prototype.open=function(){if(!this.opened_){var t=this.player();this.trigger("beforemodalopen"),this.opened_=!0,(this.options_.fillAlways||!this.hasBeenOpened_&&!this.hasBeenFilled_)&&this.fill(),this.wasPlaying_=!t.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&t.pause(),this.closeable()&&this.on(this.el_.ownerDocument,"keydown",Qe(this,this.handleKeyPress)),this.hadControls_=t.controls(),t.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},e.prototype.opened=function(t){return"boolean"==typeof t&&this[t?"open":"close"](),this.opened_},e.prototype.close=function(){if(this.opened_){var t=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&t.play(),this.closeable()&&this.off(this.el_.ownerDocument,"keydown",Qe(this,this.handleKeyPress)),this.hadControls_&&t.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},e.prototype.closeable=function(t){if("boolean"==typeof t){var e=this.closeable_=!!t,i=this.getChild("closeButton");if(e&&!i){var r=this.contentEl_;this.contentEl_=this.el_,i=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=r,this.on(i,"close",this.close)}!e&&i&&(this.off(i,"close",this.close),this.removeChild(i),i.dispose())}return this.closeable_},e.prototype.fill=function(){this.fillWith(this.content())},e.prototype.fillWith=function(t){var e=this.contentEl(),i=e.parentNode,r=e.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,i.removeChild(e),this.empty(),U(e,t),this.trigger("modalfill"),r?i.insertBefore(e,r):i.appendChild(e);var n=this.getChild("closeButton");n&&i.appendChild(n.el_)},e.prototype.empty=function(){this.trigger("beforemodalempty"),D(this.contentEl()),this.trigger("modalempty")},e.prototype.content=function(t){return void 0!==t&&(this.content_=t),this.content_},e.prototype.conditionalFocus_=function(){var t=ke.activeElement,e=this.player_.el_;this.previouslyActiveEl_=null,(e.contains(t)||e===t)&&(this.previouslyActiveEl_=t,this.focus(),this.on(ke,"keydown",this.handleKeyDown))},e.prototype.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null),this.off(ke,"keydown",this.handleKeyDown)},e.prototype.handleKeyDown=function(t){if(9===t.which){for(var e=this.focusableEls_(),i=this.el_.querySelector(":focus"),r=void 0,n=0;n<e.length;n++)if(i===e[n]){r=n;break}ke.activeElement===this.el_&&(r=0),t.shiftKey&&0===r?(e[e.length-1].focus(),t.preventDefault()):t.shiftKey||r!==e.length-1||(e[0].focus(),t.preventDefault())}},e.prototype.focusableEls_=function(){var t=this.el_.querySelectorAll("*");return Array.prototype.filter.call(t,function(t){return(t instanceof ve.HTMLAnchorElement||t instanceof ve.HTMLAreaElement)&&t.hasAttribute("href")||(t instanceof ve.HTMLInputElement||t instanceof ve.HTMLSelectElement||t instanceof ve.HTMLTextAreaElement||t instanceof ve.HTMLButtonElement)&&!t.hasAttribute("disabled")||t instanceof ve.HTMLIFrameElement||t instanceof ve.HTMLObjectElement||t instanceof ve.HTMLEmbedElement||t.hasAttribute("tabindex")&&-1!==t.getAttribute("tabindex")||t.hasAttribute("contenteditable")})},e}(hi);Gi.prototype.options_={pauseOnOpen:!0,temporary:!0},hi.registerComponent("ModalDialog",Gi);var zi=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];Ie(this,e);var r=De(this,t.call(this));r.tracks_=[],Object.defineProperty(r,"length",{get:function(){return this.tracks_.length}});for(var n=0;n<i.length;n++)r.addTrack(i[n]);return r}return xe(e,t),e.prototype.addTrack=function(t){var e=this.tracks_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.tracks_[e]}}),-1===this.tracks_.indexOf(t)&&(this.tracks_.push(t),this.trigger({track:t,type:"addtrack"}))},e.prototype.removeTrack=function(t){for(var e=void 0,i=0,r=this.length;i<r;i++)if(this[i]===t){e=this[i],e.off&&e.off(),this.tracks_.splice(i,1);break}e&&this.trigger({track:e,type:"removetrack"})},e.prototype.getTrackById=function(t){for(var e=null,i=0,r=this.length;i<r;i++){var n=this[i];if(n.id===t){e=n;break}}return e},e}(ei);zi.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"};for(var Xi in zi.prototype.allowedEvents_)zi.prototype["on"+Xi]=null;var Yi=function(t,e){for(var i=0;i<t.length;i++)Object.keys(t[i]).length&&e.id!==t[i].id&&(t[i].enabled=!1)},$i=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];Ie(this,e);for(var r=i.length-1;r>=0;r--)if(i[r].enabled){Yi(i,i[r]);break}var n=De(this,t.call(this,i));return n.changing_=!1,n}return xe(e,t),e.prototype.addTrack=function(e){var i=this;e.enabled&&Yi(this,e),t.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("enabledchange",function(){i.changing_||(i.changing_=!0,Yi(i,e),i.changing_=!1,i.trigger("change"))})},e}(zi),Ki=function(t,e){for(var i=0;i<t.length;i++)Object.keys(t[i]).length&&e.id!==t[i].id&&(t[i].selected=!1)},Ji=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];Ie(this,e);for(var r=i.length-1;r>=0;r--)if(i[r].selected){Ki(i,i[r]);break}var n=De(this,t.call(this,i));return n.changing_=!1,Object.defineProperty(n,"selectedIndex",{get:function(){for(var t=0;t<this.length;t++)if(this[t].selected)return t;return-1},set:function(){}}),n}return xe(e,t),e.prototype.addTrack=function(e){var i=this;e.selected&&Ki(this,e),t.prototype.addTrack.call(this,e),e.addEventListener&&e.addEventListener("selectedchange",function(){i.changing_||(i.changing_=!0,Ki(i,e),i.changing_=!1,i.trigger("change"))})},e}(zi),Qi=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.addTrack=function(e){t.prototype.addTrack.call(this,e),e.addEventListener("modechange",Qe(this,function(){this.trigger("change")})),-1===["metadata","chapters"].indexOf(e.kind)&&e.addEventListener("modechange",Qe(this,function(){this.trigger("selectedlanguagechange")}))},e}(zi),Zi=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];Ie(this,t),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var i=0,r=e.length;i<r;i++)this.addTrackElement_(e[i])}return t.prototype.addTrackElement_=function(t){var e=this.trackElements_.length;""+e in this||Object.defineProperty(this,e,{get:function(){return this.trackElements_[e]}}),-1===this.trackElements_.indexOf(t)&&this.trackElements_.push(t)},t.prototype.getTrackElementByTrack_=function(t){for(var e=void 0,i=0,r=this.trackElements_.length;i<r;i++)if(t===this.trackElements_[i].track){e=this.trackElements_[i];break}return e},t.prototype.removeTrackElement_=function(t){for(var e=0,i=this.trackElements_.length;e<i;e++)if(t===this.trackElements_[e]){this.trackElements_.splice(e,1);break}},t}(),tr=function(){function t(e){Ie(this,t),t.prototype.setCues_.call(this,e),Object.defineProperty(this,"length",{get:function(){return this.length_}})}return t.prototype.setCues_=function(t){var e=this.length||0,i=0,r=t.length;this.cues_=t,this.length_=t.length;var n=function(t){""+t in this||Object.defineProperty(this,""+t,{get:function(){return this.cues_[t]}})};if(e<r)for(i=e;i<r;i++)n.call(this,i)},t.prototype.getCueById=function(t){for(var e=null,i=0,r=this.length;i<r;i++){var n=this[i];if(n.id===t){e=n;break}}return e},t}(),er={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},ir={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},rr={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},nr={disabled:"disabled",hidden:"hidden",showing:"showing"},sr=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Ie(this,e);var r=De(this,t.call(this)),n={id:i.id||"vjs_track_"+B(),kind:i.kind||"",label:i.label||"",language:i.language||""};for(var s in n)!function(t){Object.defineProperty(r,t,{get:function(){return n[t]},set:function(){}})}(s);return r}return xe(e,t),e}(ei),ar=function(t){var e=["protocol","hostname","port","pathname","search","hash","host"],i=ke.createElement("a");i.href=t;var r=""===i.host&&"file:"!==i.protocol,n=void 0;r&&(n=ke.createElement("div"),n.innerHTML='<a href="'+t+'"></a>',i=n.firstChild,n.setAttribute("style","display:none; position:absolute;"),ke.body.appendChild(n));for(var s={},a=0;a<e.length;a++)s[e[a]]=i[e[a]];return"http:"===s.protocol&&(s.host=s.host.replace(/:80$/,"")),"https:"===s.protocol&&(s.host=s.host.replace(/:443$/,"")),s.protocol||(s.protocol=ve.location.protocol),r&&ke.body.removeChild(n),s},or=function(t){if(!t.match(/^https?:\/\//)){var e=ke.createElement("div");e.innerHTML='<a href="'+t+'">x</a>',t=e.firstChild.href}return t},ur=function(t){if("string"==typeof t){var e=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/i,i=e.exec(t);if(i)return i.pop().toLowerCase()}return""},lr=function(t){var e=ve.location,i=ar(t);return(":"===i.protocol?e.protocol:i.protocol)+i.host!==e.protocol+e.host},cr=(Object.freeze||Object)({parseUrl:ar,getAbsoluteURL:or,getFileExtension:ur,isCrossOrigin:lr}),hr=ct,dr=Object.prototype.toString,pr=t(function(t,e){function i(t){return t.replace(/^\s*|\s*$/g,"")}e=t.exports=i,e.left=function(t){return t.replace(/^\s*/,"")},e.right=function(t){return t.replace(/\s*$/,"")}}),fr=ht,mr=Object.prototype.toString,gr=Object.prototype.hasOwnProperty,yr=function(t){return"[object Array]"===Object.prototype.toString.call(t)},vr=function(t){if(!t)return{};var e={};return fr(pr(t).split("\n"),function(t){var i=t.indexOf(":"),r=pr(t.slice(0,i)).toLowerCase(),n=pr(t.slice(i+1));void 0===e[r]?e[r]=n:yr(e[r])?e[r].push(n):e[r]=[e[r],n]}),e},_r=mt,br=Object.prototype.hasOwnProperty,Tr=vt;vt.XMLHttpRequest=ve.XMLHttpRequest||Tt,vt.XDomainRequest="withCredentials"in new vt.XMLHttpRequest?vt.XMLHttpRequest:ve.XDomainRequest,function(t,e){for(var i=0;i<t.length;i++)e(t[i])}(["get","put","post","patch","head","delete"],function(t){vt["delete"===t?"del":t]=function(e,i,r){return i=yt(e,i,r),i.method=t.toUpperCase(),_t(i)}});var Sr=function(t,e){var i=new ve.WebVTT.Parser(ve,ve.vttjs,ve.WebVTT.StringDecoder()),r=[];i.oncue=function(t){e.addCue(t)},i.onparsingerror=function(t){r.push(t)},i.onflush=function(){e.trigger({type:"loadeddata",target:e})},i.parse(t),r.length>0&&(ve.console&&ve.console.groupCollapsed&&ve.console.groupCollapsed("Text Track parsing errors for "+e.src),r.forEach(function(t){return Le.error(t)}),ve.console&&ve.console.groupEnd&&ve.console.groupEnd()),i.flush()},kr=function(t,e){var i={uri:t},r=lr(t);r&&(i.cors=r),Tr(i,Qe(this,function(t,i,r){if(t)return Le.error(t,i);if(e.loaded_=!0,"function"!=typeof ve.WebVTT){if(e.tech_){var n=function(){return Sr(r,e)};e.tech_.on("vttjsloaded",n),e.tech_.on("vttjserror",function(){Le.error("vttjs failed to load, stopping trying to process "+e.src),e.tech_.off("vttjsloaded",n)})}}else Sr(r,e)}))},Er=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(Ie(this,e),!i.tech)throw new Error("A tech was not provided.");var r=tt(i,{kind:rr[i.kind]||"subtitles",language:i.language||i.srclang||""}),n=nr[r.mode]||"disabled",s=r.default;"metadata"!==r.kind&&"chapters"!==r.kind||(n="hidden");var a=De(this,t.call(this,r));a.tech_=r.tech,a.cues_=[],a.activeCues_=[];var o=new tr(a.cues_),u=new tr(a.activeCues_),l=!1,c=Qe(a,function(){this.activeCues,l&&(this.trigger("cuechange"),l=!1)});return"disabled"!==n&&a.tech_.ready(function(){a.tech_.on("timeupdate",c)},!0),Object.defineProperties(a,{default:{get:function(){return s},set:function(){}},mode:{get:function(){return n},set:function(t){var e=this;nr[t]&&(n=t,"showing"===n&&this.tech_.ready(function(){e.tech_.on("timeupdate",c)},!0),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?o:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return u;for(var t=this.tech_.currentTime(),e=[],i=0,r=this.cues.length;i<r;i++){var n=this.cues[i];n.startTime<=t&&n.endTime>=t?e.push(n):n.startTime===n.endTime&&n.startTime<=t&&n.startTime+.5>=t&&e.push(n)}if(l=!1,e.length!==this.activeCues_.length)l=!0;else for(var s=0;s<e.length;s++)-1===this.activeCues_.indexOf(e[s])&&(l=!0);return this.activeCues_=e,u.setCues_(this.activeCues_),u},set:function(){}}}),r.src?(a.src=r.src,kr(r.src,a)):a.loaded_=!0,a}return xe(e,t),e.prototype.addCue=function(t){var e=t;if(ve.vttjs&&!(t instanceof ve.vttjs.VTTCue)){e=new ve.vttjs.VTTCue(t.startTime,t.endTime,t.text);for(var i in t)i in e||(e[i]=t[i]);e.id=t.id,e.originalCue_=t}for(var r=this.tech_.textTracks(),n=0;n<r.length;n++)r[n]!==this&&r[n].removeCue(e);this.cues_.push(e),this.cues.setCues_(this.cues_)},e.prototype.removeCue=function(t){for(var e=this.cues_.length;e--;){var i=this.cues_[e];if(i===t||i.originalCue_&&i.originalCue_===t){this.cues_.splice(e,1),this.cues.setCues_(this.cues_);break}}},e}(sr);Er.prototype.allowedEvents_={cuechange:"cuechange"};var Cr=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Ie(this,e);var r=tt(i,{kind:ir[i.kind]||""}),n=De(this,t.call(this,r)),s=!1;return Object.defineProperty(n,"enabled",{get:function(){return s},set:function(t){"boolean"==typeof t&&t!==s&&(s=t,this.trigger("enabledchange"))}}),r.enabled&&(n.enabled=r.enabled),n.loaded_=!0,n}return xe(e,t),e}(sr),wr=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Ie(this,e);var r=tt(i,{kind:er[i.kind]||""}),n=De(this,t.call(this,r)),s=!1;return Object.defineProperty(n,"selected",{get:function(){return s},set:function(t){"boolean"==typeof t&&t!==s&&(s=t,this.trigger("selectedchange"))}}),r.selected&&(n.selected=r.selected),n}
return xe(e,t),e}(sr),Ar=0,Lr=2,Or=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Ie(this,e);var r=De(this,t.call(this)),n=void 0,s=new Er(i);return r.kind=s.kind,r.src=s.src,r.srclang=s.language,r.label=s.label,r.default=s.default,Object.defineProperties(r,{readyState:{get:function(){return n}},track:{get:function(){return s}}}),n=Ar,s.addEventListener("loadeddata",function(){n=Lr,r.trigger({type:"load",target:r})}),r}return xe(e,t),e}(ei);Or.prototype.allowedEvents_={load:"load"},Or.NONE=Ar,Or.LOADING=1,Or.LOADED=Lr,Or.ERROR=3;var Pr={audio:{ListClass:$i,TrackClass:Cr,capitalName:"Audio"},video:{ListClass:Ji,TrackClass:wr,capitalName:"Video"},text:{ListClass:Qi,TrackClass:Er,capitalName:"Text"}};Object.keys(Pr).forEach(function(t){Pr[t].getterName=t+"Tracks",Pr[t].privateName=t+"Tracks_"});var Ir={remoteText:{ListClass:Qi,TrackClass:Er,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:Zi,TrackClass:Or,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},xr=tt(Pr,Ir);Ir.names=Object.keys(Ir),Pr.names=Object.keys(Pr),xr.names=[].concat(Ir.names).concat(Pr.names);var Dr=Object.create||function(){function t(){}return function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return t.prototype=e,new t}}();St.prototype=Dr(Error.prototype),St.prototype.constructor=St,St.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},Et.prototype={set:function(t,e){this.get(t)||""===e||(this.values[t]=e)},get:function(t,e,i){return i?this.has(t)?this.values[t]:e[i]:this.has(t)?this.values[t]:e},has:function(t){return t in this.values},alt:function(t,e,i){for(var r=0;r<i.length;++r)if(e===i[r]){this.set(t,e);break}},integer:function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},percent:function(t,e){return!!(e.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(e=parseFloat(e))>=0&&e<=100)&&(this.set(t,e),!0)}};var Mr={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},Rr={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},Ur={v:"title",lang:"lang"},Nr={rt:"ruby"},Br=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];It.prototype.applyStyles=function(t,e){e=e||this.div;for(var i in t)t.hasOwnProperty(i)&&(e.style[i]=t[i])},It.prototype.formatStyle=function(t,e){return 0===t?0:t+e},xt.prototype=Dr(It.prototype),xt.prototype.constructor=xt,Dt.prototype.move=function(t,e){switch(e=void 0!==e?e:this.lineHeight,t){case"+x":this.left+=e,this.right+=e;break;case"-x":this.left-=e,this.right-=e;break;case"+y":this.top+=e,this.bottom+=e;break;case"-y":this.top-=e,this.bottom-=e}},Dt.prototype.overlaps=function(t){return this.left<t.right&&this.right>t.left&&this.top<t.bottom&&this.bottom>t.top},Dt.prototype.overlapsAny=function(t){for(var e=0;e<t.length;e++)if(this.overlaps(t[e]))return!0;return!1},Dt.prototype.within=function(t){return this.top>=t.top&&this.bottom<=t.bottom&&this.left>=t.left&&this.right<=t.right},Dt.prototype.overlapsOppositeAxis=function(t,e){switch(e){case"+x":return this.left<t.left;case"-x":return this.right>t.right;case"+y":return this.top<t.top;case"-y":return this.bottom>t.bottom}},Dt.prototype.intersectPercentage=function(t){return Math.max(0,Math.min(this.right,t.right)-Math.max(this.left,t.left))*Math.max(0,Math.min(this.bottom,t.bottom)-Math.max(this.top,t.top))/(this.height*this.width)},Dt.prototype.toCSSCompatValues=function(t){return{top:this.top-t.top,bottom:t.bottom-this.bottom,left:this.left-t.left,right:t.right-this.right,height:this.height,width:this.width}},Dt.getSimpleBoxPosition=function(t){var e=t.div?t.div.offsetHeight:t.tagName?t.offsetHeight:0,i=t.div?t.div.offsetWidth:t.tagName?t.offsetWidth:0,r=t.div?t.div.offsetTop:t.tagName?t.offsetTop:0;return t=t.div?t.div.getBoundingClientRect():t.tagName?t.getBoundingClientRect():t,{left:t.left,right:t.right,top:t.top||r,height:t.height||e,bottom:t.bottom||r+(t.height||e),width:t.width||i}},Rt.StringDecoder=function(){return{decode:function(t){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}},Rt.convertCueToDOMTree=function(t,e){return t&&e?At(t,e):null};Rt.processCues=function(t,e,i){if(!t||!e||!i)return null;for(;i.firstChild;)i.removeChild(i.firstChild);var r=t.document.createElement("div");if(r.style.position="absolute",r.style.left="0",r.style.right="0",r.style.top="0",r.style.bottom="0",r.style.margin="1.5%",i.appendChild(r),function(t){for(var e=0;e<t.length;e++)if(t[e].hasBeenReset||!t[e].displayState)return!0;return!1}(e)){var n=[],s=Dt.getSimpleBoxPosition(r),a=Math.round(.05*s.height*100)/100,o={font:a+"px sans-serif"};!function(){for(var i,a,u=0;u<e.length;u++)a=e[u],i=new xt(t,a,o),r.appendChild(i.div),Mt(t,i,s,n),a.displayState=i.div,n.push(Dt.getSimpleBoxPosition(i))}()}else for(var u=0;u<e.length;u++)r.appendChild(e[u].displayState)},Rt.Parser=function(t,e,i){i||(i=e,e={}),e||(e={}),this.window=t,this.vttjs=e,this.state="INITIAL",this.buffer="",this.decoder=i||new TextDecoder("utf8"),this.regionList=[]},Rt.Parser.prototype={reportOrThrowError:function(t){if(!(t instanceof St))throw t;this.onparsingerror&&this.onparsingerror(t)},parse:function(t){function e(){for(var t=n.buffer,e=0;e<t.length&&"\r"!==t[e]&&"\n"!==t[e];)++e;var i=t.substr(0,e);return"\r"===t[e]&&++e,"\n"===t[e]&&++e,n.buffer=t.substr(e),i}function i(t){var e=new Et;if(Ct(t,function(t,i){switch(t){case"id":e.set(t,i);break;case"width":e.percent(t,i);break;case"lines":e.integer(t,i);break;case"regionanchor":case"viewportanchor":var r=i.split(",");if(2!==r.length)break;var n=new Et;if(n.percent("x",r[0]),n.percent("y",r[1]),!n.has("x")||!n.has("y"))break;e.set(t+"X",n.get("x")),e.set(t+"Y",n.get("y"));break;case"scroll":e.alt(t,i,["up"])}},/=/,/\s/),e.has("id")){var i=new(n.vttjs.VTTRegion||n.window.VTTRegion);i.width=e.get("width",100),i.lines=e.get("lines",3),i.regionAnchorX=e.get("regionanchorX",0),i.regionAnchorY=e.get("regionanchorY",100),i.viewportAnchorX=e.get("viewportanchorX",0),i.viewportAnchorY=e.get("viewportanchorY",100),i.scroll=e.get("scroll",""),n.onregion&&n.onregion(i),n.regionList.push({id:e.get("id"),region:i})}}function r(t){var e=new Et;Ct(t,function(t,i){switch(t){case"MPEGT":e.integer(t+"S",i);break;case"LOCA":e.set(t+"L",kt(i))}},/[^\d]:/,/,/),n.ontimestampmap&&n.ontimestampmap({MPEGTS:e.get("MPEGTS"),LOCAL:e.get("LOCAL")})}var n=this;t&&(n.buffer+=n.decoder.decode(t,{stream:!0}));try{var s;if("INITIAL"===n.state){if(!/\r\n|\n/.test(n.buffer))return this;s=e();var a=s.match(/^WEBVTT([ \t].*)?$/);if(!a||!a[0])throw new St(St.Errors.BadSignature);n.state="HEADER"}for(var o=!1;n.buffer;){if(!/\r\n|\n/.test(n.buffer))return this;switch(o?o=!1:s=e(),n.state){case"HEADER":/:/.test(s)?function(t){t.match(/X-TIMESTAMP-MAP/)?Ct(t,function(t,e){switch(t){case"X-TIMESTAMP-MAP":r(e)}},/=/):Ct(t,function(t,e){switch(t){case"Region":i(e)}},/:/)}(s):s||(n.state="ID");continue;case"NOTE":s||(n.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(s)){n.state="NOTE";break}if(!s)continue;if(n.cue=new(n.vttjs.VTTCue||n.window.VTTCue)(0,0,""),n.state="CUE",-1===s.indexOf("--\x3e")){n.cue.id=s;continue}case"CUE":try{wt(s,n.cue,n.regionList)}catch(t){n.reportOrThrowError(t),n.cue=null,n.state="BADCUE";continue}n.state="CUETEXT";continue;case"CUETEXT":var u=-1!==s.indexOf("--\x3e");if(!s||u&&(o=!0)){n.oncue&&n.oncue(n.cue),n.cue=null,n.state="ID";continue}n.cue.text&&(n.cue.text+="\n"),n.cue.text+=s;continue;case"BADCUE":s||(n.state="ID");continue}}}catch(t){n.reportOrThrowError(t),"CUETEXT"===n.state&&n.cue&&n.oncue&&n.oncue(n.cue),n.cue=null,n.state="INITIAL"===n.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),(t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new St(St.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}};var jr=Rt,Fr="auto",Hr={"":!0,lr:!0,rl:!0},Vr={start:!0,middle:!0,end:!0,left:!0,right:!0};jt.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var qr=jt,Wr={"":!0,up:!0},Gr=Vt,zr=t(function(t){var e=t.exports={WebVTT:jr,VTTCue:qr,VTTRegion:Gr};ve.vttjs=e,ve.WebVTT=e.WebVTT;var i=e.VTTCue,r=e.VTTRegion,n=ve.VTTCue,s=ve.VTTRegion;e.shim=function(){ve.VTTCue=i,ve.VTTRegion=r},e.restore=function(){ve.VTTCue=n,ve.VTTRegion=s},ve.VTTCue||e.shim()}),Xr=function(t){function e(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};Ie(this,e),i.reportTouchActivity=!1;var n=De(this,t.call(this,null,i,r));return n.hasStarted_=!1,n.on("playing",function(){this.hasStarted_=!0}),n.on("loadstart",function(){this.hasStarted_=!1}),xr.names.forEach(function(t){var e=xr[t];i&&i[e.getterName]&&(n[e.privateName]=i[e.getterName])}),n.featuresProgressEvents||n.manualProgressOn(),n.featuresTimeupdateEvents||n.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(t){!1===i["native"+t+"Tracks"]&&(n["featuresNative"+t+"Tracks"]=!1)}),!1===i.nativeCaptions||!1===i.nativeTextTracks?n.featuresNativeTextTracks=!1:!0!==i.nativeCaptions&&!0!==i.nativeTextTracks||(n.featuresNativeTextTracks=!0),n.featuresNativeTextTracks||n.emulateTextTracks(),n.autoRemoteTextTracks_=new xr.text.ListClass,n.initTrackListeners(),i.nativeControlsForTouch||n.emitTapEvents(),n.constructor&&(n.name_=n.constructor.name||"Unknown Tech"),n}return xe(e,t),e.prototype.triggerSourceset=function(t){var e=this;this.isReady_||this.one("ready",function(){return e.setTimeout(function(){return e.triggerSourceset(t)},1)}),this.trigger({src:t,type:"sourceset"})},e.prototype.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},e.prototype.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},e.prototype.trackProgress=function(t){this.stopTrackingProgress(),this.progressInterval=this.setInterval(Qe(this,function(){var t=this.bufferedPercent();this.bufferedPercent_!==t&&this.trigger("progress"),this.bufferedPercent_=t,1===t&&this.stopTrackingProgress()}),500)},e.prototype.onDurationChange=function(t){this.duration_=this.duration()},e.prototype.buffered=function(){return nt(0,0)},e.prototype.bufferedPercent=function(){return st(this.buffered(),this.duration_)},e.prototype.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},e.prototype.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},e.prototype.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},e.prototype.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},e.prototype.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.prototype.dispose=function(){this.clearTracks(Pr.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),t.prototype.dispose.call(this)},e.prototype.clearTracks=function(t){var e=this;t=[].concat(t),t.forEach(function(t){for(var i=e[t+"Tracks"]()||[],r=i.length;r--;){var n=i[r];"text"===t&&e.removeRemoteTextTrack(n),i.removeTrack(n)}})},e.prototype.cleanupAutoTextTracks=function(){for(var t=this.autoRemoteTextTracks_||[],e=t.length;e--;){var i=t[e];this.removeRemoteTextTrack(i)}},e.prototype.reset=function(){},e.prototype.error=function(t){return void 0!==t&&(this.error_=new at(t),this.trigger("error")),this.error_},e.prototype.played=function(){return this.hasStarted_?nt(0,0):nt()},e.prototype.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},e.prototype.initTrackListeners=function(){var t=this;Pr.names.forEach(function(e){var i=Pr[e],r=function(){t.trigger(e+"trackchange")},n=t[i.getterName]();n.addEventListener("removetrack",r),n.addEventListener("addtrack",r),t.on("dispose",function(){n.removeEventListener("removetrack",r),n.removeEventListener("addtrack",r)})})},e.prototype.addWebVttScript_=function(){var t=this;if(!ve.WebVTT)if(ke.body.contains(this.el())){if(!this.options_["vtt.js"]&&a(zr)&&Object.keys(zr).length>0)return void this.trigger("vttjsloaded");var e=ke.createElement("script");e.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.12.4/vtt.min.js",e.onload=function(){t.trigger("vttjsloaded")},e.onerror=function(){t.trigger("vttjserror")},this.on("dispose",function(){e.onload=null,e.onerror=null}),ve.WebVTT=!0,this.el().parentNode.appendChild(e)}else this.ready(this.addWebVttScript_)},e.prototype.emulateTextTracks=function(){var t=this,e=this.textTracks(),i=this.remoteTextTracks(),r=function(t){return e.addTrack(t.track)},n=function(t){return e.removeTrack(t.track)};i.on("addtrack",r),i.on("removetrack",n),this.addWebVttScript_();var s=function(){return t.trigger("texttrackchange")},a=function(){s();for(var t=0;t<e.length;t++){var i=e[t];i.removeEventListener("cuechange",s),"showing"===i.mode&&i.addEventListener("cuechange",s)}};a(),e.addEventListener("change",a),e.addEventListener("addtrack",a),e.addEventListener("removetrack",a),this.on("dispose",function(){i.off("addtrack",r),i.off("removetrack",n),e.removeEventListener("change",a),e.removeEventListener("addtrack",a),e.removeEventListener("removetrack",a);for(var t=0;t<e.length;t++){e[t].removeEventListener("cuechange",s)}})},e.prototype.addTextTrack=function(t,e,i){if(!t)throw new Error("TextTrack kind is required but was not provided");return qt(this,t,e,i)},e.prototype.createRemoteTextTrack=function(t){var e=tt(t,{tech:this});return new Ir.remoteTextEl.TrackClass(e)},e.prototype.addRemoteTextTrack=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1],r=this.createRemoteTextTrack(e);return!0!==i&&!1!==i&&(Le.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),i=!0),this.remoteTextTrackEls().addTrackElement_(r),this.remoteTextTracks().addTrack(r.track),!0!==i&&this.ready(function(){return t.autoRemoteTextTracks_.addTrack(r.track)}),r},e.prototype.removeRemoteTextTrack=function(t){var e=this.remoteTextTrackEls().getTrackElementByTrack_(t);this.remoteTextTrackEls().removeTrackElement_(e),this.remoteTextTracks().removeTrack(t),this.autoRemoteTextTracks_.removeTrack(t)},e.prototype.getVideoPlaybackQuality=function(){return{}},e.prototype.setPoster=function(){},e.prototype.playsinline=function(){},e.prototype.setPlaysinline=function(){},e.prototype.canPlayType=function(){return""},e.canPlayType=function(){return""},e.canPlaySource=function(t,i){return e.canPlayType(t.type)},e.isTech=function(t){return t.prototype instanceof e||t instanceof e||t===e},e.registerTech=function(t,i){if(e.techs_||(e.techs_={}),!e.isTech(i))throw new Error("Tech "+t+" must be a Tech");if(!e.canPlayType)throw new Error("Techs must have a static canPlayType method on them");if(!e.canPlaySource)throw new Error("Techs must have a static canPlaySource method on them");return t=Q(t),e.techs_[t]=i,"Tech"!==t&&e.defaultTechOrder_.push(t),i},e.getTech=function(t){if(t)return t=Q(t),e.techs_&&e.techs_[t]?e.techs_[t]:ve&&ve.videojs&&ve.videojs[t]?(Le.warn("The "+t+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),ve.videojs[t]):void 0},e}(hi);xr.names.forEach(function(t){var e=xr[t];Xr.prototype[e.getterName]=function(){return this[e.privateName]=this[e.privateName]||new e.ListClass,this[e.privateName]}}),Xr.prototype.featuresVolumeControl=!0,Xr.prototype.featuresFullscreenResize=!1,Xr.prototype.featuresPlaybackRate=!1,Xr.prototype.featuresProgressEvents=!1,Xr.prototype.featuresSourceset=!1,Xr.prototype.featuresTimeupdateEvents=!1,Xr.prototype.featuresNativeTextTracks=!1,Xr.withSourceHandlers=function(t){t.registerSourceHandler=function(e,i){var r=t.sourceHandlers;r||(r=t.sourceHandlers=[]),void 0===i&&(i=r.length),r.splice(i,0,e)},t.canPlayType=function(e){for(var i=t.sourceHandlers||[],r=void 0,n=0;n<i.length;n++)if(r=i[n].canPlayType(e))return r;return""},t.selectSourceHandler=function(e,i){for(var r=t.sourceHandlers||[],n=0;n<r.length;n++)if(r[n].canHandleSource(e,i))return r[n];return null},t.canPlaySource=function(e,i){var r=t.selectSourceHandler(e,i);return r?r.canHandleSource(e,i):""},["seekable","duration"].forEach(function(t){var e=this[t];"function"==typeof e&&(this[t]=function(){return this.sourceHandler_&&this.sourceHandler_[t]?this.sourceHandler_[t].apply(this.sourceHandler_,arguments):e.apply(this,arguments)})},t.prototype),t.prototype.setSource=function(e){var i=t.selectSourceHandler(e,this.options_);i||(t.nativeSourceHandler?i=t.nativeSourceHandler:Le.error("No source hander found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),i!==t.nativeSourceHandler&&(this.currentSource_=e),this.sourceHandler_=i.handleSource(e,this,this.options_),this.on("dispose",this.disposeSourceHandler)},t.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},hi.registerComponent("Tech",Xr),Xr.registerTech("Tech",Xr),Xr.defaultTechOrder_=[];var Yr={},$r={},Kr={},Jr={buffered:1,currentTime:1,duration:1,seekable:1,played:1,paused:1},Qr={setCurrentTime:1},Zr={play:1,pause:1},tn={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",mp3:"audio/mpeg",aac:"audio/aac",oga:"audio/ogg",m3u8:"application/x-mpegURL"},en=function t(e){if(Array.isArray(e)){var i=[];e.forEach(function(e){e=t(e),Array.isArray(e)?i=i.concat(e):s(e)&&i.push(e)}),e=i}else e="string"==typeof e&&e.trim()?[ee({src:e})]:s(e)&&"string"==typeof e.src&&e.src&&e.src.trim()?[ee(e)]:[];return e},rn=function(t){function e(i,r,n){Ie(this,e);var s=tt({createEl:!1},r),a=De(this,t.call(this,i,s,n));if(r.playerOptions.sources&&0!==r.playerOptions.sources.length)i.src(r.playerOptions.sources);else for(var o=0,u=r.playerOptions.techOrder;o<u.length;o++){var l=Q(u[o]),c=Xr.getTech(l);if(l||(c=hi.getComponent(l)),c&&c.isSupported()){i.loadTech_(l);break}}return a}return xe(e,t),e}(hi);hi.registerComponent("MediaLoader",rn);var nn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.emitTapEvents(),n.enable(),n}return xe(e,t),e.prototype.createEl=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};i=n({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},i),"button"===e&&Le.error("Creating a ClickableComponent with an HTML element of "+e+" is not supported; use a Button instead."),r=n({role:"button","aria-live":"polite"},r),this.tabIndex_=i.tabIndex;var s=t.prototype.createEl.call(this,e,i,r);return this.createControlTextEl(s),s},e.prototype.dispose=function(){this.controlTextEl_=null,t.prototype.dispose.call(this)},e.prototype.createControlTextEl=function(t){return this.controlTextEl_=m("span",{className:"vjs-control-text"}),t&&t.appendChild(this.controlTextEl_),this.controlText(this.controlText_,t),this.controlTextEl_},e.prototype.controlText=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.el();if(void 0===t)return this.controlText_||"Need Text";var i=this.localize(t);this.controlText_=t,g(this.controlTextEl_,i),this.nonIconControl||e.setAttribute("title",i)},e.prototype.buildCSSClass=function(){return"vjs-control vjs-button "+t.prototype.buildCSSClass.call(this)},e.prototype.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),void 0!==this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur))},e.prototype.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),void 0!==this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off(["tap","click"],this.handleClick),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur)},e.prototype.handleClick=function(t){},e.prototype.handleFocus=function(t){G(ke,"keydown",Qe(this,this.handleKeyPress))},e.prototype.handleKeyPress=function(e){32===e.which||13===e.which?(e.preventDefault(),this.trigger("click")):t.prototype.handleKeyPress&&t.prototype.handleKeyPress.call(this,e)},e.prototype.handleBlur=function(t){z(ke,"keydown",Qe(this,this.handleKeyPress))},e}(hi);hi.registerComponent("ClickableComponent",nn);var sn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.update(),i.on("posterchange",Qe(n,n.update)),n}return xe(e,t),e.prototype.dispose=function(){this.player().off("posterchange",this.update),t.prototype.dispose.call(this)},e.prototype.createEl=function(){return m("div",{className:"vjs-poster",tabIndex:-1})},e.prototype.update=function(t){var e=this.player().poster();this.setSrc(e),e?this.show():this.hide()},e.prototype.setSrc=function(t){var e="";t&&(e='url("'+t+'")'),this.el_.style.backgroundImage=e},e.prototype.handleClick=function(t){this.player_.controls()&&(this.player_.paused()?this.player_.play():this.player_.pause())},e}(nn);hi.registerComponent("PosterImage",sn);var an={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'},on=function(t){function e(i,r,n){Ie(this,e);var s=De(this,t.call(this,i,r,n));return i.on("loadstart",Qe(s,s.toggleDisplay)),i.on("texttrackchange",Qe(s,s.updateDisplay)),i.on("loadstart",Qe(s,s.preselectTrack)),i.ready(Qe(s,function(){if(i.tech_&&i.tech_.featuresNativeTextTracks)return void this.hide();i.on("fullscreenchange",Qe(this,this.updateDisplay));for(var t=this.options_.playerOptions.tracks||[],e=0;e<t.length;e++)this.player_.addRemoteTextTrack(t[e],!0);this.preselectTrack()})),s}return xe(e,t),e.prototype.preselectTrack=function(){for(var t={captions:1,subtitles:1},e=this.player_.textTracks(),i=this.player_.cache_.selectedLanguage,r=void 0,n=void 0,s=void 0,a=0;a<e.length;a++){var o=e[a];i&&i.enabled&&i.language===o.language?o.kind===i.kind?s=o:s||(s=o):i&&!i.enabled?(s=null,r=null,n=null):o.default&&("descriptions"!==o.kind||r?o.kind in t&&!n&&(n=o):r=o)}s?s.mode="showing":n?n.mode="showing":r&&(r.mode="showing")},e.prototype.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},e.prototype.clearDisplay=function(){"function"==typeof ve.WebVTT&&ve.WebVTT.processCues(ve,[],this.el_)},e.prototype.updateDisplay=function(){var t=this.player_.textTracks();this.clearDisplay();for(var e=null,i=null,r=t.length;r--;){var n=t[r];"showing"===n.mode&&("descriptions"===n.kind?e=n:i=n)}i?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(i)):e&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(e))},e.prototype.updateForTrack=function(t){if("function"==typeof ve.WebVTT&&t.activeCues){for(var e=[],i=0;i<t.activeCues.length;i++)e.push(t.activeCues[i]);if(ve.WebVTT.processCues(ve,e,this.el_),this.player_.textTrackSettings)for(var r=this.player_.textTrackSettings.getValues(),n=e.length;n--;){var s=e[n];if(s){var a=s.displayState;if(r.color&&(a.firstChild.style.color=r.color),r.textOpacity&&re(a.firstChild,"color",ie(r.color||"#fff",r.textOpacity)),r.backgroundColor&&(a.firstChild.style.backgroundColor=r.backgroundColor),r.backgroundOpacity&&re(a.firstChild,"backgroundColor",ie(r.backgroundColor||"#000",r.backgroundOpacity)),r.windowColor&&(r.windowOpacity?re(a,"backgroundColor",ie(r.windowColor,r.windowOpacity)):a.style.backgroundColor=r.windowColor),r.edgeStyle&&("dropshadow"===r.edgeStyle?a.firstChild.style.textShadow="2px 2px 3px #222, 2px 2px 4px #222, 2px 2px 5px #222":"raised"===r.edgeStyle?a.firstChild.style.textShadow="1px 1px #222, 2px 2px #222, 3px 3px #222":"depressed"===r.edgeStyle?a.firstChild.style.textShadow="1px 1px #ccc, 0 1px #ccc, -1px -1px #222, 0 -1px #222":"uniform"===r.edgeStyle&&(a.firstChild.style.textShadow="0 0 4px #222, 0 0 4px #222, 0 0 4px #222, 0 0 4px #222")),r.fontPercent&&1!==r.fontPercent){var o=ve.parseFloat(a.style.fontSize);a.style.fontSize=o*r.fontPercent+"px",a.style.height="auto",a.style.top="auto",a.style.bottom="2px"}r.fontFamily&&"default"!==r.fontFamily&&("small-caps"===r.fontFamily?a.firstChild.style.fontVariant="small-caps":a.firstChild.style.fontFamily=an[r.fontFamily])}}}},e}(hi);hi.registerComponent("TextTrackDisplay",on);var un=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(){var e=this.player_.isAudio(),i=this.localize(e?"Audio Player":"Video Player"),r=m("span",{className:"vjs-control-text",innerHTML:this.localize("{1} is loading.",[i])}),n=t.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return n.appendChild(r),n},e}(hi);hi.registerComponent("LoadingSpinner",un);var ln=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t="button",e=n({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},e),i=n({type:"button","aria-live":"polite"},i);var r=hi.prototype.createEl.call(this,t,e,i);return this.createControlTextEl(r),r},e.prototype.addChild=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=this.constructor.name;return Le.warn("Adding an actionable (user controllable) child to a Button ("+i+") is not supported; use a ClickableComponent instead."),hi.prototype.addChild.call(this,t,e)},e.prototype.enable=function(){t.prototype.enable.call(this),this.el_.removeAttribute("disabled")},e.prototype.disable=function(){t.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},e.prototype.handleKeyPress=function(e){32!==e.which&&13!==e.which&&t.prototype.handleKeyPress.call(this,e)},e}(nn);hi.registerComponent("Button",ln);var cn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.mouseused_=!1,n.on("mousedown",n.handleMouseDown),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-big-play-button"},e.prototype.handleClick=function(t){var e=this.player_.play();if(!(this.mouseused_&&t.clientX&&t.clientY)){var i=this.player_.getChild("controlBar"),r=i&&i.getChild("playToggle");if(!r)return void this.player_.focus();var n=function(){return r.focus()};ut(e)?e.then(n,function(){}):this.setTimeout(n,1)}},e.prototype.handleKeyPress=function(e){this.mouseused_=!1,t.prototype.handleKeyPress.call(this,e)},e.prototype.handleMouseDown=function(t){this.mouseused_=!0},e}(ln);cn.prototype.controlText_="Play Video",hi.registerComponent("BigPlayButton",cn);var hn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.controlText(r&&r.controlText||n.localize("Close")),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-close-button "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){this.trigger({type:"close",bubbles:!1})},e}(ln);hi.registerComponent("CloseButton",hn);var dn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on(i,"play",n.handlePlay),n.on(i,"pause",n.handlePause),n.on(i,"ended",n.handleEnded),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-play-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){this.player_.paused()?this.player_.play():this.player_.pause()},e.prototype.handleSeeked=function(t){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(t):this.handlePlay(t)},e.prototype.handlePlay=function(t){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},e.prototype.handlePause=function(t){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},e.prototype.handleEnded=function(t){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},e}(ln);dn.prototype.controlText_="Play",hi.registerComponent("PlayToggle",dn);var pn=function(t,e){t=t<0?0:t;var i=Math.floor(t%60),r=Math.floor(t/60%60),n=Math.floor(t/3600),s=Math.floor(e/60%60),a=Math.floor(e/3600);return(isNaN(t)||t===1/0)&&(n=r=i="-"),n=n>0||a>0?n+":":"",r=((n||s>=10)&&r<10?"0"+r:r)+":",i=i<10?"0"+i:i,n+r+i},fn=pn,mn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t;return fn(t,e)},gn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.throttledUpdateContent=Ze(Qe(n,n.updateContent),25),n.on(i,"timeupdate",n.throttledUpdateContent),n}return xe(e,t),e.prototype.createEl=function(e){var i=this.buildCSSClass(),r=t.prototype.createEl.call(this,"div",{className:i+" vjs-time-control vjs-control"});return this.contentEl_=m("div",{className:i+"-display"},{"aria-live":"off"},m("span",{className:"vjs-control-text",
textContent:this.localize(this.controlText_)})),this.updateTextNode_(),r.appendChild(this.contentEl_),r},e.prototype.dispose=function(){this.contentEl_=null,this.textNode_=null,t.prototype.dispose.call(this)},e.prototype.updateTextNode_=function(){if(this.contentEl_){for(;this.contentEl_.firstChild;)this.contentEl_.removeChild(this.contentEl_.firstChild);this.textNode_=ke.createTextNode(this.formattedTime_||this.formatTime_(0)),this.contentEl_.appendChild(this.textNode_)}},e.prototype.formatTime_=function(t){return mn(t)},e.prototype.updateFormattedTime_=function(t){var e=this.formatTime_(t);e!==this.formattedTime_&&(this.formattedTime_=e,this.requestAnimationFrame(this.updateTextNode_))},e.prototype.updateContent=function(t){},e}(hi);gn.prototype.controlText_="Time",hi.registerComponent("TimeDisplay",gn);var yn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on(i,"ended",n.handleEnded),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-current-time"},e.prototype.updateContent=function(t){var e=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();this.updateFormattedTime_(e)},e.prototype.handleEnded=function(t){this.player_.duration()&&this.updateFormattedTime_(this.player_.duration())},e}(gn);yn.prototype.controlText_="Current Time",hi.registerComponent("CurrentTimeDisplay",yn);var vn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on(i,"durationchange",n.updateContent),n.on(i,"loadedmetadata",n.throttledUpdateContent),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-duration"},e.prototype.updateContent=function(t){var e=this.player_.duration();e&&this.duration_!==e&&(this.duration_=e,this.updateFormattedTime_(e))},e}(gn);vn.prototype.controlText_="Duration Time",hi.registerComponent("DurationDisplay",vn);var _n=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"})},e}(hi);hi.registerComponent("TimeDivider",_n);var bn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on(i,"durationchange",n.throttledUpdateContent),n.on(i,"ended",n.handleEnded),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-remaining-time"},e.prototype.formatTime_=function(e){return"-"+t.prototype.formatTime_.call(this,e)},e.prototype.updateContent=function(t){this.player_.duration()&&(this.player_.remainingTimeDisplay?this.updateFormattedTime_(this.player_.remainingTimeDisplay()):this.updateFormattedTime_(this.player_.remainingTime()))},e.prototype.handleEnded=function(t){this.player_.duration()&&this.updateFormattedTime_(0)},e}(gn);bn.prototype.controlText_="Remaining Time",hi.registerComponent("RemainingTimeDisplay",bn);var Tn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.updateShowing(),n.on(n.player(),"durationchange",n.updateShowing),n}return xe(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=m("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+"</span>"+this.localize("LIVE")},{"aria-live":"off"}),e.appendChild(this.contentEl_),e},e.prototype.dispose=function(){this.contentEl_=null,t.prototype.dispose.call(this)},e.prototype.updateShowing=function(t){this.player().duration()===1/0?this.show():this.hide()},e}(hi);hi.registerComponent("LiveDisplay",Tn);var Sn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.bar=n.getChild(n.options_.barName),n.vertical(!!n.options_.vertical),n.enable(),n}return xe(e,t),e.prototype.enabled=function(){return this.enabled_},e.prototype.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("focus",this.handleFocus),this.on("blur",this.handleBlur),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},e.prototype.disable=function(){if(this.enabled()){var t=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("focus",this.handleFocus),this.off("blur",this.handleBlur),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(t,"mousemove",this.handleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchmove",this.handleMouseMove),this.off(t,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},e.prototype.createEl=function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i.className=i.className+" vjs-slider",i=n({tabIndex:0},i),r=n({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},r),t.prototype.createEl.call(this,e,i,r)},e.prototype.handleMouseDown=function(t){var e=this.bar.el_.ownerDocument;t.preventDefault(),A(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(e,"mousemove",this.handleMouseMove),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchmove",this.handleMouseMove),this.on(e,"touchend",this.handleMouseUp),this.handleMouseMove(t)},e.prototype.handleMouseMove=function(t){},e.prototype.handleMouseUp=function(){var t=this.bar.el_.ownerDocument;L(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(t,"mousemove",this.handleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchmove",this.handleMouseMove),this.off(t,"touchend",this.handleMouseUp),this.update()},e.prototype.update=function(){if(this.el_){var t=this.getPercent(),e=this.bar;if(e){("number"!=typeof t||t!==t||t<0||t===1/0)&&(t=0);var i=(100*t).toFixed(2)+"%",r=e.el().style;return this.vertical()?r.height=i:r.width=i,t}}},e.prototype.calculateDistance=function(t){var e=I(this.el_,t);return this.vertical()?e.y:e.x},e.prototype.handleFocus=function(){this.on(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},e.prototype.handleKeyPress=function(t){37===t.which||40===t.which?(t.preventDefault(),this.stepBack()):38!==t.which&&39!==t.which||(t.preventDefault(),this.stepForward())},e.prototype.handleBlur=function(){this.off(this.bar.el_.ownerDocument,"keydown",this.handleKeyPress)},e.prototype.handleClick=function(t){t.stopImmediatePropagation(),t.preventDefault()},e.prototype.vertical=function(t){if(void 0===t)return this.vertical_||!1;this.vertical_=!!t,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},e}(hi);hi.registerComponent("Slider",Sn);var kn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.partEls_=[],n.on(i,"progress",n.update),n}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-load-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Loaded")+"</span>: 0%</span>"})},e.prototype.dispose=function(){this.partEls_=null,t.prototype.dispose.call(this)},e.prototype.update=function(t){var e=this.player_.buffered(),i=this.player_.duration(),r=this.player_.bufferedEnd(),n=this.partEls_,s=function(t,e){var i=t/e||0;return 100*(i>=1?1:i)+"%"};this.el_.style.width=s(r,i);for(var a=0;a<e.length;a++){var o=e.start(a),u=e.end(a),l=n[a];l||(l=this.el_.appendChild(m()),n[a]=l),l.style.left=s(o,r),l.style.width=s(u-o,r)}for(var c=n.length;c>e.length;c--)this.el_.removeChild(n[c-1]);n.length=e.length},e}(hi);hi.registerComponent("LoadProgressBar",kn);var En=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"})},e.prototype.update=function(t,e,i){var r=O(this.el_),n=O(this.player_.el()),s=t.width*e;if(n&&r){var a=t.left-n.left+s,o=t.width-s+(n.right-t.right),u=r.width/2;a<u?u+=u-a:o<u&&(u=o),u<0?u=0:u>r.width&&(u=r.width),this.el_.style.right="-"+u+"px",g(this.el_,i)}},e}(hi);hi.registerComponent("TimeTooltip",En);var Cn=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar",innerHTML:'<span class="vjs-control-text"><span>'+this.localize("Progress")+"</span>: 0%</span>"})},e.prototype.update=function(t,e){var i=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var r=i.player_.scrubbing()?i.player_.getCache().currentTime:i.player_.currentTime(),n=mn(r,i.player_.duration()),s=i.getChild("timeTooltip");s&&s.update(t,e,n)})},e}(hi);Cn.prototype.options_={children:[]},vi||bi||Cn.prototype.options_.children.push("timeTooltip"),hi.registerComponent("PlayProgressBar",Cn);var wn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.update=Ze(Qe(n,n.update),25),n}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},e.prototype.update=function(t,e){var i=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var r=i.player_.duration(),n=mn(e*r,r);i.el_.style.left=t.width*e+"px",i.getChild("timeTooltip").update(t,e,n)})},e}(hi);wn.prototype.options_={children:["timeTooltip"]},hi.registerComponent("MouseTimeDisplay",wn);var An=30,Ln=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.update=Ze(Qe(n,n.update),An),n.on(i,"timeupdate",n.update),n.on(i,"ended",n.handleEnded),n.updateInterval=null,n.on(i,["playing"],function(){n.clearInterval(n.updateInterval),n.updateInterval=n.setInterval(function(){n.requestAnimationFrame(function(){n.update()})},An)}),n.on(i,["ended","pause","waiting"],function(){n.clearInterval(n.updateInterval)}),n.on(i,["timeupdate","ended"],n.update),n}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},e.prototype.update_=function(t,e){var i=this.player_.duration();this.el_.setAttribute("aria-valuenow",(100*e).toFixed(2)),this.el_.setAttribute("aria-valuetext",this.localize("progress bar timing: currentTime={1} duration={2}",[mn(t,i),mn(i,i)],"{1} of {2}")),this.bar.update(O(this.el_),e)},e.prototype.update=function(e){var i=t.prototype.update.call(this);return this.update_(this.getCurrentTime_(),i),i},e.prototype.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},e.prototype.handleEnded=function(t){this.update_(this.player_.duration(),1)},e.prototype.getPercent=function(){var t=this.getCurrentTime_()/this.player_.duration();return t>=1?1:t},e.prototype.handleMouseDown=function(e){N(e)&&(e.stopPropagation(),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),t.prototype.handleMouseDown.call(this,e))},e.prototype.handleMouseMove=function(t){if(N(t)){var e=this.calculateDistance(t)*this.player_.duration();e===this.player_.duration()&&(e-=.1),this.player_.currentTime(e)}},e.prototype.enable=function(){t.prototype.enable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.show()},e.prototype.disable=function(){t.prototype.disable.call(this);var e=this.getChild("mouseTimeDisplay");e&&e.hide()},e.prototype.handleMouseUp=function(e){t.prototype.handleMouseUp.call(this,e),e&&e.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying&&lt(this.player_.play())},e.prototype.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+5)},e.prototype.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-5)},e.prototype.handleAction=function(t){this.player_.paused()?this.player_.play():this.player_.pause()},e.prototype.handleKeyPress=function(e){32===e.which||13===e.which?(e.preventDefault(),this.handleAction(e)):t.prototype.handleKeyPress&&t.prototype.handleKeyPress.call(this,e)},e}(Sn);Ln.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},vi||bi||Ln.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),Ln.prototype.playerEvent="timeupdate",hi.registerComponent("SeekBar",Ln);var On=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.handleMouseMove=Ze(Qe(n,n.handleMouseMove),25),n.throttledHandleMouseSeek=Ze(Qe(n,n.handleMouseSeek),25),n.enable(),n}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},e.prototype.handleMouseMove=function(t){var e=this.getChild("seekBar");if(e){var i=e.getChild("mouseTimeDisplay"),r=e.el(),n=O(r),s=I(r,t).x;s>1?s=1:s<0&&(s=0),i&&i.update(n,s)}},e.prototype.handleMouseSeek=function(t){var e=this.getChild("seekBar");e&&e.handleMouseMove(t)},e.prototype.enabled=function(){return this.enabled_},e.prototype.disable=function(){this.children().forEach(function(t){return t.disable&&t.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},e.prototype.enable=function(){this.children().forEach(function(t){return t.enable&&t.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},e.prototype.handleMouseDown=function(t){var e=this.el_.ownerDocument,i=this.getChild("seekBar");i&&i.handleMouseDown(t),this.on(e,"mousemove",this.throttledHandleMouseSeek),this.on(e,"touchmove",this.throttledHandleMouseSeek),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseUp=function(t){var e=this.el_.ownerDocument,i=this.getChild("seekBar");i&&i.handleMouseUp(t),this.off(e,"mousemove",this.throttledHandleMouseSeek),this.off(e,"touchmove",this.throttledHandleMouseSeek),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchend",this.handleMouseUp)},e}(hi);On.prototype.options_={children:["seekBar"]},hi.registerComponent("ProgressControl",On);var Pn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on(i,"fullscreenchange",n.handleFullscreenChange),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-fullscreen-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleFullscreenChange=function(t){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},e.prototype.handleClick=function(t){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},e}(ln);Pn.prototype.controlText_="Fullscreen",hi.registerComponent("FullscreenToggle",Pn);var In=function(t,e){e.tech_&&!e.tech_.featuresVolumeControl&&t.addClass("vjs-hidden"),t.on(e,"loadstart",function(){e.tech_.featuresVolumeControl?t.removeClass("vjs-hidden"):t.addClass("vjs-hidden")})},xn=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},e}(hi);hi.registerComponent("VolumeLevel",xn);var Dn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on("slideractive",n.updateLastVolume_),n.on(i,"volumechange",n.updateARIAAttributes),i.ready(function(){return n.updateARIAAttributes()}),n}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},e.prototype.handleMouseDown=function(e){N(e)&&t.prototype.handleMouseDown.call(this,e)},e.prototype.handleMouseMove=function(t){N(t)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(t)))},e.prototype.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},e.prototype.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},e.prototype.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},e.prototype.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},e.prototype.updateARIAAttributes=function(t){var e=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",e),this.el_.setAttribute("aria-valuetext",e+"%")},e.prototype.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},e.prototype.updateLastVolume_=function(){var t=this,e=this.player_.volume();this.one("sliderinactive",function(){0===t.player_.volume()&&t.player_.lastVolume_(e)})},e}(Sn);Dn.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},Dn.prototype.playerEvent="volumechange",hi.registerComponent("VolumeBar",Dn);var Mn=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ie(this,e),r.vertical=r.vertical||!1,(void 0===r.volumeBar||a(r.volumeBar))&&(r.volumeBar=r.volumeBar||{},r.volumeBar.vertical=r.vertical);var n=De(this,t.call(this,i,r));return In(n,i),n.throttledHandleMouseMove=Ze(Qe(n,n.handleMouseMove),25),n.on("mousedown",n.handleMouseDown),n.on("touchstart",n.handleMouseDown),n.on(n.volumeBar,["focus","slideractive"],function(){n.volumeBar.addClass("vjs-slider-active"),n.addClass("vjs-slider-active"),n.trigger("slideractive")}),n.on(n.volumeBar,["blur","sliderinactive"],function(){n.volumeBar.removeClass("vjs-slider-active"),n.removeClass("vjs-slider-active"),n.trigger("sliderinactive")}),n}return xe(e,t),e.prototype.createEl=function(){var e="vjs-volume-horizontal";return this.options_.vertical&&(e="vjs-volume-vertical"),t.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+e})},e.prototype.handleMouseDown=function(t){var e=this.el_.ownerDocument;this.on(e,"mousemove",this.throttledHandleMouseMove),this.on(e,"touchmove",this.throttledHandleMouseMove),this.on(e,"mouseup",this.handleMouseUp),this.on(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseUp=function(t){var e=this.el_.ownerDocument;this.off(e,"mousemove",this.throttledHandleMouseMove),this.off(e,"touchmove",this.throttledHandleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchend",this.handleMouseUp)},e.prototype.handleMouseMove=function(t){this.volumeBar.handleMouseMove(t)},e}(hi);Mn.prototype.options_={children:["volumeBar"]},hi.registerComponent("VolumeControl",Mn);var Rn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return In(n,i),n.on(i,["loadstart","volumechange"],n.update),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-mute-control "+t.prototype.buildCSSClass.call(this)},e.prototype.handleClick=function(t){var e=this.player_.volume(),i=this.player_.lastVolume_();if(0===e){var r=i<.1?.1:i;this.player_.volume(r),this.player_.muted(!1)}else this.player_.muted(!this.player_.muted())},e.prototype.update=function(t){this.updateIcon_(),this.updateControlText_()},e.prototype.updateIcon_=function(){var t=this.player_.volume(),e=3;0===t||this.player_.muted()?e=0:t<.33?e=1:t<.67&&(e=2);for(var i=0;i<4;i++)b(this.el_,"vjs-vol-"+i);_(this.el_,"vjs-vol-"+e)},e.prototype.updateControlText_=function(){var t=this.player_.muted()||0===this.player_.volume(),e=t?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},e}(ln);Rn.prototype.controlText_="Mute",hi.registerComponent("MuteToggle",Rn);var Un=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ie(this,e),void 0!==r.inline?r.inline=r.inline:r.inline=!0,(void 0===r.volumeControl||a(r.volumeControl))&&(r.volumeControl=r.volumeControl||{},r.volumeControl.vertical=!r.inline);var n=De(this,t.call(this,i,r));return In(n,i),n.on(n.volumeControl,["slideractive"],n.sliderActive_),n.on(n.volumeControl,["sliderinactive"],n.sliderInactive_),n}return xe(e,t),e.prototype.sliderActive_=function(){this.addClass("vjs-slider-active")},e.prototype.sliderInactive_=function(){this.removeClass("vjs-slider-active")},e.prototype.createEl=function(){var e="vjs-volume-panel-horizontal";return this.options_.inline||(e="vjs-volume-panel-vertical"),t.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+e})},e}(hi);Un.prototype.options_={children:["muteToggle","volumeControl"]},hi.registerComponent("VolumePanel",Un);var Nn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return r&&(n.menuButton_=r.menuButton),n.focusedChild_=-1,n.on("keydown",n.handleKeyPress),n}return xe(e,t),e.prototype.addItem=function(t){this.addChild(t),t.on("click",Qe(this,function(e){this.menuButton_&&(this.menuButton_.unpressButton(),"CaptionSettingsMenuItem"!==t.name()&&this.menuButton_.focus())}))},e.prototype.createEl=function(){var e=this.options_.contentElType||"ul";this.contentEl_=m(e,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var i=t.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return i.appendChild(this.contentEl_),G(i,"click",function(t){t.preventDefault(),t.stopImmediatePropagation()}),i},e.prototype.dispose=function(){this.contentEl_=null,t.prototype.dispose.call(this)},e.prototype.handleKeyPress=function(t){37===t.which||40===t.which?(t.preventDefault(),this.stepForward()):38!==t.which&&39!==t.which||(t.preventDefault(),this.stepBack())},e.prototype.stepForward=function(){var t=0;void 0!==this.focusedChild_&&(t=this.focusedChild_+1),this.focus(t)},e.prototype.stepBack=function(){var t=0;void 0!==this.focusedChild_&&(t=this.focusedChild_-1),this.focus(t)},e.prototype.focus=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=this.children().slice();e.length&&e[0].className&&/vjs-menu-title/.test(e[0].className)&&e.shift(),e.length>0&&(t<0?t=0:t>=e.length&&(t=e.length-1),this.focusedChild_=t,e[t].el_.focus())},e}(hi);hi.registerComponent("Menu",Nn);var Bn=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ie(this,e);var n=De(this,t.call(this,i,r));n.menuButton_=new ln(i,r),n.menuButton_.controlText(n.controlText_),n.menuButton_.el_.setAttribute("aria-haspopup","true");var s=ln.prototype.buildCSSClass();return n.menuButton_.el_.className=n.buildCSSClass()+" "+s,n.menuButton_.removeClass("vjs-control"),n.addChild(n.menuButton_),n.update(),n.enabled_=!0,n.on(n.menuButton_,"tap",n.handleClick),n.on(n.menuButton_,"click",n.handleClick),n.on(n.menuButton_,"focus",n.handleFocus),n.on(n.menuButton_,"blur",n.handleBlur),n.on("keydown",n.handleSubmenuKeyPress),n}return xe(e,t),e.prototype.update=function(){var t=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=t,this.addChild(t),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},e.prototype.createMenu=function(){var t=new Nn(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var e=m("li",{className:"vjs-menu-title",innerHTML:Q(this.options_.title),tabIndex:-1});this.hideThreshold_+=1,t.children_.unshift(e),y(e,t.contentEl())}if(this.items=this.createItems(),this.items)for(var i=0;i<this.items.length;i++)t.addItem(this.items[i]);return t},e.prototype.createItems=function(){},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},e.prototype.buildWrapperCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+ln.prototype.buildCSSClass()+" "+t.prototype.buildCSSClass.call(this)},e.prototype.buildCSSClass=function(){var e="vjs-menu-button";return!0===this.options_.inline?e+="-inline":e+="-popup","vjs-menu-button "+e+" "+t.prototype.buildCSSClass.call(this)},e.prototype.controlText=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.menuButton_.el();return this.menuButton_.controlText(t,e)},e.prototype.handleClick=function(t){this.one(this.menu.contentEl(),"mouseleave",Qe(this,function(t){this.unpressButton(),this.el_.blur()})),this.buttonPressed_?this.unpressButton():this.pressButton()},e.prototype.focus=function(){this.menuButton_.focus()},e.prototype.blur=function(){this.menuButton_.blur()},e.prototype.handleFocus=function(){G(ke,"keydown",Qe(this,this.handleKeyPress))},e.prototype.handleBlur=function(){z(ke,"keydown",Qe(this,this.handleKeyPress))},e.prototype.handleKeyPress=function(t){27===t.which||9===t.which?(this.buttonPressed_&&this.unpressButton(),9!==t.which&&(t.preventDefault(),this.menuButton_.el_.focus())):38!==t.which&&40!==t.which||this.buttonPressed_||(this.pressButton(),t.preventDefault())},e.prototype.handleSubmenuKeyPress=function(t){27!==t.which&&9!==t.which||(this.buttonPressed_&&this.unpressButton(),9!==t.which&&(t.preventDefault(),this.menuButton_.el_.focus()))},e.prototype.pressButton=function(){if(this.enabled_){if(this.buttonPressed_=!0,this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),vi&&p())return;this.menu.focus()}},e.prototype.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},e.prototype.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},e.prototype.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},e}(hi);hi.registerComponent("MenuButton",Bn);var jn=function(t){function e(i,r){Ie(this,e);var n=r.tracks,s=De(this,t.call(this,i,r));if(s.items.length<=1&&s.hide(),!n)return De(s);var a=Qe(s,s.update);return n.addEventListener("removetrack",a),n.addEventListener("addtrack",a),s.player_.on("ready",a),s.player_.on("dispose",function(){n.removeEventListener("removetrack",a),n.removeEventListener("addtrack",a)}),s}return xe(e,t),e}(Bn);hi.registerComponent("TrackButton",jn);var Fn=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.selectable=r.selectable,n.isSelected_=r.selected||!1,n.selected(n.isSelected_),n.selectable?n.el_.setAttribute("role","menuitemcheckbox"):n.el_.setAttribute("role","menuitem"),n}return xe(e,t),e.prototype.createEl=function(e,i,r){return this.nonIconControl=!0,t.prototype.createEl.call(this,"li",n({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},i),r)},e.prototype.handleClick=function(t){this.selected(!0)},e.prototype.selected=function(t){this.selectable&&(t?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},e}(nn);hi.registerComponent("MenuItem",Fn);var Hn=function(t){function e(i,r){Ie(this,e);var n=r.track,s=i.textTracks();r.label=n.label||n.language||"Unknown",r.selected="showing"===n.mode;var a=De(this,t.call(this,i,r));a.track=n;var o=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];a.handleTracksChange.apply(a,e)},u=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];a.handleSelectedLanguageChange.apply(a,e)};if(i.on(["loadstart","texttrackchange"],o),s.addEventListener("change",o),s.addEventListener("selectedlanguagechange",u),a.on("dispose",function(){i.off(["loadstart","texttrackchange"],o),s.removeEventListener("change",o),s.removeEventListener("selectedlanguagechange",u)}),void 0===s.onchange){var l=void 0;a.on(["tap","click"],function(){if("object"!==Pe(ve.Event))try{l=new ve.Event("change")}catch(t){}l||(l=ke.createEvent("Event"),l.initEvent("change",!0,!0)),s.dispatchEvent(l)})}return a.handleTracksChange(),a}return xe(e,t),e.prototype.handleClick=function(e){var i=this.track.kind,r=this.track.kinds,n=this.player_.textTracks();if(r||(r=[i]),t.prototype.handleClick.call(this,e),n)for(var s=0;s<n.length;s++){var a=n[s];a===this.track&&r.indexOf(a.kind)>-1?"showing"!==a.mode&&(a.mode="showing"):"disabled"!==a.mode&&(a.mode="disabled")}},e.prototype.handleTracksChange=function(t){var e="showing"===this.track.mode;e!==this.isSelected_&&this.selected(e)},e.prototype.handleSelectedLanguageChange=function(t){if("showing"===this.track.mode){var e=this.player_.cache_.selectedLanguage;if(e&&e.enabled&&e.language===this.track.language&&e.kind!==this.track.kind)return;this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind}}},e.prototype.dispose=function(){this.track=null,t.prototype.dispose.call(this)},e}(Fn);hi.registerComponent("TextTrackMenuItem",Hn);var Vn=function(t){function e(i,r){return Ie(this,e),r.track={player:i,kind:r.kind,kinds:r.kinds,default:!1,mode:"disabled"},r.kinds||(r.kinds=[r.kind]),r.label?r.track.label=r.label:r.track.label=r.kinds.join(" and ")+" off",r.selectable=!0,De(this,t.call(this,i,r))}return xe(e,t),e.prototype.handleTracksChange=function(t){for(var e=this.player().textTracks(),i=!0,r=0,n=e.length;r<n;r++){var s=e[r];if(this.options_.kinds.indexOf(s.kind)>-1&&"showing"===s.mode){i=!1;break}}i!==this.isSelected_&&this.selected(i)},e.prototype.handleSelectedLanguageChange=function(t){for(var e=this.player().textTracks(),i=!0,r=0,n=e.length;r<n;r++){var s=e[r];if(["captions","descriptions","subtitles"].indexOf(s.kind)>-1&&"showing"===s.mode){i=!1;break}}i&&(this.player_.cache_.selectedLanguage={enabled:!1})},e}(Hn);hi.registerComponent("OffTextTrackMenuItem",Vn);var qn=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Ie(this,e),r.tracks=i.textTracks(),De(this,t.call(this,i,r))}return xe(e,t),e.prototype.createItems=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Hn,i=void 0;this.label_&&(i=this.label_+" off"),t.push(new Vn(this.player_,{kinds:this.kinds_,kind:this.kind_,label:i})),this.hideThreshold_+=1;var r=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var n=0;n<r.length;n++){var s=r[n];if(this.kinds_.indexOf(s.kind)>-1){var a=new e(this.player_,{track:s,selectable:!0});a.addClass("vjs-"+s.kind+"-menu-item"),t.push(a)}}return t},e}(jn);hi.registerComponent("TextTrackButton",qn);var Wn=function(t){function e(i,r){Ie(this,e);var n=r.track,s=r.cue,a=i.currentTime();r.selectable=!0,r.label=s.text,r.selected=s.startTime<=a&&a<s.endTime;var o=De(this,t.call(this,i,r));return o.track=n,o.cue=s,n.addEventListener("cuechange",Qe(o,o.update)),o}return xe(e,t),e.prototype.handleClick=function(e){t.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},e.prototype.update=function(t){var e=this.cue,i=this.player_.currentTime();this.selected(e.startTime<=i&&i<e.endTime)},e}(Fn);hi.registerComponent("ChaptersTrackMenuItem",Wn);var Gn=function(t){function e(i,r,n){return Ie(this,e),De(this,t.call(this,i,r,n))}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-chapters-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-chapters-button "+t.prototype.buildWrapperCSSClass.call(this)},
e.prototype.update=function(e){this.track_&&(!e||"addtrack"!==e.type&&"removetrack"!==e.type)||this.setTrack(this.findChaptersTrack()),t.prototype.update.call(this)},e.prototype.setTrack=function(t){if(this.track_!==t){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var e=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);e&&e.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=t,this.track_){this.track_.mode="hidden";var i=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);i&&i.addEventListener("load",this.updateHandler_)}}},e.prototype.findChaptersTrack=function(){for(var t=this.player_.textTracks()||[],e=t.length-1;e>=0;e--){var i=t[e];if(i.kind===this.kind_)return i}},e.prototype.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(Q(this.kind_))},e.prototype.createMenu=function(){return this.options_.title=this.getMenuCaption(),t.prototype.createMenu.call(this)},e.prototype.createItems=function(){var t=[];if(!this.track_)return t;var e=this.track_.cues;if(!e)return t;for(var i=0,r=e.length;i<r;i++){var n=e[i],s=new Wn(this.player_,{track:this.track_,cue:n});t.push(s)}return t},e}(qn);Gn.prototype.kind_="chapters",Gn.prototype.controlText_="Chapters",hi.registerComponent("ChaptersButton",Gn);var zn=function(t){function e(i,r,n){Ie(this,e);var s=De(this,t.call(this,i,r,n)),a=i.textTracks(),o=Qe(s,s.handleTracksChange);return a.addEventListener("change",o),s.on("dispose",function(){a.removeEventListener("change",o)}),s}return xe(e,t),e.prototype.handleTracksChange=function(t){for(var e=this.player().textTracks(),i=!1,r=0,n=e.length;r<n;r++){var s=e[r];if(s.kind!==this.kind_&&"showing"===s.mode){i=!0;break}}i?this.disable():this.enable()},e.prototype.buildCSSClass=function(){return"vjs-descriptions-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+t.prototype.buildWrapperCSSClass.call(this)},e}(qn);zn.prototype.kind_="descriptions",zn.prototype.controlText_="Descriptions",hi.registerComponent("DescriptionsButton",zn);var Xn=function(t){function e(i,r,n){return Ie(this,e),De(this,t.call(this,i,r,n))}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-subtitles-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+t.prototype.buildWrapperCSSClass.call(this)},e}(qn);Xn.prototype.kind_="subtitles",Xn.prototype.controlText_="Subtitles",hi.registerComponent("SubtitlesButton",Xn);var Yn=function(t){function e(i,r){Ie(this,e),r.track={player:i,kind:r.kind,label:r.kind+" settings",selectable:!1,default:!1,mode:"disabled"},r.selectable=!1,r.name="CaptionSettingsMenuItem";var n=De(this,t.call(this,i,r));return n.addClass("vjs-texttrack-settings"),n.controlText(", opens "+r.kind+" settings dialog"),n}return xe(e,t),e.prototype.handleClick=function(t){this.player().getChild("textTrackSettings").open()},e}(Hn);hi.registerComponent("CaptionSettingsMenuItem",Yn);var $n=function(t){function e(i,r,n){return Ie(this,e),De(this,t.call(this,i,r,n))}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-captions-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-captions-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new Yn(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),t.prototype.createItems.call(this,e)},e}(qn);$n.prototype.kind_="captions",$n.prototype.controlText_="Captions",hi.registerComponent("CaptionsButton",$n);var Kn=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(e,i,r){var s='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"captions"===this.options_.track.kind&&(s+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Captions")+"</span>\n      "),s+="</span>",t.prototype.createEl.call(this,e,n({innerHTML:s},i),r)},e}(Hn);hi.registerComponent("SubsCapsMenuItem",Kn);var Jn=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ie(this,e);var n=De(this,t.call(this,i,r));return n.label_="subtitles",["en","en-us","en-ca","fr-ca"].indexOf(n.player_.language_)>-1&&(n.label_="captions"),n.menuButton_.controlText(Q(n.label_)),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-subs-caps-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var e=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(e.push(new Yn(this.player_,{kind:this.label_})),this.hideThreshold_+=1),e=t.prototype.createItems.call(this,e,Kn)},e}(qn);Jn.prototype.kinds_=["captions","subtitles"],Jn.prototype.controlText_="Subtitles",hi.registerComponent("SubsCapsButton",Jn);var Qn=function(t){function e(i,r){Ie(this,e);var n=r.track,s=i.audioTracks();r.label=n.label||n.language||"Unknown",r.selected=n.enabled;var a=De(this,t.call(this,i,r));a.track=n;var o=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];a.handleTracksChange.apply(a,e)};return s.addEventListener("change",o),a.on("dispose",function(){s.removeEventListener("change",o)}),a}return xe(e,t),e.prototype.handleClick=function(e){var i=this.player_.audioTracks();t.prototype.handleClick.call(this,e);for(var r=0;r<i.length;r++){var n=i[r];n.enabled=n===this.track}},e.prototype.handleTracksChange=function(t){this.selected(this.track.enabled)},e}(Fn);hi.registerComponent("AudioTrackMenuItem",Qn);var Zn=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Ie(this,e),r.tracks=i.audioTracks(),De(this,t.call(this,i,r))}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-audio-button "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-audio-button "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createItems=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.hideThreshold_=1;for(var e=this.player_.audioTracks(),i=0;i<e.length;i++){var r=e[i];t.push(new Qn(this.player_,{track:r,selectable:!0}))}return t},e}(jn);Zn.prototype.controlText_="Audio Track",hi.registerComponent("AudioTrackButton",Zn);var ts=function(t){function e(i,r){Ie(this,e);var n=r.rate,s=parseFloat(n,10);r.label=n,r.selected=1===s,r.selectable=!0;var a=De(this,t.call(this,i,r));return a.label=n,a.rate=s,a.on(i,"ratechange",a.update),a}return xe(e,t),e.prototype.handleClick=function(e){t.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},e.prototype.update=function(t){this.selected(this.player().playbackRate()===this.rate)},e}(Fn);ts.prototype.contentElType="button",hi.registerComponent("PlaybackRateMenuItem",ts);var es=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.updateVisibility(),n.updateLabel(),n.on(i,"loadstart",n.updateVisibility),n.on(i,"ratechange",n.updateLabel),n}return xe(e,t),e.prototype.createEl=function(){var e=t.prototype.createEl.call(this);return this.labelEl_=m("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),e.appendChild(this.labelEl_),e},e.prototype.dispose=function(){this.labelEl_=null,t.prototype.dispose.call(this)},e.prototype.buildCSSClass=function(){return"vjs-playback-rate "+t.prototype.buildCSSClass.call(this)},e.prototype.buildWrapperCSSClass=function(){return"vjs-playback-rate "+t.prototype.buildWrapperCSSClass.call(this)},e.prototype.createMenu=function(){var t=new Nn(this.player()),e=this.playbackRates();if(e)for(var i=e.length-1;i>=0;i--)t.addChild(new ts(this.player(),{rate:e[i]+"x"}));return t},e.prototype.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},e.prototype.handleClick=function(t){for(var e=this.player().playbackRate(),i=this.playbackRates(),r=i[0],n=0;n<i.length;n++)if(i[n]>e){r=i[n];break}this.player().playbackRate(r)},e.prototype.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},e.prototype.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&this.playbackRates().length>0},e.prototype.updateVisibility=function(t){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},e.prototype.updateLabel=function(t){this.playbackRateSupported()&&(this.labelEl_.innerHTML=this.player().playbackRate()+"x")},e}(Bn);es.prototype.controlText_="Playback Rate",hi.registerComponent("PlaybackRateMenuButton",es);var is=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-spacer "+t.prototype.buildCSSClass.call(this)},e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},e}(hi);hi.registerComponent("Spacer",is);var rs=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-custom-control-spacer "+t.prototype.buildCSSClass.call(this)},e.prototype.createEl=function(){var e=t.prototype.createEl.call(this,{className:this.buildCSSClass()});return e.innerHTML=" ",e},e}(is);hi.registerComponent("CustomControlSpacer",rs);var ns=function(t){function e(){return Ie(this,e),De(this,t.apply(this,arguments))}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"},{role:"group"})},e}(hi);ns.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},hi.registerComponent("ControlBar",ns);var ss=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));return n.on(i,"error",n.open),n}return xe(e,t),e.prototype.buildCSSClass=function(){return"vjs-error-display "+t.prototype.buildCSSClass.call(this)},e.prototype.content=function(){var t=this.player().error();return t?this.localize(t.message):""},e}(Gi);ss.prototype.options_=tt(Gi.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),hi.registerComponent("ErrorDisplay",ss);var as=["#000","Black"],os=["#00F","Blue"],us=["#0FF","Cyan"],ls=["#0F0","Green"],cs=["#F0F","Magenta"],hs=["#F00","Red"],ds=["#FFF","White"],ps=["#FF0","Yellow"],fs=["1","Opaque"],ms=["0.5","Semi-Transparent"],gs=["0","Transparent"],ys={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[as,ds,hs,ls,os,ps,cs,us]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[fs,ms,gs]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[ds,as,hs,ls,os,ps,cs,us]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],default:2,parser:function(t){return"1.00"===t?null:Number(t)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[fs,ms]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[gs,ms,fs]}};ys.windowColor.options=ys.backgroundColor.options;var vs=function(t){function e(r,n){Ie(this,e),n.temporary=!1;var s=De(this,t.call(this,r,n));return s.updateDisplay=Qe(s,s.updateDisplay),s.fill(),s.hasBeenOpened_=s.hasBeenFilled_=!0,s.endDialog=m("p",{className:"vjs-control-text",textContent:s.localize("End of dialog window.")}),s.el().appendChild(s.endDialog),s.setDefaults(),void 0===n.persistTextTrackSettings&&(s.options_.persistTextTrackSettings=s.options_.playerOptions.persistTextTrackSettings),s.on(s.$(".vjs-done-button"),"click",function(){s.saveSettings(),s.close()}),s.on(s.$(".vjs-default-button"),"click",function(){s.setDefaults(),s.updateDisplay()}),i(ys,function(t){s.on(s.$(t.selector),"change",s.updateDisplay)}),s.options_.persistTextTrackSettings&&s.restoreSettings(),s}return xe(e,t),e.prototype.dispose=function(){this.endDialog=null,t.prototype.dispose.call(this)},e.prototype.createElSelect_=function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"label",n=ys[t],s=n.id.replace("%s",this.id_);return["<"+r+' id="'+s+'" class="'+("label"===r?"vjs-label":"")+'">',this.localize(n.label),"</"+r+">",'<select aria-labelledby="'+(""!==i?i+" ":"")+s+'">'].concat(n.options.map(function(t){var r=s+"-"+t[1];return['<option id="'+r+'" value="'+t[0]+'" ','aria-labelledby="'+(""!==i?i+" ":"")+s+" "+r+'">',e.localize(t[1]),"</option>"].join("")})).concat("</select>").join("")},e.prototype.createElFgColor_=function(){var t="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",t),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElBgColor_=function(){var t="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",t),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElWinColor_=function(){var t="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+t+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",t),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",t),"</span>","</fieldset>"].join("")},e.prototype.createElColors_=function(){return m("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},e.prototype.createElFont_=function(){return m("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},e.prototype.createElControls_=function(){var t=this.localize("restore all settings to the default values");return m("div",{className:"vjs-track-settings-controls",innerHTML:['<button class="vjs-default-button" title="'+t+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+t+"</span>","</button>",'<button class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},e.prototype.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},e.prototype.label=function(){return this.localize("Caption Settings Dialog")},e.prototype.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},e.prototype.buildCSSClass=function(){return t.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},e.prototype.getValues=function(){var t=this;return r(ys,function(e,i,r){var n=oe(t.$(i.selector),i.parser);return void 0!==n&&(e[r]=n),e},{})},e.prototype.setValues=function(t){var e=this;i(ys,function(i,r){ue(e.$(i.selector),t[r],i.parser)})},e.prototype.setDefaults=function(){var t=this;i(ys,function(e){var i=e.hasOwnProperty("default")?e.default:0;t.$(e.selector).selectedIndex=i})},e.prototype.restoreSettings=function(){var t=void 0;try{t=JSON.parse(ve.localStorage.getItem("vjs-text-track-settings"))}catch(t){Le.warn(t)}t&&this.setValues(t)},e.prototype.saveSettings=function(){if(this.options_.persistTextTrackSettings){var t=this.getValues();try{Object.keys(t).length?ve.localStorage.setItem("vjs-text-track-settings",JSON.stringify(t)):ve.localStorage.removeItem("vjs-text-track-settings")}catch(t){Le.warn(t)}}},e.prototype.updateDisplay=function(){var t=this.player_.getChild("textTrackDisplay");t&&t.updateDisplay()},e.prototype.conditionalBlur_=function(){this.previouslyActiveEl_=null,this.off(ke,"keydown",this.handleKeyDown);var t=this.player_.controlBar,e=t&&t.subsCapsButton,i=t&&t.captionsButton;e?e.focus():i&&i.focus()},e}(Gi);hi.registerComponent("TextTrackSettings",vs);var _s=function(t){function e(i,r){Ie(this,e);var n=r.ResizeObserver||ve.ResizeObserver;null===r.ResizeObserver&&(n=!1);var s=tt({createEl:!n},r),a=De(this,t.call(this,i,s));return a.ResizeObserver=r.ResizeObserver||ve.ResizeObserver,a.loadListener_=null,a.resizeObserver_=null,a.debouncedHandler_=ti(function(){a.resizeHandler()},100,!1,i),n?(a.resizeObserver_=new a.ResizeObserver(a.debouncedHandler_),a.resizeObserver_.observe(i.el())):(a.loadListener_=function(){a.el_.contentWindow&&G(a.el_.contentWindow,"resize",a.debouncedHandler_),a.off("load",a.loadListener_)},a.on("load",a.loadListener_)),a}return xe(e,t),e.prototype.createEl=function(){return t.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager"})},e.prototype.resizeHandler=function(){this.player_.trigger("playerresize")},e.prototype.dispose=function(){this.resizeObserver_&&(this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.el_&&this.el_.contentWindow&&z(this.el_.contentWindow,"resize",this.debouncedHandler_),this.loadListener_&&this.off("load",this.loadListener_),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null},e}(hi);hi.registerComponent("ResizeManager",_s);var bs=function(t){if(t.featuresSourceset){var e=t.el();(e.src||e.currentSrc&&3!==t.el().initNetworkState_)&&t.triggerSourceset(e.src||e.currentSrc);var i=ve.HTMLMediaElement.prototype,r={};Object.getOwnPropertyDescriptor(e,"src")?r=Object.getOwnPropertyDescriptor(e,"src"):Object.getOwnPropertyDescriptor(i,"src")&&(r=tt(r,Object.getOwnPropertyDescriptor(i,"src"))),r.get||(r.get=function(){return i.getAttribute.call(e,"src")}),r.set||(r.set=function(t){return i.setAttribute.call(e,"src",t)}),void 0===r.enumerable&&(r.enumerable=!0),Object.defineProperty(e,"src",{get:r.get.bind(e),set:function(i){var n=r.set.call(e,i);return t.triggerSourceset(i),n},configurable:!0,enumerable:r.enumerable});var n=e.setAttribute;e.setAttribute=function(i,r){var s=n.call(e,i,r);return"src"===i&&t.triggerSourceset(r),s};var s=e.load;e.load=function(){var i=s.call(e);return t.triggerSourceset(e.src||""),i}}},Ts=Me(["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."],["Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\n            This may prevent text tracks from loading."]),Ss=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,i,r));i.enableSourceset&&n.setupSourcesetHandling_();var s=i.source,a=!1;if(s&&(n.el_.currentSrc!==s.src||i.tag&&3===i.tag.initNetworkState_)?n.setSource(s):n.handleLateInit_(n.el_),n.el_.hasChildNodes()){for(var o=n.el_.childNodes,u=o.length,l=[];u--;){var c=o[u];"track"===c.nodeName.toLowerCase()&&(n.featuresNativeTextTracks?(n.remoteTextTrackEls().addTrackElement_(c),n.remoteTextTracks().addTrack(c.track),n.textTracks().addTrack(c.track),a||n.el_.hasAttribute("crossorigin")||!lr(c.src)||(a=!0)):l.push(c))}for(var h=0;h<l.length;h++)n.el_.removeChild(l[h])}return n.proxyNativeTracks_(),n.featuresNativeTextTracks&&a&&Le.warn(Oe(Ts)),n.restoreMetadataTracksInIOSNativePlayer_(),(Pi||gi||Si)&&!0===i.nativeControlsForTouch&&n.setControls(!0),n.proxyWebkitFullscreen_(),n.triggerReady(),n}return xe(e,t),e.prototype.dispose=function(){e.disposeMediaElement(this.el_),this.options_=null,t.prototype.dispose.call(this)},e.prototype.setupSourcesetHandling_=function(){bs(this)},e.prototype.restoreMetadataTracksInIOSNativePlayer_=function(){var t=this.textTracks(),e=void 0,i=function(){e=[];for(var i=0;i<t.length;i++){var r=t[i];"metadata"===r.kind&&e.push({track:r,storedMode:r.mode})}};i(),t.addEventListener("change",i),this.on("dispose",function(){return t.removeEventListener("change",i)});var r=function i(){for(var r=0;r<e.length;r++){var n=e[r];"disabled"===n.track.mode&&n.track.mode!==n.storedMode&&(n.track.mode=n.storedMode)}t.removeEventListener("change",i)};this.on("webkitbeginfullscreen",function(){t.removeEventListener("change",i),t.removeEventListener("change",r),t.addEventListener("change",r)}),this.on("webkitendfullscreen",function(){t.removeEventListener("change",i),t.addEventListener("change",i),t.removeEventListener("change",r)})},e.prototype.proxyNativeTracks_=function(){var t=this;Pr.names.forEach(function(e){var i=Pr[e],r=t.el()[i.getterName],n=t[i.getterName]();if(t["featuresNative"+i.capitalName+"Tracks"]&&r&&r.addEventListener){var s={change:function(t){n.trigger({type:"change",target:n,currentTarget:n,srcElement:n})},addtrack:function(t){n.addTrack(t.track)},removetrack:function(t){n.removeTrack(t.track)}},a=function(){for(var t=[],e=0;e<n.length;e++){for(var i=!1,s=0;s<r.length;s++)if(r[s]===n[e]){i=!0;break}i||t.push(n[e])}for(;t.length;)n.removeTrack(t.shift())};Object.keys(s).forEach(function(e){var i=s[e];r.addEventListener(e,i),t.on("dispose",function(t){return r.removeEventListener(e,i)})}),t.on("loadstart",a),t.on("dispose",function(e){return t.off("loadstart",a)})}})},e.prototype.createEl=function(){var t=this.options_.tag;if(!t||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(t){var i=t.cloneNode(!0);t.parentNode&&t.parentNode.insertBefore(i,t),e.disposeMediaElement(t),t=i}else{t=ke.createElement("video");var r=this.options_.tag&&k(this.options_.tag),s=tt({},r);Pi&&!0===this.options_.nativeControlsForTouch||delete s.controls,S(t,n(s,{id:this.options_.techId,class:"vjs-tech"}))}t.playerId=this.options_.playerId}void 0!==this.options_.preload&&C(t,"preload",this.options_.preload);for(var a=["loop","muted","playsinline","autoplay"],o=0;o<a.length;o++){var u=a[o],l=this.options_[u];void 0!==l&&(l?C(t,u,u):w(t,u),t[u]=l)}return t},e.prototype.handleLateInit_=function(t){if(0!==t.networkState&&3!==t.networkState){if(0===t.readyState){var e=!1,i=function(){e=!0};this.on("loadstart",i);var r=function(){e||this.trigger("loadstart")};return this.on("loadedmetadata",r),void this.ready(function(){this.off("loadstart",i),this.off("loadedmetadata",r),e||this.trigger("loadstart")})}var n=["loadstart"];n.push("loadedmetadata"),t.readyState>=2&&n.push("loadeddata"),t.readyState>=3&&n.push("canplay"),t.readyState>=4&&n.push("canplaythrough"),this.ready(function(){n.forEach(function(t){this.trigger(t)},this)})}},e.prototype.setCurrentTime=function(t){try{this.el_.currentTime=t}catch(t){Le(t,"Video is not ready. (Video.js)")}},e.prototype.duration=function(){var t=this;if(this.el_.duration===1/0&&bi&&Ci&&0===this.el_.currentTime){var e=function e(){t.el_.currentTime>0&&(t.el_.duration===1/0&&t.trigger("durationchange"),t.off("timeupdate",e))};return this.on("timeupdate",e),NaN}return this.el_.duration||NaN},e.prototype.width=function(){return this.el_.offsetWidth},e.prototype.height=function(){return this.el_.offsetHeight},e.prototype.proxyWebkitFullscreen_=function(){var t=this;if("webkitDisplayingFullscreen"in this.el_){var e=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},i=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",e),this.trigger("fullscreenchange",{isFullscreen:!0}))};this.on("webkitbeginfullscreen",i),this.on("dispose",function(){t.off("webkitbeginfullscreen",i),t.off("webkitendfullscreen",e)})}},e.prototype.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var t=ve.navigator&&ve.navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1},e.prototype.enterFullScreen=function(){var t=this.el_;t.paused&&t.networkState<=t.HAVE_METADATA?(this.el_.play(),this.setTimeout(function(){t.pause(),t.webkitEnterFullScreen()},0)):t.webkitEnterFullScreen()},e.prototype.exitFullScreen=function(){this.el_.webkitExitFullScreen()},e.prototype.src=function(t){if(void 0===t)return this.el_.src;this.setSrc(t)},e.prototype.reset=function(){e.resetMediaElement(this.el_)},e.prototype.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},e.prototype.setControls=function(t){this.el_.controls=!!t},e.prototype.addTextTrack=function(e,i,r){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,i,r):t.prototype.addTextTrack.call(this,e,i,r)},e.prototype.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return t.prototype.createRemoteTextTrack.call(this,e);var i=ke.createElement("track");return e.kind&&(i.kind=e.kind),e.label&&(i.label=e.label),(e.language||e.srclang)&&(i.srclang=e.language||e.srclang),e.default&&(i.default=e.default),e.id&&(i.id=e.id),e.src&&(i.src=e.src),i},e.prototype.addRemoteTextTrack=function(e,i){var r=t.prototype.addRemoteTextTrack.call(this,e,i);return this.featuresNativeTextTracks&&this.el().appendChild(r),r},e.prototype.removeRemoteTextTrack=function(e){if(t.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var i=this.$$("track"),r=i.length;r--;)e!==i[r]&&e!==i[r].track||this.el().removeChild(i[r])},e.prototype.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var t={};return void 0!==this.el().webkitDroppedFrameCount&&void 0!==this.el().webkitDecodedFrameCount&&(t.droppedVideoFrames=this.el().webkitDroppedFrameCount,t.totalVideoFrames=this.el().webkitDecodedFrameCount),ve.performance&&"function"==typeof ve.performance.now?t.creationTime=ve.performance.now():ve.performance&&ve.performance.timing&&"number"==typeof ve.performance.timing.navigationStart&&(t.creationTime=ve.Date.now()-ve.performance.timing.navigationStart),t},e}(Xr);if(h()){Ss.TEST_VID=ke.createElement("video");var ks=ke.createElement("track");ks.kind="captions",ks.srclang="en",ks.label="English",Ss.TEST_VID.appendChild(ks)}Ss.isSupported=function(){try{Ss.TEST_VID.volume=.5}catch(t){return!1}return!(!Ss.TEST_VID||!Ss.TEST_VID.canPlayType)},Ss.canPlayType=function(t){return Ss.TEST_VID.canPlayType(t)},Ss.canPlaySource=function(t,e){return Ss.canPlayType(t.type)},Ss.canControlVolume=function(){try{var t=Ss.TEST_VID.volume;return Ss.TEST_VID.volume=t/2+.1,t!==Ss.TEST_VID.volume}catch(t){return!1}},Ss.canControlPlaybackRate=function(){if(bi&&Ci&&wi<58)return!1;try{var t=Ss.TEST_VID.playbackRate;return Ss.TEST_VID.playbackRate=t/2+.1,t!==Ss.TEST_VID.playbackRate}catch(t){return!1}},Ss.canOverrideAttributes=function(){try{var t=function(){};Object.defineProperty(ke.createElement("video"),"src",{get:t,set:t}),Object.defineProperty(ke.createElement("audio"),"src",{get:t,set:t})}catch(t){return!1}return!0},Ss.supportsNativeTextTracks=function(){return Oi},Ss.supportsNativeVideoTracks=function(){return!(!Ss.TEST_VID||!Ss.TEST_VID.videoTracks)},Ss.supportsNativeAudioTracks=function(){return!(!Ss.TEST_VID||!Ss.TEST_VID.audioTracks)},Ss.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],Ss.prototype.featuresVolumeControl=Ss.canControlVolume(),Ss.prototype.featuresPlaybackRate=Ss.canControlPlaybackRate(),Ss.prototype.featuresSourceset=Ss.canOverrideAttributes(),Ss.prototype.movingMediaElementInDOM=!vi,Ss.prototype.featuresFullscreenResize=!0,Ss.prototype.featuresProgressEvents=!0,Ss.prototype.featuresTimeupdateEvents=!0,Ss.prototype.featuresNativeTextTracks=Ss.supportsNativeTextTracks(),Ss.prototype.featuresNativeVideoTracks=Ss.supportsNativeVideoTracks(),Ss.prototype.featuresNativeAudioTracks=Ss.supportsNativeAudioTracks();var Es=Ss.TEST_VID&&Ss.TEST_VID.constructor.prototype.canPlayType,Cs=/^application\/(?:x-|vnd\.apple\.)mpegurl/i;Ss.patchCanPlayType=function(){Ti>=4&&!ki&&!Ci&&(Ss.TEST_VID.constructor.prototype.canPlayType=function(t){return t&&Cs.test(t)?"maybe":Es.call(this,t)})},Ss.unpatchCanPlayType=function(){var t=Ss.TEST_VID.constructor.prototype.canPlayType;return Ss.TEST_VID.constructor.prototype.canPlayType=Es,t},Ss.patchCanPlayType(),Ss.disposeMediaElement=function(t){if(t){for(t.parentNode&&t.parentNode.removeChild(t);t.hasChildNodes();)t.removeChild(t.firstChild);t.removeAttribute("src"),"function"==typeof t.load&&function(){try{t.load()}catch(t){}}()}},Ss.resetMediaElement=function(t){if(t){for(var e=t.querySelectorAll("source"),i=e.length;i--;)t.removeChild(e[i]);t.removeAttribute("src"),"function"==typeof t.load&&function(){try{t.load()}catch(t){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(t){Ss.prototype[t]=function(){return this.el_[t]||this.el_.hasAttribute(t)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(t){Ss.prototype["set"+Q(t)]=function(e){this.el_[t]=e,e?this.el_.setAttribute(t,t):this.el_.removeAttribute(t)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight"].forEach(function(t){Ss.prototype[t]=function(){return this.el_[t]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate"].forEach(function(t){Ss.prototype["set"+Q(t)]=function(e){this.el_[t]=e}}),["pause","load","play"].forEach(function(t){Ss.prototype[t]=function(){return this.el_[t]()}}),Xr.withSourceHandlers(Ss),Ss.nativeSourceHandler={},Ss.nativeSourceHandler.canPlayType=function(t){try{return Ss.TEST_VID.canPlayType(t)}catch(t){return""}},Ss.nativeSourceHandler.canHandleSource=function(t,e){if(t.type)return Ss.nativeSourceHandler.canPlayType(t.type);if(t.src){var i=ur(t.src);return Ss.nativeSourceHandler.canPlayType("video/"+i)}return""},Ss.nativeSourceHandler.handleSource=function(t,e,i){e.setSrc(t.src)},Ss.nativeSourceHandler.dispose=function(){},Ss.registerSourceHandler(Ss.nativeSourceHandler),Xr.registerTech("Html5",Ss)
;var ws=Me(["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "],["\n        Using the tech directly can be dangerous. I hope you know what you're doing.\n        See https://github.com/videojs/video.js/issues/2617 for more info.\n      "]),As=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","ratechange","resize","volumechange","texttrackchange"],Ls=function(t){function e(i,r,s){if(Ie(this,e),i.id=i.id||"vjs_video_"+B(),r=n(e.getTagSettings(i),r),r.initChildren=!1,r.createEl=!1,r.evented=!1,r.reportTouchActivity=!1,!r.language)if("function"==typeof i.closest){var a=i.closest("[lang]");a&&a.getAttribute&&(r.language=a.getAttribute("lang"))}else for(var o=i;o&&1===o.nodeType;){if(k(o).hasOwnProperty("lang")){r.language=o.getAttribute("lang");break}o=o.parentNode}var u=De(this,t.call(this,null,r,s));if(u.isPosterFromTech_=!1,u.isReady_=!1,u.hasStarted_=!1,u.userActive_=!1,!u.options_||!u.options_.techOrder||!u.options_.techOrder.length)throw new Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(u.tag=i,u.tagAttributes=i&&k(i),u.language(u.options_.language),r.languages){var l={};Object.getOwnPropertyNames(r.languages).forEach(function(t){l[t.toLowerCase()]=r.languages[t]}),u.languages_=l}else u.languages_=e.prototype.options_.languages;u.cache_={},u.poster_=r.poster||"",u.controls_=!!r.controls,u.cache_.lastVolume=1,i.controls=!1,i.removeAttribute("controls"),u.scrubbing_=!1,u.el_=u.createEl(),K(u,{eventBusKey:"el_"});var c=tt(u.options_);if(r.plugins){var h=r.plugins;Object.keys(h).forEach(function(t){if("function"!=typeof this[t])throw new Error('plugin "'+t+'" does not exist');this[t](h[t])},u)}u.options_.playerOptions=c,u.middleware_=[],u.initChildren(),u.isAudio("audio"===i.nodeName.toLowerCase()),u.controls()?u.addClass("vjs-controls-enabled"):u.addClass("vjs-controls-disabled"),u.el_.setAttribute("role","region"),u.isAudio()?u.el_.setAttribute("aria-label",u.localize("Audio Player")):u.el_.setAttribute("aria-label",u.localize("Video Player")),u.isAudio()&&u.addClass("vjs-audio"),u.flexNotSupported_()&&u.addClass("vjs-no-flex"),vi||u.addClass("vjs-workinghover"),e.players[u.id_]=u;var d=me.split(".")[0];return u.addClass("vjs-v"+d),u.userActive(!0),u.reportUserActivity(),u.listenForUserActivity_(),u.on("fullscreenchange",u.handleFullscreenChange_),u.on("stageclick",u.handleStageClick_),u.changingSrc_=!1,u.playWaitingForReady_=!1,u.playOnLoadstart_=null,u}return xe(e,t),e.prototype.dispose=function(){this.trigger("dispose"),this.off("dispose"),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),e.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),Qt(this),t.prototype.dispose.call(this)},e.prototype.createEl=function(){var e=this.tag,i=void 0,r=this.playerElIngest_=e.parentNode&&e.parentNode.hasAttribute&&e.parentNode.hasAttribute("data-vjs-player"),n="video-js"===this.tag.tagName.toLowerCase();r?i=this.el_=e.parentNode:n||(i=this.el_=t.prototype.createEl.call(this,"div"));var s=k(e);if(n){for(i=this.el_=e,e=this.tag=ke.createElement("video");i.children.length;)e.appendChild(i.firstChild);v(i,"video-js")||_(i,"video-js"),i.appendChild(e),r=this.playerElIngest_=i}if(e.setAttribute("tabindex","-1"),e.removeAttribute("width"),e.removeAttribute("height"),Object.getOwnPropertyNames(s).forEach(function(t){i.setAttribute(t,s[t]),n&&e.setAttribute(t,s[t])}),e.playerId=e.id,e.id+="_html5_api",e.className="vjs-tech",e.player=i.player=this,this.addClass("vjs-paused"),!0!==ve.VIDEOJS_NO_DYNAMIC_STYLE){this.styleEl_=Ke("vjs-styles-dimensions");var a=Be(".vjs-styles-defaults"),o=Be("head");o.insertBefore(this.styleEl_,a?a.nextSibling:o.firstChild)}this.width(this.options_.width),this.height(this.options_.height),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio);for(var u=e.getElementsByTagName("a"),l=0;l<u.length;l++){var c=u.item(l);_(c,"vjs-hidden"),c.setAttribute("hidden","hidden")}return e.initNetworkState_=e.networkState,e.parentNode&&!r&&e.parentNode.insertBefore(i,e),y(e,i),this.children_.unshift(e),this.el_.setAttribute("lang",this.language_),this.el_=i,i},e.prototype.width=function(t){return this.dimension("width",t)},e.prototype.height=function(t){return this.dimension("height",t)},e.prototype.dimension=function(t,e){var i=t+"_";if(void 0===e)return this[i]||0;if(""===e)return this[i]=void 0,void this.updateStyleEl_();var r=parseFloat(e);if(isNaN(r))return void Le.error('Improper value "'+e+'" supplied for for '+t);this[i]=r,this.updateStyleEl_()},e.prototype.fluid=function(t){if(void 0===t)return!!this.fluid_;this.fluid_=!!t,t?this.addClass("vjs-fluid"):this.removeClass("vjs-fluid"),this.updateStyleEl_()},e.prototype.aspectRatio=function(t){if(void 0===t)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(t))throw new Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=t,this.fluid(!0),this.updateStyleEl_()},e.prototype.updateStyleEl_=function(){if(!0===ve.VIDEOJS_NO_DYNAMIC_STYLE){var t="number"==typeof this.width_?this.width_:this.options_.width,e="number"==typeof this.height_?this.height_:this.options_.height,i=this.tech_&&this.tech_.el();return void(i&&(t>=0&&(i.width=t),e>=0&&(i.height=e)))}var r=void 0,n=void 0,s=void 0,a=void 0;s=void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:this.videoWidth()>0?this.videoWidth()+":"+this.videoHeight():"16:9";var o=s.split(":"),u=o[1]/o[0];r=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/u:this.videoWidth()||300,n=void 0!==this.height_?this.height_:r*u,a=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(a),Je(this.styleEl_,"\n      ."+a+" {\n        width: "+r+"px;\n        height: "+n+"px;\n      }\n\n      ."+a+".vjs-fluid {\n        padding-top: "+100*u+"%;\n      }\n    ")},e.prototype.loadTech_=function(t,e){var i=this;this.tech_&&this.unloadTech_();var r=Q(t),s=t.charAt(0).toLowerCase()+t.slice(1);"Html5"!==r&&this.tag&&(Xr.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=r,this.isReady_=!1;var a={source:e,nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+r+"_api",autoplay:this.options_.autoplay,playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset};xr.names.forEach(function(t){var e=xr[t];a[e.getterName]=i[e.privateName]}),n(a,this.options_[r]),n(a,this.options_[s]),n(a,this.options_[t.toLowerCase()]),this.tag&&(a.tag=this.tag),e&&e.src===this.cache_.src&&this.cache_.currentTime>0&&(a.startTime=this.cache_.currentTime);var o=Xr.getTech(t);if(!o)throw new Error("No Tech named '"+r+"' exists! '"+r+"' should be registered using videojs.registerTech()'");this.tech_=new o(a),this.tech_.ready(Qe(this,this.handleTechReady_),!0),qi.jsonToTextTracks(this.textTracksJson_||[],this.tech_),As.forEach(function(t){i.on(i.tech_,t,i["handleTech"+Q(t)+"_"])}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"canplay",this.handleTechCanPlay_),this.on(this.tech_,"canplaythrough",this.handleTechCanPlayThrough_),this.on(this.tech_,"playing",this.handleTechPlaying_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"seeked",this.handleTechSeeked_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===r&&this.tag||y(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},e.prototype.unloadTech_=function(){var t=this;xr.names.forEach(function(e){var i=xr[e];t[i.privateName]=t[i.getterName]()}),this.textTracksJson_=qi.textTracksToJson(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},e.prototype.tech=function(t){return void 0===t&&Le.warn(Oe(ws)),this.tech_},e.prototype.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mousedown",this.handleTechClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},e.prototype.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mousedown",this.handleTechClick_)},e.prototype.handleTechReady_=function(){if(this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_(),(this.src()||this.currentSrc())&&this.tag&&this.options_.autoplay&&this.paused())try{delete this.tag.poster}catch(t){Le("deleting tag.poster throws in some browsers",t)}},e.prototype.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay"))},e.prototype.handleTechSourceset_=function(t){this.trigger({src:t.src,type:"sourceset"})},e.prototype.hasStarted=function(t){if(void 0===t)return this.hasStarted_;t!==this.hasStarted_&&(this.hasStarted_=t,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},e.prototype.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},e.prototype.handleTechWaiting_=function(){var t=this;this.addClass("vjs-waiting"),this.trigger("waiting"),this.one("timeupdate",function(){return t.removeClass("vjs-waiting")})},e.prototype.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},e.prototype.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},e.prototype.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},e.prototype.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},e.prototype.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.trigger("seeked")},e.prototype.handleTechFirstPlay_=function(){this.options_.starttime&&(Le.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},e.prototype.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},e.prototype.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},e.prototype.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},e.prototype.handleTechClick_=function(t){N(t)&&this.controls_&&(this.paused()?this.play():this.pause())},e.prototype.handleTechTap_=function(){this.userActive(!this.userActive())},e.prototype.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},e.prototype.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},e.prototype.handleTechTouchEnd_=function(t){t.preventDefault()},e.prototype.handleFullscreenChange_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},e.prototype.handleStageClick_=function(){this.reportUserActivity()},e.prototype.handleTechFullscreenChange_=function(t,e){e&&this.isFullscreen(e.isFullscreen),this.trigger("fullscreenchange")},e.prototype.handleTechError_=function(){var t=this.tech_.error();this.error(t)},e.prototype.handleTechTextData_=function(){var t=null;arguments.length>1&&(t=arguments[1]),this.trigger("textdata",t)},e.prototype.getCache=function(){return this.cache_},e.prototype.techCall_=function(t,e){this.ready(function(){if(t in Qr)return Yt(this.middleware_,this.tech_,t,e);if(t in Zr)return $t(this.middleware_,this.tech_,t,e);try{this.tech_&&this.tech_[t](e)}catch(t){throw Le(t),t}},!0)},e.prototype.techGet_=function(t){if(this.tech_&&this.tech_.isReady_){if(t in Jr)return Xt(this.middleware_,this.tech_,t);if(t in Zr)return $t(this.middleware_,this.tech_,t);try{return this.tech_[t]()}catch(e){if(void 0===this.tech_[t])throw Le("Video.js: "+t+" method not defined for "+this.techName_+" playback technology.",e),e;if("TypeError"===e.name)throw Le("Video.js: "+t+" unavailable on "+this.techName_+" playback technology element.",e),this.tech_.isReady_=!1,e;throw Le(e),e}}},e.prototype.play=function(){var t=this;if(this.playOnLoadstart_&&this.off("loadstart",this.playOnLoadstart_),this.isReady_){if(!this.changingSrc_&&(this.src()||this.currentSrc()))return this.techGet_("play");this.playOnLoadstart_=function(){t.playOnLoadstart_=null,lt(t.play())},this.one("loadstart",this.playOnLoadstart_)}else{if(this.playWaitingForReady_)return;this.playWaitingForReady_=!0,this.ready(function(){t.playWaitingForReady_=!1,lt(t.play())})}},e.prototype.pause=function(){this.techCall_("pause")},e.prototype.paused=function(){return!1!==this.techGet_("paused")},e.prototype.played=function(){return this.techGet_("played")||nt(0,0)},e.prototype.scrubbing=function(t){if(void 0===t)return this.scrubbing_;this.scrubbing_=!!t,t?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},e.prototype.currentTime=function(t){return void 0!==t?(t<0&&(t=0),void this.techCall_("setCurrentTime",t)):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},e.prototype.duration=function(t){if(void 0===t)return void 0!==this.cache_.duration?this.cache_.duration:NaN;t=parseFloat(t),t<0&&(t=1/0),t!==this.cache_.duration&&(this.cache_.duration=t,t===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),this.trigger("durationchange"))},e.prototype.remainingTime=function(){return this.duration()-this.currentTime()},e.prototype.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},e.prototype.buffered=function(){var t=this.techGet_("buffered");return t&&t.length||(t=nt(0,0)),t},e.prototype.bufferedPercent=function(){return st(this.buffered(),this.duration())},e.prototype.bufferedEnd=function(){var t=this.buffered(),e=this.duration(),i=t.end(t.length-1);return i>e&&(i=e),i},e.prototype.volume=function(t){var e=void 0;return void 0!==t?(e=Math.max(0,Math.min(1,parseFloat(t))),this.cache_.volume=e,this.techCall_("setVolume",e),void(e>0&&this.lastVolume_(e))):(e=parseFloat(this.techGet_("volume")),isNaN(e)?1:e)},e.prototype.muted=function(t){return void 0!==t?void this.techCall_("setMuted",t):this.techGet_("muted")||!1},e.prototype.defaultMuted=function(t){return void 0!==t?this.techCall_("setDefaultMuted",t):this.techGet_("defaultMuted")||!1},e.prototype.lastVolume_=function(t){return void 0!==t&&0!==t?void(this.cache_.lastVolume=t):this.cache_.lastVolume},e.prototype.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},e.prototype.isFullscreen=function(t){return void 0!==t?void(this.isFullscreen_=!!t):!!this.isFullscreen_},e.prototype.requestFullscreen=function(){var t=xi;this.isFullscreen(!0),t.requestFullscreen?(G(ke,t.fullscreenchange,Qe(this,function e(i){this.isFullscreen(ke[t.fullscreenElement]),!1===this.isFullscreen()&&z(ke,t.fullscreenchange,e),this.trigger("fullscreenchange")})),this.el_[t.requestFullscreen]()):this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):(this.enterFullWindow(),this.trigger("fullscreenchange"))},e.prototype.exitFullscreen=function(){var t=xi;this.isFullscreen(!1),t.requestFullscreen?ke[t.exitFullscreen]():this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):(this.exitFullWindow(),this.trigger("fullscreenchange"))},e.prototype.enterFullWindow=function(){this.isFullWindow=!0,this.docOrigOverflow=ke.documentElement.style.overflow,G(ke,"keydown",Qe(this,this.fullWindowOnEscKey)),ke.documentElement.style.overflow="hidden",_(ke.body,"vjs-full-window"),this.trigger("enterFullWindow")},e.prototype.fullWindowOnEscKey=function(t){27===t.keyCode&&(!0===this.isFullscreen()?this.exitFullscreen():this.exitFullWindow())},e.prototype.exitFullWindow=function(){this.isFullWindow=!1,z(ke,"keydown",this.fullWindowOnEscKey),ke.documentElement.style.overflow=this.docOrigOverflow,b(ke.body,"vjs-full-window"),this.trigger("exitFullWindow")},e.prototype.canPlayType=function(t){for(var e=void 0,i=0,r=this.options_.techOrder;i<r.length;i++){var n=r[i],s=Xr.getTech(n);if(s||(s=hi.getComponent(n)),s){if(s.isSupported()&&(e=s.canPlayType(t)))return e}else Le.error('The "'+n+'" tech is undefined. Skipped browser support check for that tech.')}return""},e.prototype.selectSource=function(t){var e=this,i=this.options_.techOrder.map(function(t){return[t,Xr.getTech(t)]}).filter(function(t){var e=t[0],i=t[1];return i?i.isSupported():(Le.error('The "'+e+'" tech is undefined. Skipped browser support check for that tech.'),!1)}),r=function(t,e,i){var r=void 0;return t.some(function(t){return e.some(function(e){if(r=i(t,e))return!0})}),r},n=function(t,i){var r=t[0];if(t[1].canPlaySource(i,e.options_[r.toLowerCase()]))return{source:i,tech:r}};return(this.options_.sourceOrder?r(t,i,function(t){return function(e,i){return t(i,e)}}(n)):r(i,t,n))||!1},e.prototype.src=function(t){var e=this;if(void 0===t)return this.cache_.src||"";var i=en(t);if(!i.length)return void this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0);this.cache_.sources=i,this.changingSrc_=!0,this.cache_.source=i[0],Gt(this,i[0],function(t,r){if(e.middleware_=r,e.src_(t))return i.length>1?e.src(i.slice(1)):(e.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void e.triggerReady());e.changingSrc_=!1,e.cache_.src=t.src,zt(r,e.tech_)})},e.prototype.src_=function(t){var e=this.selectSource([t]);return!e||(Z(e.tech,this.techName_)?(this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",t):this.techCall_("src",t.src),"auto"===this.options_.preload&&this.load()},!0),!1):(this.changingSrc_=!0,this.loadTech_(e.tech,e.source),!1))},e.prototype.load=function(){this.techCall_("load")},e.prototype.reset=function(){this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset")},e.prototype.currentSources=function(){var t=this.currentSource(),e=[];return 0!==Object.keys(t).length&&e.push(t),this.cache_.sources||e},e.prototype.currentSource=function(){return this.cache_.source||{}},e.prototype.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},e.prototype.currentType=function(){return this.currentSource()&&this.currentSource().type||""},e.prototype.preload=function(t){return void 0!==t?(this.techCall_("setPreload",t),void(this.options_.preload=t)):this.techGet_("preload")},e.prototype.autoplay=function(t){return void 0!==t?(this.techCall_("setAutoplay",t),void(this.options_.autoplay=t)):this.techGet_("autoplay",t)},e.prototype.playsinline=function(t){return void 0!==t?(this.techCall_("setPlaysinline",t),this.options_.playsinline=t,this):this.techGet_("playsinline")},e.prototype.loop=function(t){return void 0!==t?(this.techCall_("setLoop",t),void(this.options_.loop=t)):this.techGet_("loop")},e.prototype.poster=function(t){if(void 0===t)return this.poster_;t||(t=""),t!==this.poster_&&(this.poster_=t,this.techCall_("setPoster",t),this.isPosterFromTech_=!1,this.trigger("posterchange"))},e.prototype.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var t=this.tech_.poster()||"";t!==this.poster_&&(this.poster_=t,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},e.prototype.controls=function(t){if(void 0===t)return!!this.controls_;t=!!t,this.controls_!==t&&(this.controls_=t,this.usingNativeControls()&&this.techCall_("setControls",t),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},e.prototype.usingNativeControls=function(t){if(void 0===t)return!!this.usingNativeControls_;t=!!t,this.usingNativeControls_!==t&&(this.usingNativeControls_=t,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},e.prototype.error=function(t){return void 0===t?this.error_||null:null===t?(this.error_=t,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close())):(this.error_=new at(t),this.addClass("vjs-error"),Le.error("(CODE:"+this.error_.code+" "+at.errorTypes[this.error_.code]+")",this.error_.message,this.error_),void this.trigger("error"))},e.prototype.reportUserActivity=function(t){this.userActivity_=!0},e.prototype.userActive=function(t){if(void 0===t)return this.userActive_;if((t=!!t)!==this.userActive_){if(this.userActive_=t,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(t){t.stopPropagation(),t.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},e.prototype.listenForUserActivity_=function(){var t=void 0,e=void 0,i=void 0,r=Qe(this,this.reportUserActivity),n=function(t){t.screenX===e&&t.screenY===i||(e=t.screenX,i=t.screenY,r())},s=function(){r(),this.clearInterval(t),t=this.setInterval(r,250)},a=function(e){r(),this.clearInterval(t)};this.on("mousedown",s),this.on("mousemove",n),this.on("mouseup",a),this.on("keydown",r),this.on("keyup",r);var o=void 0;this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(o);var t=this.options_.inactivityTimeout;t<=0||(o=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},t))}},250)},e.prototype.playbackRate=function(t){return void 0!==t?void this.techCall_("setPlaybackRate",t):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("playbackRate"):1},e.prototype.defaultPlaybackRate=function(t){return void 0!==t?this.techCall_("setDefaultPlaybackRate",t):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},e.prototype.isAudio=function(t){return void 0!==t?void(this.isAudio_=!!t):!!this.isAudio_},e.prototype.addTextTrack=function(t,e,i){if(this.tech_)return this.tech_.addTextTrack(t,e,i)},e.prototype.addRemoteTextTrack=function(t,e){if(this.tech_)return this.tech_.addRemoteTextTrack(t,e)},e.prototype.removeRemoteTextTrack=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.track,i=void 0===e?arguments[0]:e;if(this.tech_)return this.tech_.removeRemoteTextTrack(i)},e.prototype.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},e.prototype.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},e.prototype.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},e.prototype.language=function(t){if(void 0===t)return this.language_;this.language_=String(t).toLowerCase()},e.prototype.languages=function(){return tt(e.prototype.options_.languages,this.languages_)},e.prototype.toJSON=function(){var t=tt(this.options_),e=t.tracks;t.tracks=[];for(var i=0;i<e.length;i++){var r=e[i];r=tt(r),r.player=void 0,t.tracks[i]=r}return t},e.prototype.createModal=function(t,e){var i=this;e=e||{},e.content=t||"";var r=new Gi(this,e);return this.addChild(r),r.on("dispose",function(){i.removeChild(r)}),r.open(),r},e.getTagSettings=function(t){var e={sources:[],tracks:[]},i=k(t),r=i["data-setup"];if(v(t,"vjs-fluid")&&(i.fluid=!0),null!==r){var s=ji(r||"{}"),a=s[0],o=s[1];a&&Le.error(a),n(i,o)}if(n(e,i),t.hasChildNodes())for(var u=t.childNodes,l=0,c=u.length;l<c;l++){var h=u[l],d=h.nodeName.toLowerCase();"source"===d?e.sources.push(k(h)):"track"===d&&e.tracks.push(k(h))}return e},e.prototype.flexNotSupported_=function(){var t=ke.createElement("i");return!("flexBasis"in t.style||"webkitFlexBasis"in t.style||"mozFlexBasis"in t.style||"msFlexBasis"in t.style||"msFlexOrder"in t.style)},e}(hi);xr.names.forEach(function(t){var e=xr[t];Ls.prototype[e.getterName]=function(){return this.tech_?this.tech_[e.getterName]():(this[e.privateName]=this[e.privateName]||new e.ListClass,this[e.privateName])}}),Ls.players={};var Os=ve.navigator;Ls.prototype.options_={techOrder:Xr.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:Os&&(Os.languages&&Os.languages[0]||Os.userLanguage||Os.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media."},["ended","seeking","seekable","networkState","readyState"].forEach(function(t){Ls.prototype[t]=function(){return this.techGet_(t)}}),As.forEach(function(t){Ls.prototype["handleTech"+Q(t)+"_"]=function(){return this.trigger(t)}}),hi.registerComponent("Player",Ls);var Ps={},Is=function(t){return Ps.hasOwnProperty(t)},xs=function(t){return Is(t)?Ps[t]:void 0},Ds=function(t,e){t.activePlugins_=t.activePlugins_||{},t.activePlugins_[e]=!0},Ms=function(t,e,i){var r=(i?"before":"")+"pluginsetup";t.trigger(r,e),t.trigger(r+":"+e.name,e)},Rs=function(t,e){var i=function(){Ms(this,{name:t,plugin:e,instance:null},!0);var i=e.apply(this,arguments);return Ds(this,t),Ms(this,{name:t,plugin:e,instance:i}),i};return Object.keys(e).forEach(function(t){i[t]=e[t]}),i},Us=function(t,e){return e.prototype.name=t,function(){Ms(this,{name:t,plugin:e,instance:null},!0);for(var i=arguments.length,r=Array(i),n=0;n<i;n++)r[n]=arguments[n];var s=new(Function.prototype.bind.apply(e,[null].concat([this].concat(r))));return this[t]=function(){return s},Ms(this,s.getEventHash()),s}},Ns=function(){function t(e){if(Ie(this,t),this.constructor===t)throw new Error("Plugin must be sub-classed; not directly instantiated.");this.player=e,K(this),delete this.trigger,J(this,this.constructor.defaultState),Ds(e,this.name),this.dispose=Qe(this,this.dispose),e.on("dispose",this.dispose)}return t.prototype.version=function(){return this.constructor.VERSION},t.prototype.getEventHash=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return t.name=this.name,t.plugin=this.constructor,t.instance=this,t},t.prototype.trigger=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return X(this.eventBusEl_,t,this.getEventHash(e))},t.prototype.handleStateChanged=function(t){},t.prototype.dispose=function(){var t=this.name,e=this.player;this.trigger("dispose"),this.off(),e.off("dispose",this.dispose),e.activePlugins_[t]=!1,this.player=this.state=null,e[t]=Us(t,Ps[t])},t.isBasic=function(e){var i="string"==typeof e?xs(e):e;return"function"==typeof i&&!t.prototype.isPrototypeOf(i.prototype)},t.registerPlugin=function(e,i){if("string"!=typeof e)throw new Error('Illegal plugin name, "'+e+'", must be a string, was '+(void 0===e?"undefined":Pe(e))+".");if(Is(e))Le.warn('A plugin named "'+e+'" already exists. You may want to avoid re-registering plugins!');else if(Ls.prototype.hasOwnProperty(e))throw new Error('Illegal plugin name, "'+e+'", cannot share a name with an existing player method!');if("function"!=typeof i)throw new Error('Illegal plugin for "'+e+'", must be a function, was '+(void 0===i?"undefined":Pe(i))+".");return Ps[e]=i,"plugin"!==e&&(t.isBasic(i)?Ls.prototype[e]=Rs(e,i):Ls.prototype[e]=Us(e,i)),i},t.deregisterPlugin=function(t){if("plugin"===t)throw new Error("Cannot de-register base plugin.");Is(t)&&(delete Ps[t],delete Ls.prototype[t])},t.getPlugins=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Object.keys(Ps),e=void 0;return t.forEach(function(t){var i=xs(t);i&&(e=e||{},e[t]=i)}),e},t.getPluginVersion=function(t){var e=xs(t);return e&&e.VERSION||""},t}();Ns.getPlugin=xs,Ns.BASE_PLUGIN_NAME="plugin",Ns.registerPlugin("plugin",Ns),Ls.prototype.usingPlugin=function(t){return!!this.activePlugins_&&!0===this.activePlugins_[t]},Ls.prototype.hasPlugin=function(t){return!!Is(t)};var Bs=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":Pe(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(t.super_=e)},js=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=function(){t.apply(this,arguments)},r={};"object"===(void 0===e?"undefined":Pe(e))?(e.constructor!==Object.prototype.constructor&&(i=e.constructor),r=e):"function"==typeof e&&(i=e),Bs(i,t);for(var n in r)r.hasOwnProperty(n)&&(i.prototype[n]=r[n]);return i},Fs=function(t){return 0===t.indexOf("#")?t.slice(1):t};if(le.hooks_={},le.hooks=function(t,e){return le.hooks_[t]=le.hooks_[t]||[],e&&(le.hooks_[t]=le.hooks_[t].concat(e)),le.hooks_[t]},le.hook=function(t,e){le.hooks(t,e)},le.hookOnce=function(t,e){le.hooks(t,[].concat(e).map(function(e){return function i(){return le.removeHook(t,i),e.apply(void 0,arguments)}}))},le.removeHook=function(t,e){var i=le.hooks(t).indexOf(e);return!(i<=-1)&&(le.hooks_[t]=le.hooks_[t].slice(),le.hooks_[t].splice(i,1),!0)},!0!==ve.VIDEOJS_NO_DYNAMIC_STYLE&&h()){var Hs=Be(".vjs-styles-defaults");if(!Hs){Hs=Ke("vjs-styles-defaults");var Vs=Be("head");Vs&&Vs.insertBefore(Hs,Vs.firstChild),Je(Hs,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}$(1,le),le.VERSION=me,le.options=Ls.prototype.options_,le.getPlayers=function(){return Ls.players},le.getPlayer=function(t){var e=Ls.players,i=void 0;if("string"==typeof t){var r=Fs(t),n=e[r];if(n)return n;i=Be("#"+r)}else i=t;if(d(i)){var s=i,a=s.player,o=s.playerId;if(a||e[o])return a||e[o]}},
le.getAllPlayers=function(){return Object.keys(Ls.players).map(function(t){return Ls.players[t]}).filter(Boolean)},le.players=Ls.players,le.getComponent=hi.getComponent,le.registerComponent=function(t,e){Xr.isTech(e)&&Le.warn("The "+t+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),hi.registerComponent.call(hi,t,e)},le.getTech=Xr.getTech,le.registerTech=Xr.registerTech,le.use=Wt,Object.defineProperty(le,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(le.middleware,"TERMINATOR",{value:Kr,writeable:!1,enumerable:!0}),le.browser=Ii,le.TOUCH_ENABLED=Pi,le.extend=js,le.mergeOptions=tt,le.bind=Qe,le.registerPlugin=Ns.registerPlugin,le.plugin=function(t,e){return Le.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),Ns.registerPlugin(t,e)},le.getPlugins=Ns.getPlugins,le.getPlugin=Ns.getPlugin,le.getPluginVersion=Ns.getPluginVersion,le.addLanguage=function(t,e){var i;return t=(""+t).toLowerCase(),le.options.languages=tt(le.options.languages,(i={},i[t]=e,i)),le.options.languages[t]},le.log=Le,le.createTimeRange=le.createTimeRanges=nt,le.formatTime=mn,le.setFormatTime=ne,le.resetFormatTime=se,le.parseUrl=ar,le.isCrossOrigin=lr,le.EventTarget=ei,le.on=G,le.one=Y,le.off=z,le.trigger=X,le.xhr=Tr,le.TextTrack=Er,le.AudioTrack=Cr,le.VideoTrack=wr,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(t){le[t]=function(){return Le.warn("videojs."+t+"() is deprecated; use videojs.dom."+t+"() instead"),Fe[t].apply(null,arguments)}}),le.computedStyle=o,le.dom=Fe,le.url=cr;var qs,Ws,Gs,zs,Xs=t(function(t,e){!function(e){var i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/\;?#]*)?(.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,r=/^([^\/;?#]*)(.*)$/,n=/(?:\/|^)\.(?=\/)/g,s=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,a={buildAbsoluteURL:function(t,e,i){if(i=i||{},t=t.trim(),!(e=e.trim())){if(!i.alwaysNormalize)return t;var n=a.parseURL(t);if(!n)throw new Error("Error trying to parse base URL.");return n.path=a.normalizePath(n.path),a.buildURLFromParts(n)}var s=a.parseURL(e);if(!s)throw new Error("Error trying to parse relative URL.");if(s.scheme)return i.alwaysNormalize?(s.path=a.normalizePath(s.path),a.buildURLFromParts(s)):e;var o=a.parseURL(t);if(!o)throw new Error("Error trying to parse base URL.");if(!o.netLoc&&o.path&&"/"!==o.path[0]){var u=r.exec(o.path);o.netLoc=u[1],o.path=u[2]}o.netLoc&&!o.path&&(o.path="/");var l={scheme:o.scheme,netLoc:s.netLoc,path:null,params:s.params,query:s.query,fragment:s.fragment};if(!s.netLoc&&(l.netLoc=o.netLoc,"/"!==s.path[0]))if(s.path){var c=o.path,h=c.substring(0,c.lastIndexOf("/")+1)+s.path;l.path=a.normalizePath(h)}else l.path=o.path,s.params||(l.params=o.params,s.query||(l.query=o.query));return null===l.path&&(l.path=i.alwaysNormalize?a.normalizePath(s.path):s.path),a.buildURLFromParts(l)},parseURL:function(t){var e=i.exec(t);return e?{scheme:e[1]||"",netLoc:e[2]||"",path:e[3]||"",params:e[4]||"",query:e[5]||"",fragment:e[6]||""}:null},normalizePath:function(t){for(t=t.split("").reverse().join("").replace(n,"");t.length!==(t=t.replace(s,"")).length;);return t.split("").reverse().join("")},buildURLFromParts:function(t){return t.scheme+t.netLoc+t.path+t.params+t.query+t.fragment}};t.exports=a}()}),Ys=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},$s=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},Ks=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":Pe(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},Js=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==(void 0===e?"undefined":Pe(e))&&"function"!=typeof e?t:e},Qs=function(){function t(){Ys(this,t),this.listeners={}}return t.prototype.on=function(t,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(e)},t.prototype.off=function(t,e){if(!this.listeners[t])return!1;var i=this.listeners[t].indexOf(e);return this.listeners[t].splice(i,1),i>-1},t.prototype.trigger=function(t){var e=this.listeners[t],i=void 0,r=void 0,n=void 0;if(e)if(2===arguments.length)for(r=e.length,i=0;i<r;++i)e[i].call(this,arguments[1]);else for(n=Array.prototype.slice.call(arguments,1),r=e.length,i=0;i<r;++i)e[i].apply(this,n)},t.prototype.dispose=function(){this.listeners={}},t.prototype.pipe=function(t){this.on("data",function(e){t.push(e)})},t}(),Zs=function(t){function e(){Ys(this,e);var i=Js(this,t.call(this));return i.buffer="",i}return Ks(e,t),e.prototype.push=function(t){var e=void 0;for(this.buffer+=t,e=this.buffer.indexOf("\n");e>-1;e=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,e)),this.buffer=this.buffer.substring(e+1)},e}(Qs),ta=function(){return new RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')},ea=function(t){for(var e=t.split(ta()),i={},r=e.length,n=void 0;r--;)""!==e[r]&&(n=/([^=]*)=(.*)/.exec(e[r]).slice(1),n[0]=n[0].replace(/^\s+|\s+$/g,""),n[1]=n[1].replace(/^\s+|\s+$/g,""),n[1]=n[1].replace(/^['"](.*)['"]$/g,"$1"),i[n[0]]=n[1]);return i},ia=function(t){function e(){Ys(this,e);var i=Js(this,t.call(this));return i.customParsers=[],i}return Ks(e,t),e.prototype.push=function(t){var e=void 0,i=void 0;if(t=t.replace(/^[\u0000\s]+|[\u0000\s]+$/g,""),0!==t.length){if("#"!==t[0])return void this.trigger("data",{type:"uri",uri:t});for(var r=0;r<this.customParsers.length;r++)if(this.customParsers[r].call(this,t))return;if(0!==t.indexOf("#EXT"))return void this.trigger("data",{type:"comment",text:t.slice(1)});if(t=t.replace("\r",""),e=/^#EXTM3U/.exec(t))return void this.trigger("data",{type:"tag",tagType:"m3u"});if(e=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(t))return i={type:"tag",tagType:"inf"},e[1]&&(i.duration=parseFloat(e[1])),e[2]&&(i.title=e[2]),void this.trigger("data",i);if(e=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(t))return i={type:"tag",tagType:"targetduration"},e[1]&&(i.duration=parseInt(e[1],10)),void this.trigger("data",i);if(e=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(t))return i={type:"tag",tagType:"totalduration"},e[1]&&(i.duration=parseInt(e[1],10)),void this.trigger("data",i);if(e=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(t))return i={type:"tag",tagType:"version"},e[1]&&(i.version=parseInt(e[1],10)),void this.trigger("data",i);if(e=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(t))return i={type:"tag",tagType:"media-sequence"},e[1]&&(i.number=parseInt(e[1],10)),void this.trigger("data",i);if(e=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(t))return i={type:"tag",tagType:"discontinuity-sequence"},e[1]&&(i.number=parseInt(e[1],10)),void this.trigger("data",i);if(e=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(t))return i={type:"tag",tagType:"playlist-type"},e[1]&&(i.playlistType=e[1]),void this.trigger("data",i);if(e=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(t))return i={type:"tag",tagType:"byterange"},e[1]&&(i.length=parseInt(e[1],10)),e[2]&&(i.offset=parseInt(e[2],10)),void this.trigger("data",i);if(e=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(t))return i={type:"tag",tagType:"allow-cache"},e[1]&&(i.allowed=!/NO/.test(e[1])),void this.trigger("data",i);if(e=/^#EXT-X-MAP:?(.*)$/.exec(t)){if(i={type:"tag",tagType:"map"},e[1]){var n=ea(e[1]);if(n.URI&&(i.uri=n.URI),n.BYTERANGE){var s=n.BYTERANGE.split("@"),a=s[0],o=s[1];i.byterange={},a&&(i.byterange.length=parseInt(a,10)),o&&(i.byterange.offset=parseInt(o,10))}}return void this.trigger("data",i)}if(e=/^#EXT-X-STREAM-INF:?(.*)$/.exec(t)){if(i={type:"tag",tagType:"stream-inf"},e[1]){if(i.attributes=ea(e[1]),i.attributes.RESOLUTION){var u=i.attributes.RESOLUTION.split("x"),l={};u[0]&&(l.width=parseInt(u[0],10)),u[1]&&(l.height=parseInt(u[1],10)),i.attributes.RESOLUTION=l}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}return void this.trigger("data",i)}if(e=/^#EXT-X-MEDIA:?(.*)$/.exec(t))return i={type:"tag",tagType:"media"},e[1]&&(i.attributes=ea(e[1])),void this.trigger("data",i);if(e=/^#EXT-X-ENDLIST/.exec(t))return void this.trigger("data",{type:"tag",tagType:"endlist"});if(e=/^#EXT-X-DISCONTINUITY/.exec(t))return void this.trigger("data",{type:"tag",tagType:"discontinuity"});if(e=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(t))return i={type:"tag",tagType:"program-date-time"},e[1]&&(i.dateTimeString=e[1],i.dateTimeObject=new Date(e[1])),void this.trigger("data",i);if(e=/^#EXT-X-KEY:?(.*)$/.exec(t))return i={type:"tag",tagType:"key"},e[1]&&(i.attributes=ea(e[1]),i.attributes.IV&&("0x"===i.attributes.IV.substring(0,2).toLowerCase()&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),void this.trigger("data",i);if(e=/^#EXT-X-START:?(.*)$/.exec(t))return i={type:"tag",tagType:"start"},e[1]&&(i.attributes=ea(e[1]),i.attributes["TIME-OFFSET"]=parseFloat(i.attributes["TIME-OFFSET"]),i.attributes.PRECISE=/YES/.test(i.attributes.PRECISE)),void this.trigger("data",i);if(e=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(t))return i={type:"tag",tagType:"cue-out-cont"},e[1]?i.data=e[1]:i.data="",void this.trigger("data",i);if(e=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(t))return i={type:"tag",tagType:"cue-out"},e[1]?i.data=e[1]:i.data="",void this.trigger("data",i);if(e=/^#EXT-X-CUE-IN:?(.*)?$/.exec(t))return i={type:"tag",tagType:"cue-in"},e[1]?i.data=e[1]:i.data="",void this.trigger("data",i);this.trigger("data",{type:"tag",data:t.slice(4)})}},e.prototype.addParser=function(t){var e=this,i=t.expression,r=t.customType,n=t.dataParser,s=t.segment;"function"!=typeof n&&(n=function(t){return t}),this.customParsers.push(function(t){if(i.exec(t))return e.trigger("data",{type:"custom",data:n(t),customType:r,segment:s}),!0})},e}(Qs),ra=function(t){function e(){Ys(this,e);var i=Js(this,t.call(this));i.lineStream=new Zs,i.parseStream=new ia,i.lineStream.pipe(i.parseStream);var r=i,n=[],s={},a=void 0,o=void 0,u=function(){},l={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},c=0;return i.manifest={allowCache:!0,discontinuityStarts:[],segments:[]},i.parseStream.on("data",function(t){var e=void 0,i=void 0;({tag:function(){(({"allow-cache":function(){this.manifest.allowCache=t.allowed,"allowed"in t||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var e={};"length"in t&&(s.byterange=e,e.length=t.length,"offset"in t||(this.trigger("info",{message:"defaulting offset to zero"}),t.offset=0)),"offset"in t&&(s.byterange=e,e.offset=t.offset)},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),t.duration>0&&(s.duration=t.duration),0===t.duration&&(s.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=n},key:function(){return t.attributes?"NONE"===t.attributes.METHOD?void(o=null):t.attributes.URI?(t.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),o={method:t.attributes.METHOD||"AES-128",uri:t.attributes.URI},void(void 0!==t.attributes.IV&&(o.iv=t.attributes.IV))):void this.trigger("warn",{message:"ignoring key declaration without URI"}):void this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){if(!isFinite(t.number))return void this.trigger("warn",{message:"ignoring invalid media sequence: "+t.number});this.manifest.mediaSequence=t.number},"discontinuity-sequence":function(){if(!isFinite(t.number))return void this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+t.number});this.manifest.discontinuitySequence=t.number,c=t.number},"playlist-type":function(){if(!/VOD|EVENT/.test(t.playlistType))return void this.trigger("warn",{message:"ignoring unknown playlist type: "+t.playlist});this.manifest.playlistType=t.playlistType},map:function(){a={},t.uri&&(a.uri=t.uri),t.byterange&&(a.byterange=t.byterange)},"stream-inf":function(){if(this.manifest.playlists=n,this.manifest.mediaGroups=this.manifest.mediaGroups||l,!t.attributes)return void this.trigger("warn",{message:"ignoring empty stream-inf attributes"});s.attributes||(s.attributes={}),$s(s.attributes,t.attributes)},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||l,!(t.attributes&&t.attributes.TYPE&&t.attributes["GROUP-ID"]&&t.attributes.NAME))return void this.trigger("warn",{message:"ignoring incomplete or missing media group"});var r=this.manifest.mediaGroups[t.attributes.TYPE];r[t.attributes["GROUP-ID"]]=r[t.attributes["GROUP-ID"]]||{},e=r[t.attributes["GROUP-ID"]],i={default:/yes/i.test(t.attributes.DEFAULT)},i.default?i.autoselect=!0:i.autoselect=/yes/i.test(t.attributes.AUTOSELECT),t.attributes.LANGUAGE&&(i.language=t.attributes.LANGUAGE),t.attributes.URI&&(i.uri=t.attributes.URI),t.attributes["INSTREAM-ID"]&&(i.instreamId=t.attributes["INSTREAM-ID"]),t.attributes.CHARACTERISTICS&&(i.characteristics=t.attributes.CHARACTERISTICS),t.attributes.FORCED&&(i.forced=/yes/i.test(t.attributes.FORCED)),e[t.attributes.NAME]=i},discontinuity:function(){c+=1,s.discontinuity=!0,this.manifest.discontinuityStarts.push(n.length)},"program-date-time":function(){void 0===this.manifest.dateTimeString&&(this.manifest.dateTimeString=t.dateTimeString,this.manifest.dateTimeObject=t.dateTimeObject),s.dateTimeString=t.dateTimeString,s.dateTimeObject=t.dateTimeObject},targetduration:function(){if(!isFinite(t.duration)||t.duration<0)return void this.trigger("warn",{message:"ignoring invalid target duration: "+t.duration});this.manifest.targetDuration=t.duration},totalduration:function(){if(!isFinite(t.duration)||t.duration<0)return void this.trigger("warn",{message:"ignoring invalid total duration: "+t.duration});this.manifest.totalDuration=t.duration},start:function(){if(!t.attributes||isNaN(t.attributes["TIME-OFFSET"]))return void this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"});this.manifest.start={timeOffset:t.attributes["TIME-OFFSET"],precise:t.attributes.PRECISE}},"cue-out":function(){s.cueOut=t.data},"cue-out-cont":function(){s.cueOutCont=t.data},"cue-in":function(){s.cueIn=t.data}})[t.tagType]||u).call(r)},uri:function(){s.uri=t.uri,n.push(s),!this.manifest.targetDuration||"duration"in s||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),s.duration=this.manifest.targetDuration),o&&(s.key=o),s.timeline=c,a&&(s.map=a),s={}},comment:function(){},custom:function(){t.segment?(s.custom=s.custom||{},s.custom[t.customType]=t.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[t.customType]=t.data)}})[t.type].call(r)}),i}return Ks(e,t),e.prototype.push=function(t){this.lineStream.push(t)},e.prototype.end=function(){this.lineStream.push("\n")},e.prototype.addParser=function(t){this.parseStream.addParser(t)},e}(Qs),na=function(t){var e,i=t.attributes,r=t.segments;return{attributes:(e={NAME:i.id,BANDWIDTH:i.bandwidth,CODECS:i.codecs},e["PROGRAM-ID"]=1,e),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:"",targetDuration:i.duration,segments:r,mediaSequence:r.length?r[0].number:1}},sa=function(t){var e,i=t.attributes,r=t.segments;return void 0===r&&(r=[{uri:i.baseUrl,timeline:i.periodIndex,resolvedUri:i.baseUrl||"",duration:i.sourceDuration,number:0}],i.duration=i.sourceDuration),{attributes:(e={NAME:i.id,BANDWIDTH:i.bandwidth},e["PROGRAM-ID"]=1,e),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:i.baseUrl||"",targetDuration:i.duration,segments:r,mediaSequence:r.length?r[0].number:1}},aa=function(t){return t.reduce(function(t,e){var i=e.attributes.role&&e.attributes.role.value||"main",r=e.attributes.lang||"",n="main";return r&&(n=e.attributes.lang+" ("+i+")"),t[n]&&t[n].playlists[0].attributes.BANDWIDTH>e.attributes.bandwidth?t:(t[n]={language:r,autoselect:!0,default:"main"===i,playlists:[na(e)],uri:""},t)},{})},oa=function(t){return t.reduce(function(t,e){var i=e.attributes.lang||"text";return t[i]?t:(t[i]={language:i,default:!1,autoselect:!1,playlists:[sa(e)],uri:""},t)},{})},ua=function(t){var e,i=t.attributes,r=t.segments;return{attributes:(e={NAME:i.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:i.width,height:i.height},CODECS:i.codecs,BANDWIDTH:i.bandwidth},e["PROGRAM-ID"]=1,e),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:"",targetDuration:i.duration,segments:r,mediaSequence:r.length?r[0].number:1}},la=function(t){var e;if(!t.length)return{};var i=t[0].attributes,r=i.sourceDuration,n=i.minimumUpdatePeriod,s=void 0===n?0:n,a=function(t){var e=t.attributes;return"video/mp4"===e.mimeType||"video"===e.contentType},o=function(t){var e=t.attributes;return"audio/mp4"===e.mimeType||"audio"===e.contentType},u=function(t){var e=t.attributes;return"text/vtt"===e.mimeType||"text"===e.contentType},l=t.filter(a).map(ua),c=t.filter(o),h=t.filter(u),d={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:(e={AUDIO:{},VIDEO:{}},e["CLOSED-CAPTIONS"]={},e.SUBTITLES={},e),uri:"",duration:r,playlists:l,minimumUpdatePeriod:1e3*s};return c.length&&(d.mediaGroups.AUDIO.audio=aa(c)),h.length&&(d.mediaGroups.SUBTITLES.subs=oa(h)),d},ca="function"==typeof Symbol&&"symbol"===Pe(Symbol.iterator)?function(t){return void 0===t?"undefined":Pe(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":void 0===t?"undefined":Pe(t)},ha=function(t){return!!t&&"object"===(void 0===t?"undefined":ca(t))},da=function t(){for(var e=arguments.length,i=Array(e),r=0;r<e;r++)i[r]=arguments[r];return i.reduce(function(e,i){return Object.keys(i).forEach(function(r){Array.isArray(e[r])&&Array.isArray(i[r])?e[r]=e[r].concat(i[r]):ha(e[r])&&ha(i[r])?e[r]=t(e[r],i[r]):e[r]=i[r]}),e},{})},pa=function(t,e){return/^[a-z]+:/i.test(e)?e:(/\/\//i.test(t)||(t=Xs.buildAbsoluteURL(ve.location.href,t)),Xs.buildAbsoluteURL(t,e))},fa=function(t){var e=t.baseUrl,i=void 0===e?"":e,r=t.source,n=void 0===r?"":r,s=t.range,a=void 0===s?"":s,o={uri:n,resolvedUri:pa(i||"",n)};if(a){var u=a.split("-"),l=parseInt(u[0],10),c=parseInt(u[1],10);o.byterange={length:c-l,offset:l}}return o},ma=function(t,e,i){var r=t.NOW,n=t.clientOffset,s=t.availabilityStartTime,a=t.timescale,o=void 0===a?1:a,u=t.start,l=void 0===u?0:u,c=t.minimumUpdatePeriod,h=void 0===c?0:c,d=(r+n)/1e3,p=s+l,f=d+h,m=f-p;return Math.ceil((m*o-e)/i)},ga=function(t,e){for(var i=t.type,r=void 0===i?"static":i,n=t.minimumUpdatePeriod,s=void 0===n?0:n,a=t.media,o=void 0===a?"":a,u=t.sourceDuration,l=t.timescale,c=void 0===l?1:l,h=t.startNumber,d=void 0===h?1:h,p=t.periodIndex,f=[],m=-1,g=0;g<e.length;g++){var y=e[g],v=y.d,_=y.r||0,b=y.t||0;m<0&&(m=b),b&&b>m&&(m=b);var T=void 0;if(_<0){var S=g+1;T=S===e.length?"dynamic"===r&&s>0&&o.indexOf("$Number$")>0?ma(t,m,v):(u*c-m)/v:(e[S].t-m)/v}else T=_+1;for(var k=d+f.length+T,E=d+f.length;E<k;)f.push({number:E,duration:v/c,time:m,timeline:p}),m+=v,E++}return f},ya=function(t,e){for(var i=[],r=t;r<e;r++)i.push(r);return i},va=function(t){return t.reduce(function(t,e){return t.concat(e)},[])},_a=function(t){if(!t.length)return[];for(var e=[],i=0;i<t.length;i++)e.push(t[i]);return e},ba={static:function(t){var e=t.duration,i=t.timescale,r=void 0===i?1:i,n=t.sourceDuration;return{start:0,end:Math.ceil(n/(e/r))}},dynamic:function(t){var e=t.NOW,i=t.clientOffset,r=t.availabilityStartTime,n=t.timescale,s=void 0===n?1:n,a=t.duration,o=t.start,u=void 0===o?0:o,l=t.minimumUpdatePeriod,c=void 0===l?0:l,h=t.timeShiftBufferDepth,d=void 0===h?1/0:h,p=(e+i)/1e3,f=r+u,m=p+c,g=m-f,y=Math.ceil(g*s/a),v=Math.floor((p-f-d)*s/a),_=Math.floor((p-f)*s/a);return{start:Math.max(0,v),end:Math.min(y,_)}}},Ta=function(t){return function(e,i){var r=t.duration,n=t.timescale,s=void 0===n?1:n,a=t.periodIndex,o=t.startNumber;return{number:(void 0===o?1:o)+e,duration:r/s,timeline:a,time:i*r}}},Sa=function(t){var e=t.type,i=void 0===e?"static":e,r=t.duration,n=t.timescale,s=void 0===n?1:n,a=t.sourceDuration,o=ba[i](t),u=o.start,l=o.end,c=ya(u,l).map(Ta(t));if("static"===i){var h=c.length-1;c[h].duration=a-r/s*h}return c},ka=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,Ea=function(t){return function(e,i,r,n){if("$$"===e)return"$";if(void 0===t[i])return e;var s=""+t[i];return"RepresentationID"===i?s:(n=r?parseInt(n,10):1,s.length>=n?s:""+new Array(n-s.length+1).join("0")+s)}},Ca=function(t,e){return t.replace(ka,Ea(e))},wa=function(t,e){return t.duration||e?t.duration?Sa(t):ga(t,e):[{number:t.startNumber||1,duration:t.sourceDuration,time:0,timeline:t.periodIndex}]},Aa=function(t,e){var i={RepresentationID:t.id,Bandwidth:t.bandwidth||0},r=t.initialization,n=void 0===r?{sourceURL:"",range:""}:r,s=fa({baseUrl:t.baseUrl,source:Ca(n.sourceURL,i),range:n.range});return wa(t,e).map(function(e){i.Number=e.number,i.Time=e.time;var r=Ca(t.media||"",i);return{uri:r,timeline:e.timeline,duration:e.duration,resolvedUri:pa(t.baseUrl||"",r),map:s,number:e.number}})},La={INVALID_NUMBER_OF_PERIOD:"INVALID_NUMBER_OF_PERIOD",DASH_EMPTY_MANIFEST:"DASH_EMPTY_MANIFEST",DASH_INVALID_XML:"DASH_INVALID_XML",NO_BASE_URL:"NO_BASE_URL",MISSING_SEGMENT_INFORMATION:"MISSING_SEGMENT_INFORMATION",SEGMENT_TIME_UNSPECIFIED:"SEGMENT_TIME_UNSPECIFIED",UNSUPPORTED_UTC_TIMING_SCHEME:"UNSUPPORTED_UTC_TIMING_SCHEME"},Oa=function(t,e){var i=t.baseUrl,r=t.initialization,n=void 0===r?{}:r,s=fa({baseUrl:i,source:n.sourceURL,range:n.range}),a=fa({baseUrl:i,source:e.media,range:e.mediaRange});return a.map=s,a},Pa=function(t,e){var i=t.duration,r=t.segmentUrls,n=void 0===r?[]:r;if(!i&&!e||i&&e)throw new Error(La.SEGMENT_TIME_UNSPECIFIED);var s=n.map(function(e){return Oa(t,e)}),a=void 0;return i&&(a=Sa(t)),e&&(a=ga(t,e)),a.map(function(t,e){if(s[e]){var i=s[e];return i.timeline=t.timeline,i.duration=t.duration,i.number=t.number,i}}).filter(function(t){return t})},Ia=function(t){var e=t.baseUrl,i=t.initialization,r=void 0===i?{}:i,n=t.sourceDuration,s=t.timescale,a=void 0===s?1:s,o=t.indexRange,u=void 0===o?"":o,l=t.duration;if(!e)throw new Error(La.NO_BASE_URL);var c=fa({baseUrl:e,source:r.sourceURL,range:r.range}),h=fa({baseUrl:e,source:e,range:u});if(h.map=c,l){var d=Sa(t);d.length&&(h.duration=d[0].duration,h.timeline=d[0].timeline)}else n&&(h.duration=n/a,h.timeline=0);return h.number=0,[h]},xa=function(t){var e=t.attributes,i=t.segmentInfo,r=void 0,n=void 0;if(i.template?(n=Aa,r=da(e,i.template)):i.base?(n=Ia,r=da(e,i.base)):i.list&&(n=Pa,r=da(e,i.list)),!n)return{attributes:e};var s=n(r,i.timeline);if(r.duration){var a=r,o=a.duration,u=a.timescale,l=void 0===u?1:u;r.duration=o/l}else s.length?r.duration=s.reduce(function(t,e){return Math.max(t,Math.ceil(e.duration))},0):r.duration=0;return{attributes:r,segments:s}},Da=function(t){return t.map(xa)},Ma=function(t,e){return _a(t.childNodes).filter(function(t){return t.tagName===e})},Ra=function(t){return t.textContent.trim()},Ua=function(t){var e=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/,i=e.exec(t);if(!i)return 0;var r=i.slice(1),n=r[0],s=r[1],a=r[2],o=r[3],u=r[4],l=r[5];return 31536e3*parseFloat(n||0)+2592e3*parseFloat(s||0)+86400*parseFloat(a||0)+3600*parseFloat(o||0)+60*parseFloat(u||0)+parseFloat(l||0)},Na=function(t){return/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(t)&&(t+="Z"),Date.parse(t)},Ba={mediaPresentationDuration:function(t){return Ua(t)},availabilityStartTime:function(t){return Na(t)/1e3},minimumUpdatePeriod:function(t){return Ua(t)},timeShiftBufferDepth:function(t){return Ua(t)},start:function(t){return Ua(t)},width:function(t){return parseInt(t,10)},height:function(t){return parseInt(t,10)},bandwidth:function(t){return parseInt(t,10)},startNumber:function(t){return parseInt(t,10)},timescale:function(t){return parseInt(t,10)},duration:function(t){var e=parseInt(t,10);return isNaN(e)?Ua(t):e},d:function(t){return parseInt(t,10)},t:function(t){return parseInt(t,10)},r:function(t){return parseInt(t,10)},DEFAULT:function(t){return t}},ja=function(t){return t&&t.attributes?_a(t.attributes).reduce(function(t,e){var i=Ba[e.name]||Ba.DEFAULT;return t[e.name]=i(e.value),t},{}):{}},Fa=function(t,e){return e.length?va(t.map(function(t){return e.map(function(e){return pa(t,Ra(e))})})):t},Ha=function(t){var e=Ma(t,"SegmentTemplate")[0],i=Ma(t,"SegmentList")[0],r=i&&Ma(i,"SegmentURL").map(function(t){return da({tag:"SegmentURL"},ja(t))}),n=Ma(t,"SegmentBase")[0],s=i||e,a=s&&Ma(s,"SegmentTimeline")[0],o=i||n||e,u=o&&Ma(o,"Initialization")[0],l=e&&ja(e);l&&u?l.initialization=u&&ja(u):l&&l.initialization&&(l.initialization={sourceURL:l.initialization});var c={template:l,timeline:a&&Ma(a,"S").map(function(t){return ja(t)}),list:i&&da(ja(i),{segmentUrls:r,initialization:ja(u)}),base:n&&da(ja(n),{initialization:ja(u)})};return Object.keys(c).forEach(function(t){c[t]||delete c[t]}),c},Va=function(t,e,i){return function(r){var n=Ma(r,"BaseURL"),s=Fa(e,n),a=da(t,ja(r)),o=Ha(r);return s.map(function(t){return{segmentInfo:da(i,o),attributes:da(a,{baseUrl:t})}})}},qa=function(t,e,i){return function(r){var n=ja(r),s=Fa(e,Ma(r,"BaseURL")),a=Ma(r,"Role")[0],o={role:ja(a)},u=da(t,n,o),l=Ha(r),c=Ma(r,"Representation"),h=da(i,l);return va(c.map(Va(u,s,h)))}},Wa=function(t,e){return function(i,r){var n=Fa(e,Ma(i,"BaseURL")),s=ja(i),a=da(t,s,{periodIndex:r}),o=Ma(i,"AdaptationSet"),u=Ha(i);return va(o.map(qa(a,n,u)))}},Ga=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.manifestUri,r=void 0===i?"":i,n=e.NOW,s=void 0===n?Date.now():n,a=e.clientOffset,o=void 0===a?0:a,u=Ma(t,"Period");if(1!==u.length)throw new Error(La.INVALID_NUMBER_OF_PERIOD);var l=ja(t),c=Fa([r],Ma(t,"BaseURL"));return l.sourceDuration=l.mediaPresentationDuration||0,l.NOW=s,l.clientOffset=o,va(u.map(Wa(l,c)))},za=function(t){if(""===t)throw new Error(La.DASH_EMPTY_MANIFEST);var e=new ve.DOMParser,i=e.parseFromString(t,"application/xml"),r=i&&"MPD"===i.documentElement.tagName?i.documentElement:null;if(!r||r&&r.getElementsByTagName("parsererror").length>0)throw new Error(La.DASH_INVALID_XML);return r},Xa=function(t){var e=Ma(t,"UTCTiming")[0];if(!e)return null;var i=ja(e);switch(i.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":i.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":i.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":i.method="DIRECT",i.value=Date.parse(i.value);break;case"urn:mpeg:dash:utc:http-ntp:2014":case"urn:mpeg:dash:utc:ntp:2014":case"urn:mpeg:dash:utc:sntp:2014":default:throw new Error(La.UNSUPPORTED_UTC_TIMING_SCHEME)}return i},Ya=function(t,e){return la(Da(Ga(za(t),e)))},$a=function(t){return Xa(za(t))},Ka=function(t){return t>>>0},Ja={toUnsigned:Ka},Qa=Ja.toUnsigned;qs=function(t,e){var i,r,n,s,a,o=[];if(!e.length)return null;for(i=0;i<t.byteLength;)r=Qa(t[i]<<24|t[i+1]<<16|t[i+2]<<8|t[i+3]),n=Ws(t.subarray(i+4,i+8)),s=r>1?i+r:t.byteLength,n===e[0]&&(1===e.length?o.push(t.subarray(i+8,s)):(a=qs(t.subarray(i+8,s),e.slice(1)),a.length&&(o=o.concat(a)))),i=s;return o},Ws=function(t){var e="";return e+=String.fromCharCode(t[0]),e+=String.fromCharCode(t[1]),e+=String.fromCharCode(t[2]),e+=String.fromCharCode(t[3])},Gs=function(t){var e={};return qs(t,["moov","trak"]).reduce(function(t,e){var i,r,n,s,a;return(i=qs(e,["tkhd"])[0])?(r=i[0],n=0===r?12:20,s=Qa(i[n]<<24|i[n+1]<<16|i[n+2]<<8|i[n+3]),(a=qs(e,["mdia","mdhd"])[0])?(r=a[0],n=0===r?12:20,t[s]=Qa(a[n]<<24|a[n+1]<<16|a[n+2]<<8|a[n+3]),t):null):null},e)},zs=function(t,e){var i,r,n;return i=qs(e,["moof","traf"]),r=[].concat.apply([],i.map(function(e){return qs(e,["tfhd"]).map(function(i){var r,n,s;return r=Qa(i[4]<<24|i[5]<<16|i[6]<<8|i[7]),n=t[r]||9e4,s=qs(e,["tfdt"]).map(function(t){var e,i;return e=t[0],i=Qa(t[4]<<24|t[5]<<16|t[6]<<8|t[7]),1===e&&(i*=Math.pow(2,32),i+=Qa(t[8]<<24|t[9]<<16|t[10]<<8|t[11])),i})[0],(s=s||1/0)/n})})),n=Math.min.apply(null,r),isFinite(n)?n:0};var Za={parseType:Ws,timescale:Gs,startTime:zs},to={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21},eo=function(){this.init=function(){var t={};this.on=function(e,i){t[e]||(t[e]=[]),t[e]=t[e].concat(i)},this.off=function(e,i){var r;return!!t[e]&&(r=t[e].indexOf(i),t[e]=t[e].slice(),t[e].splice(r,1),r>-1)},this.trigger=function(e){var i,r,n,s;if(i=t[e])if(2===arguments.length)for(n=i.length,r=0;r<n;++r)i[r].call(this,arguments[1]);else{for(s=[],r=arguments.length,r=1;r<arguments.length;++r)s.push(arguments[r]);for(n=i.length,r=0;r<n;++r)i[r].apply(this,s)}},this.dispose=function(){t={}}}};eo.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),t},eo.prototype.push=function(t){this.trigger("data",t)},eo.prototype.flush=function(t){this.trigger("done",t)};var io=eo,ro=function(t,e){var i=1;for(t>e&&(i=-1);Math.abs(e-t)>4294967296;)t+=8589934592*i;return t},no=function t(e){var i,r;t.prototype.init.call(this),this.type_=e,this.push=function(t){t.type===this.type_&&(void 0===r&&(r=t.dts),t.dts=ro(t.dts,r),t.pts=ro(t.pts,r),i=t.dts,this.trigger("data",t))},this.flush=function(){r=i,this.trigger("done")},this.discontinuity=function(){r=void 0,i=void 0}};no.prototype=new io;var so={TimestampRolloverStream:no,handleRollover:ro},ao=function(t){var e=31&t[1];return e<<=8,e|=t[2]},oo=function(t){return!!(64&t[1])},uo=function(t){var e=0;return(48&t[3])>>>4>1&&(e+=t[4]+1),e},lo=function(t,e){var i=ao(t);return 0===i?"pat":i===e?"pmt":e?"pes":null},co=function(t){var e=oo(t),i=4+uo(t);return e&&(i+=t[i]+1),(31&t[i+10])<<8|t[i+11]},ho=function(t){var e={},i=oo(t),r=4+uo(t);if(i&&(r+=t[r]+1),1&t[r+5]){var n,s,a;n=(15&t[r+1])<<8|t[r+2],s=3+n-4,a=(15&t[r+10])<<8|t[r+11];for(var o=12+a;o<s;){var u=r+o;e[(31&t[u+1])<<8|t[u+2]]=t[u],o+=5+((15&t[u+3])<<8|t[u+4])}return e}},po=function(t,e){switch(e[ao(t)]){case to.H264_STREAM_TYPE:return"video";case to.ADTS_STREAM_TYPE:return"audio";case to.METADATA_STREAM_TYPE:return"timed-metadata";default:return null}},fo=function(t){if(!oo(t))return null;var e=4+uo(t);if(e>=t.byteLength)return null;var i,r=null;return i=t[e+7],192&i&&(r={},r.pts=(14&t[e+9])<<27|(255&t[e+10])<<20|(254&t[e+11])<<12|(255&t[e+12])<<5|(254&t[e+13])>>>3,r.pts*=4,r.pts+=(6&t[e+13])>>>1,r.dts=r.pts,64&i&&(r.dts=(14&t[e+14])<<27|(255&t[e+15])<<20|(254&t[e+16])<<12|(255&t[e+17])<<5|(254&t[e+18])>>>3,r.dts*=4,r.dts+=(6&t[e+18])>>>1)),r},mo=function(t){switch(t){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}},go=function(t){for(var e,i=4+uo(t),r=t.subarray(i),n=0,s=0,a=!1;s<r.byteLength-3;s++)if(1===r[s+2]){n=s+5;break}for(;n<r.byteLength;)switch(r[n]){case 0:if(0!==r[n-1]){n+=2;break}if(0!==r[n-2]){n++;break}s+3!==n-2&&"slice_layer_without_partitioning_rbsp_idr"===(e=mo(31&r[s+3]))&&(a=!0);do{n++}while(1!==r[n]&&n<r.length);s=n-2,n+=3;break;case 1:if(0!==r[n-1]||0!==r[n-2]){n+=3;break
}e=mo(31&r[s+3]),"slice_layer_without_partitioning_rbsp_idr"===e&&(a=!0),s=n-2,n+=3;break;default:n+=3}return r=r.subarray(s),n-=s,s=0,r&&r.byteLength>3&&"slice_layer_without_partitioning_rbsp_idr"===(e=mo(31&r[s+3]))&&(a=!0),a},yo={parseType:lo,parsePat:co,parsePmt:ho,parsePayloadUnitStartIndicator:oo,parsePesType:po,parsePesTime:fo,videoPacketContainsKeyFrame:go},vo=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],_o=function(t){return t[0]<<21|t[1]<<14|t[2]<<7|t[3]},bo=function(t,e,i){var r,n="";for(r=e;r<i;r++)n+="%"+("00"+t[r].toString(16)).slice(-2);return n},To=function(t,e,i){return unescape(bo(t,e,i))},So=function(t,e){var i=t[e+6]<<21|t[e+7]<<14|t[e+8]<<7|t[e+9];return(16&t[e+5])>>4?i+20:i+10},ko=function(t,e){var i=(224&t[e+5])>>5,r=t[e+4]<<3;return 6144&t[e+3]|r|i},Eo=function(t,e){return t[e]==="I".charCodeAt(0)&&t[e+1]==="D".charCodeAt(0)&&t[e+2]==="3".charCodeAt(0)?"timed-metadata":!0&t[e]&&240==(240&t[e+1])?"audio":null},Co=function(t){for(var e=0;e+5<t.length;){if(255===t[e]&&240==(246&t[e+1]))return vo[(60&t[e+2])>>>2];e++}return null},wo=function(t){var e,i,r;e=10,64&t[5]&&(e+=4,e+=_o(t.subarray(10,14)));do{if((i=_o(t.subarray(e+4,e+8)))<1)return null;if("PRIV"===String.fromCharCode(t[e],t[e+1],t[e+2],t[e+3])){r=t.subarray(e+10,e+i+10);for(var n=0;n<r.byteLength;n++)if(0===r[n]){var s=To(r,0,n);if("com.apple.streaming.transportStreamTimestamp"===s){var a=r.subarray(n+1),o=(1&a[3])<<30|a[4]<<22|a[5]<<14|a[6]<<6|a[7]>>>2;return o*=4,o+=3&a[7]}break}}e+=10,e+=i}while(e<t.byteLength);return null},Ao={parseId3TagSize:So,parseAdtsSize:ko,parseType:Eo,parseSampleRate:Co,parseAacTimestamp:wo},Lo=so.handleRollover,Oo={};Oo.ts=yo,Oo.aac=Ao;var Po,Io=function(t){return t[0]==="I".charCodeAt(0)&&t[1]==="D".charCodeAt(0)&&t[2]==="3".charCodeAt(0)},xo=function(t,e){for(var i,r=0,n=188;n<t.byteLength;)if(71!==t[r]||71!==t[n])r++,n++;else{switch(i=t.subarray(r,n),Oo.ts.parseType(i,e.pid)){case"pat":e.pid||(e.pid=Oo.ts.parsePat(i));break;case"pmt":e.table||(e.table=Oo.ts.parsePmt(i))}if(e.pid&&e.table)return;r+=188,n+=188}},Do=function(t,e,i){for(var r,n,s,a,o=0,u=188,l=!1;u<t.byteLength;)if(71!==t[o]||71!==t[u])o++,u++;else{switch(r=t.subarray(o,u),Oo.ts.parseType(r,e.pid)){case"pes":n=Oo.ts.parsePesType(r,e.table),s=Oo.ts.parsePayloadUnitStartIndicator(r),"audio"===n&&s&&(a=Oo.ts.parsePesTime(r))&&(a.type="audio",i.audio.push(a),l=!0)}if(l)break;o+=188,u+=188}for(u=t.byteLength,o=u-188,l=!1;o>=0;)if(71!==t[o]||71!==t[u])o--,u--;else{switch(r=t.subarray(o,u),Oo.ts.parseType(r,e.pid)){case"pes":n=Oo.ts.parsePesType(r,e.table),s=Oo.ts.parsePayloadUnitStartIndicator(r),"audio"===n&&s&&(a=Oo.ts.parsePesTime(r))&&(a.type="audio",i.audio.push(a),l=!0)}if(l)break;o-=188,u-=188}},Mo=function(t,e,i){for(var r,n,s,a,o,u,l,c=0,h=188,d=!1,p={data:[],size:0};h<t.byteLength;)if(71!==t[c]||71!==t[h])c++,h++;else{switch(r=t.subarray(c,h),Oo.ts.parseType(r,e.pid)){case"pes":if(n=Oo.ts.parsePesType(r,e.table),s=Oo.ts.parsePayloadUnitStartIndicator(r),"video"===n&&(s&&!d&&(a=Oo.ts.parsePesTime(r))&&(a.type="video",i.video.push(a),d=!0),!i.firstKeyFrame)){if(s&&0!==p.size){for(o=new Uint8Array(p.size),u=0;p.data.length;)l=p.data.shift(),o.set(l,u),u+=l.byteLength;Oo.ts.videoPacketContainsKeyFrame(o)&&(i.firstKeyFrame=Oo.ts.parsePesTime(o),i.firstKeyFrame.type="video"),p.size=0}p.data.push(r),p.size+=r.byteLength}}if(d&&i.firstKeyFrame)break;c+=188,h+=188}for(h=t.byteLength,c=h-188,d=!1;c>=0;)if(71!==t[c]||71!==t[h])c--,h--;else{switch(r=t.subarray(c,h),Oo.ts.parseType(r,e.pid)){case"pes":n=Oo.ts.parsePesType(r,e.table),s=Oo.ts.parsePayloadUnitStartIndicator(r),"video"===n&&s&&(a=Oo.ts.parsePesTime(r))&&(a.type="video",i.video.push(a),d=!0)}if(d)break;c-=188,h-=188}},Ro=function(t,e){if(t.audio&&t.audio.length){var i=e;void 0===i&&(i=t.audio[0].dts),t.audio.forEach(function(t){t.dts=Lo(t.dts,i),t.pts=Lo(t.pts,i),t.dtsTime=t.dts/9e4,t.ptsTime=t.pts/9e4})}if(t.video&&t.video.length){var r=e;if(void 0===r&&(r=t.video[0].dts),t.video.forEach(function(t){t.dts=Lo(t.dts,r),t.pts=Lo(t.pts,r),t.dtsTime=t.dts/9e4,t.ptsTime=t.pts/9e4}),t.firstKeyFrame){var n=t.firstKeyFrame;n.dts=Lo(n.dts,r),n.pts=Lo(n.pts,r),n.dtsTime=n.dts/9e4,n.ptsTime=n.dts/9e4}}},Uo=function(t){for(var e,i=!1,r=0,n=null,s=null,a=0,o=0;t.length-o>=3;){switch(Oo.aac.parseType(t,o)){case"timed-metadata":if(t.length-o<10){i=!0;break}if((a=Oo.aac.parseId3TagSize(t,o))>t.length){i=!0;break}null===s&&(e=t.subarray(o,o+a),s=Oo.aac.parseAacTimestamp(e)),o+=a;break;case"audio":if(t.length-o<7){i=!0;break}if((a=Oo.aac.parseAdtsSize(t,o))>t.length){i=!0;break}null===n&&(e=t.subarray(o,o+a),n=Oo.aac.parseSampleRate(e)),r++,o+=a;break;default:o++}if(i)return null}if(null===n||null===s)return null;var u=9e4/n;return{audio:[{type:"audio",dts:s,pts:s},{type:"audio",dts:s+1024*r*u,pts:s+1024*r*u}]}},No=function(t){var e={pid:null,table:null},i={};xo(t,e);for(var r in e.table)if(e.table.hasOwnProperty(r)){var n=e.table[r];switch(n){case to.H264_STREAM_TYPE:i.video=[],Mo(t,e,i),0===i.video.length&&delete i.video;break;case to.ADTS_STREAM_TYPE:i.audio=[],Do(t,e,i),0===i.audio.length&&delete i.audio}}return i},Bo=function(t,e){var i,r=Io(t);return(i=r?Uo(t):No(t))&&(i.audio||i.video)?(Ro(i,e),i):null},jo={inspect:Bo},Fo=t(function(t,e){"use strict";function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=function(){var t=[[[],[],[],[],[]],[[],[],[],[],[]]],e=t[0],i=t[1],r=e[4],n=i[4],s=void 0,a=void 0,o=void 0,u=[],l=[],c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0;for(s=0;s<256;s++)l[(u[s]=s<<1^283*(s>>7))^s]=s;for(a=o=0;!r[a];a^=c||1,o=l[o]||1)for(p=o^o<<1^o<<2^o<<3^o<<4,p=p>>8^255&p^99,r[a]=p,n[p]=a,d=u[h=u[c=u[a]]],m=16843009*d^65537*h^257*c^16843008*a,f=257*u[p]^16843008*p,s=0;s<4;s++)e[s][a]=f=f<<24^f>>>8,i[s][p]=m=m<<24^m>>>8;for(s=0;s<5;s++)e[s]=e[s].slice(0),i[s]=i[s].slice(0);return t},s=null,a=function(){function t(e){i(this,t),s||(s=n()),this._tables=[[s[0][0].slice(),s[0][1].slice(),s[0][2].slice(),s[0][3].slice(),s[0][4].slice()],[s[1][0].slice(),s[1][1].slice(),s[1][2].slice(),s[1][3].slice(),s[1][4].slice()]];var r=void 0,a=void 0,o=void 0,u=void 0,l=void 0,c=this._tables[0][4],h=this._tables[1],d=e.length,p=1;if(4!==d&&6!==d&&8!==d)throw new Error("Invalid aes key size");for(u=e.slice(0),l=[],this._key=[u,l],r=d;r<4*d+28;r++)o=u[r-1],(r%d==0||8===d&&r%d==4)&&(o=c[o>>>24]<<24^c[o>>16&255]<<16^c[o>>8&255]<<8^c[255&o],r%d==0&&(o=o<<8^o>>>24^p<<24,p=p<<1^283*(p>>7))),u[r]=u[r-d]^o;for(a=0;r;a++,r--)o=u[3&a?r:r-4],l[a]=r<=4||a<4?o:h[0][c[o>>>24]]^h[1][c[o>>16&255]]^h[2][c[o>>8&255]]^h[3][c[255&o]]}return r(t,[{key:"decrypt",value:function(t,e,i,r,n,s){var a=this._key[1],o=t^a[0],u=r^a[1],l=i^a[2],c=e^a[3],h=void 0,d=void 0,p=void 0,f=a.length/4-2,m=void 0,g=4,y=this._tables[1],v=y[0],_=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<f;m++)h=v[o>>>24]^_[u>>16&255]^b[l>>8&255]^T[255&c]^a[g],d=v[u>>>24]^_[l>>16&255]^b[c>>8&255]^T[255&o]^a[g+1],p=v[l>>>24]^_[c>>16&255]^b[o>>8&255]^T[255&u]^a[g+2],c=v[c>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&l]^a[g+3],g+=4,o=h,u=d,l=p;for(m=0;m<4;m++)n[(3&-m)+s]=S[o>>>24]<<24^S[u>>16&255]<<16^S[l>>8&255]<<8^S[255&c]^a[g++],h=o,o=u,u=l,l=c,c=h}}]),t}();e.default=a,t.exports=e.default}),Ho=t(function(t,e){"use strict";function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=function(){function t(){i(this,t),this.listeners={}}return r(t,[{key:"on",value:function(t,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(e)}},{key:"off",value:function(t,e){var i=void 0;return!!this.listeners[t]&&(i=this.listeners[t].indexOf(e),this.listeners[t].splice(i,1),i>-1)}},{key:"trigger",value:function(t){var e=void 0,i=void 0,r=void 0,n=void 0;if(e=this.listeners[t])if(2===arguments.length)for(r=e.length,i=0;i<r;++i)e[i].call(this,arguments[1]);else for(n=Array.prototype.slice.call(arguments,1),r=e.length,i=0;i<r;++i)e[i].apply(this,n)}},{key:"dispose",value:function(){this.listeners={}}},{key:"pipe",value:function(t){this.on("data",function(e){t.push(e)})}}]),t}();e.default=n,t.exports=e.default}),Vo=t(function(t,e){"use strict";function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":Pe(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),s=function(t,e,i){for(var r=!0;r;){var n=t,s=e,a=i;r=!1,null===n&&(n=Function.prototype);var o=Object.getOwnPropertyDescriptor(n,s);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(a)}var l=Object.getPrototypeOf(n);if(null===l)return;t=l,e=s,i=a,r=!0,o=l=void 0}},a=function(t){return t&&t.__esModule?t:{default:t}}(Ho),o=function(t){function e(){i(this,e),s(Object.getPrototypeOf(e.prototype),"constructor",this).call(this,a.default),this.jobs=[],this.delay=1,this.timeout_=null}return r(e,t),n(e,[{key:"processJob_",value:function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null}},{key:"push",value:function(t){this.jobs.push(t),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))}}]),e}(a.default);e.default=o,t.exports=e.default}),qo=function(t){var e=Po[t.byteLength%16||0],i=new Uint8Array(t.byteLength+e.length);return i.set(t),i.set(e,t.byteLength),i};Po=[[16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16],[15,15,15,15,15,15,15,15,15,15,15,15,15,15,15],[14,14,14,14,14,14,14,14,14,14,14,14,14,14],[13,13,13,13,13,13,13,13,13,13,13,13,13],[12,12,12,12,12,12,12,12,12,12,12,12],[11,11,11,11,11,11,11,11,11,11,11],[10,10,10,10,10,10,10,10,10,10],[9,9,9,9,9,9,9,9,9],[8,8,8,8,8,8,8,8],[7,7,7,7,7,7,7],[6,6,6,6,6,6],[5,5,5,5,5],[4,4,4,4],[3,3,3],[2,2],[1]];var Wo=function(t){return t.subarray(0,t.byteLength-t[t.byteLength-1])},Go=qo,zo=Wo,Xo={pad:Go,unpad:zo},Yo=t(function(t,e){"use strict";function i(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),s=i(Fo),a=i(Vo),o=function(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24},u=function(t,e,i){var r=new Int32Array(t.buffer,t.byteOffset,t.byteLength>>2),n=new s.default(Array.prototype.slice.call(e)),a=new Uint8Array(t.byteLength),u=new Int32Array(a.buffer),l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0,g=void 0,y=void 0;for(l=i[0],c=i[1],h=i[2],d=i[3],y=0;y<r.length;y+=4)p=o(r[y]),f=o(r[y+1]),m=o(r[y+2]),g=o(r[y+3]),n.decrypt(p,f,m,g,u,y),u[y]=o(u[y]^l),u[y+1]=o(u[y+1]^c),u[y+2]=o(u[y+2]^h),u[y+3]=o(u[y+3]^d),l=p,c=f,h=m,d=g;return a};e.decrypt=u;var l=function(){function t(e,i,n,s){r(this,t);var u=t.STEP,l=new Int32Array(e.buffer),c=new Uint8Array(e.byteLength),h=0;for(this.asyncStream_=new a.default,this.asyncStream_.push(this.decryptChunk_(l.subarray(h,h+u),i,n,c)),h=u;h<l.length;h+=u)n=new Uint32Array([o(l[h-4]),o(l[h-3]),o(l[h-2]),o(l[h-1])]),this.asyncStream_.push(this.decryptChunk_(l.subarray(h,h+u),i,n,c));this.asyncStream_.push(function(){s(null,(0,Xo.unpad)(c))})}return n(t,[{key:"decryptChunk_",value:function(t,e,i,r){return function(){var n=u(t,e,i);r.set(n,t.byteOffset)}}}],[{key:"STEP",get:function(){return 32e3}}]),t}();e.Decrypter=l,e.default={Decrypter:l,decrypt:u}}),$o=t(function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){return t&&t.__esModule?t:{default:t}}(Vo);e.default={decrypt:Yo.decrypt,Decrypter:Yo.Decrypter,AsyncStream:i.default},t.exports=e.default}),Ko=function(t){return t&&t.__esModule?t.default:t}($o),Jo=function(t,e){return/^[a-z]+:/i.test(e)?e:(/\/\//i.test(t)||(t=Xs.buildAbsoluteURL(ve.location.href,t)),Xs.buildAbsoluteURL(t,e))},Qo=le.mergeOptions,Zo=le.EventTarget,tu=le.log,eu=function(t,e){["AUDIO","SUBTITLES"].forEach(function(i){for(var r in t.mediaGroups[i])for(var n in t.mediaGroups[i][r]){var s=t.mediaGroups[i][r][n];e(s,i,r,n)}})},iu=function(t,e,i){var r=e.slice();i=i||0;for(var n=Math.min(t.length,e.length+i),s=i;s<n;s++)r[s-i]=Qo(t[s],r[s-i]);return r},ru=function(t,e){t.resolvedUri||(t.resolvedUri=Jo(e,t.uri)),t.key&&!t.key.resolvedUri&&(t.key.resolvedUri=Jo(e,t.key.uri)),t.map&&!t.map.resolvedUri&&(t.map.resolvedUri=Jo(e,t.map.uri))},nu=function(t,e){var i=Qo(t,{}),r=i.playlists[e.uri];if(!r)return null;if(r.segments&&e.segments&&r.segments.length===e.segments.length&&r.mediaSequence===e.mediaSequence)return null;var n=Qo(r,e);r.segments&&(n.segments=iu(r.segments,e.segments,e.mediaSequence-r.mediaSequence)),n.segments.forEach(function(t){ru(t,n.resolvedUri)});for(var s=0;s<i.playlists.length;s++)i.playlists[s].uri===e.uri&&(i.playlists[s]=n);return i.playlists[e.uri]=n,i},su=function(t){for(var e=t.playlists.length;e--;){var i=t.playlists[e];t.playlists[i.uri]=i,i.resolvedUri=Jo(t.uri,i.uri),i.id=e,i.attributes||(i.attributes={},tu.warn("Invalid playlist STREAM-INF detected. Missing BANDWIDTH attribute."))}},au=function(t){eu(t,function(e){e.uri&&(e.resolvedUri=Jo(t.uri,e.uri))})},ou=function(t,e){var i=t.segments[t.segments.length-1];return e&&i&&i.duration?1e3*i.duration:500*(t.targetDuration||10)},uu=function(t){function e(i,r,n){Ie(this,e);var s=De(this,t.call(this));if(s.srcUrl=i,s.hls_=r,s.withCredentials=n,!s.srcUrl)throw new Error("A non-empty playlist URL is required");return s.state="HAVE_NOTHING",s.on("mediaupdatetimeout",function(){"HAVE_METADATA"===s.state&&(s.state="HAVE_CURRENT_METADATA",s.request=s.hls_.xhr({uri:Jo(s.master.uri,s.media().uri),withCredentials:s.withCredentials},function(t,e){if(s.request)return t?s.playlistRequestError(s.request,s.media().uri,"HAVE_METADATA"):void s.haveMetadata(s.request,s.media().uri)}))}),s}return xe(e,t),e.prototype.playlistRequestError=function(t,e,i){this.request=null,i&&(this.state=i),this.error={playlist:this.master.playlists[e],status:t.status,message:"HLS playlist request error at URL: "+e,responseText:t.responseText,code:t.status>=500?4:2},this.trigger("error")},e.prototype.haveMetadata=function(t,e){var i=this;this.request=null,this.state="HAVE_METADATA";var r=new ra;r.push(t.responseText),r.end(),r.manifest.uri=e,r.manifest.attributes=r.manifest.attributes||{};var n=nu(this.master,r.manifest);this.targetDuration=r.manifest.targetDuration,n?(this.master=n,this.media_=this.master.playlists[r.manifest.uri]):this.trigger("playlistunchanged"),this.media().endList||(ve.clearTimeout(this.mediaUpdateTimeout),this.mediaUpdateTimeout=ve.setTimeout(function(){i.trigger("mediaupdatetimeout")},ou(this.media(),!!n))),this.trigger("loadedplaylist")},e.prototype.dispose=function(){this.stopRequest(),ve.clearTimeout(this.mediaUpdateTimeout)},e.prototype.stopRequest=function(){if(this.request){var t=this.request;this.request=null,t.onreadystatechange=null,t.abort()}},e.prototype.media=function(t){var e=this;if(!t)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);var i=this.state;if("string"==typeof t){if(!this.master.playlists[t])throw new Error("Unknown playlist URI: "+t);t=this.master.playlists[t]}var r=!this.media_||t.uri!==this.media_.uri;if(this.master.playlists[t.uri].endList)return this.request&&(this.request.onreadystatechange=null,this.request.abort(),this.request=null),this.state="HAVE_METADATA",this.media_=t,void(r&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(r){if(this.state="SWITCHING_MEDIA",this.request){if(Jo(this.master.uri,t.uri)===this.request.url)return;this.request.onreadystatechange=null,this.request.abort(),this.request=null}this.media_&&this.trigger("mediachanging"),this.request=this.hls_.xhr({uri:Jo(this.master.uri,t.uri),withCredentials:this.withCredentials},function(r,n){if(e.request){if(r)return e.playlistRequestError(e.request,t.uri,i);e.haveMetadata(n,t.uri),"HAVE_MASTER"===i?e.trigger("loadedmetadata"):e.trigger("mediachange")}})}},e.prototype.pause=function(){this.stopRequest(),ve.clearTimeout(this.mediaUpdateTimeout),"HAVE_NOTHING"===this.state&&(this.started=!1),"SWITCHING_MEDIA"===this.state?this.media_?this.state="HAVE_METADATA":this.state="HAVE_MASTER":"HAVE_CURRENT_METADATA"===this.state&&(this.state="HAVE_METADATA")},e.prototype.load=function(t){var e=this;ve.clearTimeout(this.mediaUpdateTimeout);var i=this.media();if(t){var r=i?i.targetDuration/2*1e3:5e3;return void(this.mediaUpdateTimeout=ve.setTimeout(function(){return e.load()},r))}if(!this.started)return void this.start();i&&!i.endList?this.trigger("mediaupdatetimeout"):this.trigger("loadedplaylist")},e.prototype.start=function(){var t=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,i){if(t.request){if(t.request=null,e)return t.error={status:i.status,message:"HLS playlist request error at URL: "+t.srcUrl,responseText:i.responseText,code:2},"HAVE_NOTHING"===t.state&&(t.started=!1),t.trigger("error");var r=new ra;return r.push(i.responseText),(r.end(),t.state="HAVE_MASTER",r.manifest.uri=t.srcUrl,r.manifest.playlists)?(t.master=r.manifest,su(t.master),au(t.master),t.trigger("loadedplaylist"),void(t.request||t.media(r.manifest.playlists[0]))):(t.master={mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:ve.location.href,playlists:[{uri:t.srcUrl,id:0}]},t.master.playlists[t.srcUrl]=t.master.playlists[0],t.master.playlists[0].resolvedUri=t.srcUrl,t.master.playlists[0].attributes=t.master.playlists[0].attributes||{},t.haveMetadata(i,t.srcUrl),t.trigger("loadedmetadata"))}})},e}(Zo),lu=le.createTimeRange,cu=function(t,e){var i=0,r=e-t.mediaSequence,n=t.segments[r];if(n){if(void 0!==n.start)return{result:n.start,precise:!0};if(void 0!==n.end)return{result:n.end-n.duration,precise:!0}}for(;r--;){if(n=t.segments[r],void 0!==n.end)return{result:i+n.end,precise:!0};if(i+=n.duration,void 0!==n.start)return{result:i+n.start,precise:!0}}return{result:i,precise:!1}},hu=function(t,e){for(var i=0,r=void 0,n=e-t.mediaSequence;n<t.segments.length;n++){if(r=t.segments[n],void 0!==r.start)return{result:r.start-i,precise:!0};if(i+=r.duration,void 0!==r.end)return{result:r.end-i,precise:!0}}return{result:-1,precise:!1}},du=function(t,e,i){var r=void 0,n=void 0;return void 0===e&&(e=t.mediaSequence+t.segments.length),e<t.mediaSequence?0:(r=cu(t,e),r.precise?r.result:(n=hu(t,e),n.precise?n.result:r.result+i))},pu=function(t,e,i){if(!t)return 0;if("number"!=typeof i&&(i=0),void 0===e){if(t.totalDuration)return t.totalDuration;if(!t.endList)return ve.Infinity}return du(t,e,i)},fu=function(t,e,i){var r=0;if(e>i){var n=[i,e];e=n[0],i=n[1]}if(e<0){for(var s=e;s<Math.min(0,i);s++)r+=t.targetDuration;e=0}for(var a=e;a<i;a++)r+=t.segments[a].duration;return r},mu=function(t){if(!t.segments.length)return 0;for(var e=t.segments.length-1,i=t.segments[e].duration||t.targetDuration,r=i+2*t.targetDuration;e--&&!((i+=t.segments[e].duration)>=r););return Math.max(0,e)},gu=function(t,e,i){if(!t||!t.segments)return null;if(t.endList)return pu(t);if(null===e)return null;e=e||0;var r=i?mu(t):t.segments.length;return du(t,t.mediaSequence+r,e)},yu=function(t,e){var i=e||0,r=gu(t,e,!0);return null===r?lu():lu(i,r)},vu=function(t){return t-Math.floor(t)==0},_u=function(t,e){if(vu(e))return e+.1*t;for(var i=e.toString().split(".")[1].length,r=1;r<=i;r++){var n=Math.pow(10,r),s=e*n;if(vu(s)||r===i)return(s+t)/n}},bu=_u.bind(null,1),Tu=_u.bind(null,-1),Su=function(t,e,i,r){var n=void 0,s=void 0,a=t.segments.length,o=e-r;if(o<0){if(i>0)for(n=i-1;n>=0;n--)if(s=t.segments[n],(o+=Tu(s.duration))>0)return{mediaIndex:n,startTime:r-fu(t,i,n)};return{mediaIndex:0,startTime:e}}if(i<0){for(n=i;n<0;n++)if((o-=t.targetDuration)<0)return{mediaIndex:0,startTime:e};i=0}for(n=i;n<a;n++)if(s=t.segments[n],(o-=bu(s.duration))<0)return{mediaIndex:n,startTime:r+fu(t,i,n)};return{mediaIndex:a-1,startTime:e}},ku=function(t){return t.excludeUntil&&t.excludeUntil>Date.now()},Eu=function(t){return t.excludeUntil&&t.excludeUntil===1/0},Cu=function(t){var e=ku(t);return!t.disabled&&!e},wu=function(t){return t.disabled},Au=function(t){for(var e=0;e<t.segments.length;e++)if(t.segments[e].key)return!0;return!1},Lu=function(t){for(var e=0;e<t.segments.length;e++)if(t.segments[e].map)return!0;return!1},Ou=function(t,e){return e.attributes&&e.attributes[t]},Pu=function(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return Ou("BANDWIDTH",i)?(t*i.attributes.BANDWIDTH-8*r)/e:NaN},Iu=function(t,e){if(1===t.playlists.length)return!0;var i=e.attributes.BANDWIDTH||Number.MAX_VALUE;return 0===t.playlists.filter(function(t){return!!Cu(t)&&(t.attributes.BANDWIDTH||0)<i}).length},xu={duration:pu,seekable:yu,safeLiveIndex:mu,getMediaInfoForTime:Su,isEnabled:Cu,isDisabled:wu,isBlacklisted:ku,isIncompatible:Eu,playlistEnd:gu,isAes:Au,isFmp4:Lu,hasAttribute:Ou,estimateSegmentRequestTime:Pu,isLowestEnabledRendition:Iu},Du=le.xhr,Mu=le.mergeOptions,Ru=function(){return function t(e,i){e=Mu({timeout:45e3},e);var r=t.beforeRequest||le.Hls.xhr.beforeRequest;if(r&&"function"==typeof r){var n=r(e);n&&(e=n)}var s=Du(e,function(t,e){var r=s.response;!t&&r&&(s.responseTime=Date.now(),s.roundTripTime=s.responseTime-s.requestTime,s.bytesReceived=r.byteLength||r.length,s.bandwidth||(s.bandwidth=Math.floor(s.bytesReceived/s.roundTripTime*8*1e3))),e.headers&&(s.responseHeaders=e.headers),t&&"ETIMEDOUT"===t.code&&(s.timedout=!0),t||s.aborted||200===e.statusCode||206===e.statusCode||0===e.statusCode||(t=new Error("XHR Failed with a response of: "+(s&&(r||s.responseText)))),i(t,s)}),a=s.abort;return s.abort=function(){return s.aborted=!0,a.apply(s,arguments)},s.uri=e.uri,s.requestTime=Date.now(),s}},Uu=function(t,e){return t.start(e)+"-"+t.end(e)},Nu=function(t,e){var i=t.toString(16);return"00".substring(0,2-i.length)+i+(e%2?" ":"")},Bu=function(t){return t>=32&&t<126?String.fromCharCode(t):"."},ju=function(t){var e={};return Object.keys(t).forEach(function(i){var r=t[i];ArrayBuffer.isView(r)?e[i]={bytes:r.buffer,byteOffset:r.byteOffset,byteLength:r.byteLength}:e[i]=r}),e},Fu=function(t){var e=t.byterange||{length:1/0,offset:0};return[e.length,e.offset,t.resolvedUri].join(",")},Hu=function(t){for(var e=Array.prototype.slice.call(t),i="",r=void 0,n=void 0,s=0;s<e.length/16;s++)r=e.slice(16*s,16*s+16).map(Nu).join(""),n=e.slice(16*s,16*s+16).map(Bu).join(""),i+=r+" "+n+"\n";return i},Vu=function(t){var e=t.bytes;return Hu(e)},qu=function(t){var e="",i=void 0;for(i=0;i<t.length;i++)e+=Uu(t,i)+" ";return e},Wu=Object.freeze({createTransferableMessage:ju,initSegmentId:Fu,hexDump:Hu,tagDump:Vu,textRanges:qu}),Gu=function(t,e){var i=[],r=void 0;if(t&&t.length)for(r=0;r<t.length;r++)e(t.start(r),t.end(r))&&i.push([t.start(r),t.end(r)]);return le.createTimeRanges(i)},zu=function(t,e){return Gu(t,function(t,i){return t-1/30<=e&&i+1/30>=e})},Xu=function(t,e){return Gu(t,function(t){return t-1/30>=e})},Yu=function(t){if(t.length<2)return le.createTimeRanges();for(var e=[],i=1;i<t.length;i++){var r=t.end(i-1),n=t.start(i);e.push([r,n])}return le.createTimeRanges(e)},$u=function(t){var e=[];if(!t||!t.length)return"";for(var i=0;i<t.length;i++)e.push(t.start(i)+" => "+t.end(i));return e.join(", ")},Ku=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return((t.length?t.end(t.length-1):0)-e)/i},Ju=function(t){for(var e=[],i=0;i<t.length;i++)e.push({start:t.start(i),end:t.end(i)});return e},Qu=function(t,e,i){var r=e.player_;if(i.captions&&i.captions.length){t.inbandTextTracks_||(t.inbandTextTracks_={});for(var n in i.captionStreams)if(!t.inbandTextTracks_[n]){r.tech_.trigger({type:"usage",name:"hls-608"});var s=r.textTracks().getTrackById(n);t.inbandTextTracks_[n]=s||r.addRemoteTextTrack({kind:"captions",id:n,label:n},!1).track}}i.metadata&&i.metadata.length&&!t.metadataTrack_&&(t.metadataTrack_=r.addRemoteTextTrack({kind:"metadata",label:"Timed Metadata"},!1).track,t.metadataTrack_.inBandMetadataTrackDispatchType=i.metadata.dispatchType)},Zu=function(t,e,i){var r=void 0,n=void 0;if(i&&i.cues)for(r=i.cues.length;r--;)n=i.cues[r],n.startTime<=e&&n.endTime>=t&&i.removeCue(n)},tl=function(t){Object.defineProperties(t.frame,{id:{get:function(){return le.log.warn("cue.frame.id is deprecated. Use cue.value.key instead."),t.value.key}},value:{get:function(){return le.log.warn("cue.frame.value is deprecated. Use cue.value.data instead."),t.value.data}},privateData:{get:function(){return le.log.warn("cue.frame.privateData is deprecated. Use cue.value.data instead."),t.value.data}}})},el=function(t){return isNaN(t)||Math.abs(t)===1/0?Number.MAX_VALUE:t},il=function(t,e,i){var r=ve.WebKitDataCue||ve.VTTCue;if(e&&e.forEach(function(t){var e=t.stream;this.inbandTextTracks_[e].addCue(new r(t.startTime+this.timestampOffset,t.endTime+this.timestampOffset,t.text))},t),i){var n=el(t.mediaSource_.duration);if(i.forEach(function(t){var e=t.cueTime+this.timestampOffset;t.frames.forEach(function(t){var i=new r(e,e,t.value||t.url||t.data||"");i.frame=t,i.value=t,tl(i),this.metadataTrack_.addCue(i)},this)},t),t.metadataTrack_&&t.metadataTrack_.cues&&t.metadataTrack_.cues.length){for(var s=t.metadataTrack_.cues,a=[],o=0;o<s.length;o++)s[o]&&a.push(s[o]);var u=a.reduce(function(t,e){var i=t[e.startTime]||[];return i.push(e),t[e.startTime]=i,t},{}),l=Object.keys(u).sort(function(t,e){return Number(t)-Number(e)});l.forEach(function(t,e){var i=u[t],r=Number(l[e+1])||n;i.forEach(function(t){t.endTime=r})})}}},rl="undefined"!=typeof window?window:{},nl="undefined"==typeof Symbol?"__target":Symbol(),sl="application/javascript",al=rl.BlobBuilder||rl.WebKitBlobBuilder||rl.MozBlobBuilder||rl.MSBlobBuilder,ol=rl.URL||rl.webkitURL||ol&&ol.msURL,ul=rl.Worker;if(ul){var ll,cl=he("self.onmessage = function () {}"),hl=new Uint8Array(1);try{ll=new ul(cl),ll.postMessage(hl,[hl.buffer])}catch(t){ul=null}finally{ol.revokeObjectURL(cl),ll&&ll.terminate()}}var dl=new ce("./transmuxer-worker.worker.js",function(t,e){var i=this;!function(){var e,r=void 0!==t?t:"undefined"!=typeof global?global:void 0!==i?i:{};e=void 0!==t?t:void 0!==r?r:void 0!==i?i:{};var n,s,a,o,u,l,c,h,d,p,f,m,g,y,v,_,b,T,S,k,E,C,w,A,L,O,P,I,x,D,M,R,U,N,B,j,F=e,H=Math.pow(2,32)-1;!function(){var t;if(w={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(t in w)w.hasOwnProperty(t)&&(w[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);A=new Uint8Array(["i".charCodeAt(0),"s".charCodeAt(0),"o".charCodeAt(0),"m".charCodeAt(0)]),O=new Uint8Array(["a".charCodeAt(0),"v".charCodeAt(0),"c".charCodeAt(0),"1".charCodeAt(0)]),L=new Uint8Array([0,0,0,1]),P=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),I=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),x={video:P,audio:I},R=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),M=new Uint8Array([0,0,0,0,0,0,0,0]),U=new Uint8Array([0,0,0,0,0,0,0,0]),N=U,B=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),j=U,D=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),n=function(t){var e,i,r,n=[],s=0;for(e=1;e<arguments.length;e++)n.push(arguments[e]);for(e=n.length;e--;)s+=n[e].byteLength;for(i=new Uint8Array(s+8),r=new DataView(i.buffer,i.byteOffset,i.byteLength),r.setUint32(0,i.byteLength),i.set(t,4),e=0,s=8;e<n.length;e++)i.set(n[e],s),s+=n[e].byteLength;return i},s=function(){return n(w.dinf,n(w.dref,R))},a=function(t){return n(w.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,t.audioobjecttype<<3|t.samplingfrequencyindex>>>1,t.samplingfrequencyindex<<7|t.channelcount<<3,6,1,2]))},o=function(){return n(w.ftyp,A,L,A,O)},_=function(t){return n(w.hdlr,x[t])},u=function(t){return n(w.mdat,t)},v=function(t){var e=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,t.duration>>>24&255,t.duration>>>16&255,t.duration>>>8&255,255&t.duration,85,196,0,0]);return t.samplerate&&(e[12]=t.samplerate>>>24&255,e[13]=t.samplerate>>>16&255,e[14]=t.samplerate>>>8&255,e[15]=255&t.samplerate),n(w.mdhd,e)},y=function(t){return n(w.mdia,v(t),_(t.type),c(t))},l=function(t){return n(w.mfhd,new Uint8Array([0,0,0,0,(4278190080&t)>>24,(16711680&t)>>16,(65280&t)>>8,255&t]))},c=function(t){return n(w.minf,"video"===t.type?n(w.vmhd,D):n(w.smhd,M),s(),T(t))},h=function(t,e){for(var i=[],r=e.length;r--;)i[r]=k(e[r]);return n.apply(null,[w.moof,l(t)].concat(i))},d=function(t){for(var e=t.length,i=[];e--;)i[e]=m(t[e]);return n.apply(null,[w.moov,f(4294967295)].concat(i).concat(p(t)))},p=function(t){for(var e=t.length,i=[];e--;)i[e]=E(t[e]);return n.apply(null,[w.mvex].concat(i))},f=function(t){var e=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&t)>>24,(16711680&t)>>16,(65280&t)>>8,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return n(w.mvhd,e)},b=function(t){var e,i,r=t.samples||[],s=new Uint8Array(4+r.length);for(i=0;i<r.length;i++)e=r[i].flags,s[i+4]=e.dependsOn<<4|e.isDependedOn<<2|e.hasRedundancy;return n(w.sdtp,s)},T=function(t){return n(w.stbl,S(t),n(w.stts,j),n(w.stsc,N),n(w.stsz,B),n(w.stco,U))},function(){var t,e;S=function(i){return n(w.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===i.type?t(i):e(i))},t=function(t){var e,i=t.sps||[],r=t.pps||[],s=[],a=[];for(e=0;e<i.length;e++)s.push((65280&i[e].byteLength)>>>8),s.push(255&i[e].byteLength),s=s.concat(Array.prototype.slice.call(i[e]));for(e=0;e<r.length;e++)a.push((65280&r[e].byteLength)>>>8),a.push(255&r[e].byteLength),a=a.concat(Array.prototype.slice.call(r[e]));return n(w.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&t.width)>>8,255&t.width,(65280&t.height)>>8,255&t.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),n(w.avcC,new Uint8Array([1,t.profileIdc,t.profileCompatibility,t.levelIdc,255].concat([i.length]).concat(s).concat([r.length]).concat(a))),n(w.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])))},e=function(t){return n(w.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&t.channelcount)>>8,255&t.channelcount,(65280&t.samplesize)>>8,255&t.samplesize,0,0,0,0,(65280&t.samplerate)>>8,255&t.samplerate,0,0]),a(t))}}(),g=function(t){
var e=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&t.id)>>24,(16711680&t.id)>>16,(65280&t.id)>>8,255&t.id,0,0,0,0,(4278190080&t.duration)>>24,(16711680&t.duration)>>16,(65280&t.duration)>>8,255&t.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&t.width)>>8,255&t.width,0,0,(65280&t.height)>>8,255&t.height,0,0]);return n(w.tkhd,e)},k=function(t){var e,i,r,s,a,o,u;return e=n(w.tfhd,new Uint8Array([0,0,0,58,(4278190080&t.id)>>24,(16711680&t.id)>>16,(65280&t.id)>>8,255&t.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),o=Math.floor(t.baseMediaDecodeTime/(H+1)),u=Math.floor(t.baseMediaDecodeTime%(H+1)),i=n(w.tfdt,new Uint8Array([1,0,0,0,o>>>24&255,o>>>16&255,o>>>8&255,255&o,u>>>24&255,u>>>16&255,u>>>8&255,255&u])),a=92,"audio"===t.type?(r=C(t,a),n(w.traf,e,i,r)):(s=b(t),r=C(t,s.length+a),n(w.traf,e,i,r,s))},m=function(t){return t.duration=t.duration||4294967295,n(w.trak,g(t),y(t))},E=function(t){var e=new Uint8Array([0,0,0,0,(4278190080&t.id)>>24,(16711680&t.id)>>16,(65280&t.id)>>8,255&t.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==t.type&&(e[e.length-1]=0),n(w.trex,e)},function(){var t,e,i;i=function(t,e){var i=0,r=0,n=0,s=0;return t.length&&(void 0!==t[0].duration&&(i=1),void 0!==t[0].size&&(r=2),void 0!==t[0].flags&&(n=4),void 0!==t[0].compositionTimeOffset&&(s=8)),[0,0,i|r|n|s,1,(4278190080&t.length)>>>24,(16711680&t.length)>>>16,(65280&t.length)>>>8,255&t.length,(4278190080&e)>>>24,(16711680&e)>>>16,(65280&e)>>>8,255&e]},e=function(t,e){var r,s,a,o;for(s=t.samples||[],e+=20+16*s.length,r=i(s,e),o=0;o<s.length;o++)a=s[o],r=r.concat([(4278190080&a.duration)>>>24,(16711680&a.duration)>>>16,(65280&a.duration)>>>8,255&a.duration,(4278190080&a.size)>>>24,(16711680&a.size)>>>16,(65280&a.size)>>>8,255&a.size,a.flags.isLeading<<2|a.flags.dependsOn,a.flags.isDependedOn<<6|a.flags.hasRedundancy<<4|a.flags.paddingValue<<1|a.flags.isNonSyncSample,61440&a.flags.degradationPriority,15&a.flags.degradationPriority,(4278190080&a.compositionTimeOffset)>>>24,(16711680&a.compositionTimeOffset)>>>16,(65280&a.compositionTimeOffset)>>>8,255&a.compositionTimeOffset]);return n(w.trun,new Uint8Array(r))},t=function(t,e){var r,s,a,o;for(s=t.samples||[],e+=20+8*s.length,r=i(s,e),o=0;o<s.length;o++)a=s[o],r=r.concat([(4278190080&a.duration)>>>24,(16711680&a.duration)>>>16,(65280&a.duration)>>>8,255&a.duration,(4278190080&a.size)>>>24,(16711680&a.size)>>>16,(65280&a.size)>>>8,255&a.size]);return n(w.trun,new Uint8Array(r))},C=function(i,r){return"audio"===i.type?t(i,r):e(i,r)}}();var V={ftyp:o,mdat:u,moof:h,moov:d,initSegment:function(t){var e,i=o(),r=d(t);return e=new Uint8Array(i.byteLength+r.byteLength),e.set(i),e.set(r,i.byteLength),e}},q=function(){this.init=function(){var t={};this.on=function(e,i){t[e]||(t[e]=[]),t[e]=t[e].concat(i)},this.off=function(e,i){var r;return!!t[e]&&(r=t[e].indexOf(i),t[e]=t[e].slice(),t[e].splice(r,1),r>-1)},this.trigger=function(e){var i,r,n,s;if(i=t[e])if(2===arguments.length)for(n=i.length,r=0;r<n;++r)i[r].call(this,arguments[1]);else{for(s=[],r=arguments.length,r=1;r<arguments.length;++r)s.push(arguments[r]);for(n=i.length,r=0;r<n;++r)i[r].apply(this,s)}},this.dispose=function(){t={}}}};q.prototype.pipe=function(t){return this.on("data",function(e){t.push(e)}),this.on("done",function(e){t.flush(e)}),t},q.prototype.push=function(t){this.trigger("data",t)},q.prototype.flush=function(t){this.trigger("done",t)};var W=q,G=function(t){for(var e=0,i={payloadType:-1,payloadSize:0},r=0,n=0;e<t.byteLength&&128!==t[e];){for(;255===t[e];)r+=255,e++;for(r+=t[e++];255===t[e];)n+=255,e++;if(n+=t[e++],!i.payload&&4===r){i.payloadType=r,i.payloadSize=n,i.payload=t.subarray(e,e+n);break}e+=n,r=0,n=0}return i},z=function(t){return 181!==t.payload[0]?null:49!=(t.payload[1]<<8|t.payload[2])?null:"GA94"!==String.fromCharCode(t.payload[3],t.payload[4],t.payload[5],t.payload[6])?null:3!==t.payload[7]?null:t.payload.subarray(8,t.payload.length-1)},X=function(t,e){var i,r,n,s,a=[];if(!(64&e[0]))return a;for(r=31&e[0],i=0;i<r;i++)n=3*i,s={type:3&e[n+2],pts:t},4&e[n+2]&&(s.ccData=e[n+3]<<8|e[n+4],a.push(s));return a},Y=function t(){t.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new Z(0,0),new Z(0,1),new Z(1,0),new Z(1,1)],this.reset(),this.ccStreams_.forEach(function(t){t.on("data",this.trigger.bind(this,"data")),t.on("done",this.trigger.bind(this,"done"))},this)};Y.prototype=new W,Y.prototype.push=function(t){var e,i;if("sei_rbsp"===t.nalUnitType&&(e=G(t.escapedRBSP),4===e.payloadType&&(i=z(e)))){if(t.dts<this.latestDts_)return void(this.ignoreNextEqualDts_=!0);if(t.dts===this.latestDts_&&this.ignoreNextEqualDts_)return void(--this.numSameDts_||(this.ignoreNextEqualDts_=!1));this.captionPackets_=this.captionPackets_.concat(X(t.pts,i)),this.latestDts_!==t.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=t.dts}},Y.prototype.flush=function(){if(!this.captionPackets_.length)return void this.ccStreams_.forEach(function(t){t.flush()},this);this.captionPackets_.forEach(function(t,e){t.presortIndex=e}),this.captionPackets_.sort(function(t,e){return t.pts===e.pts?t.presortIndex-e.presortIndex:t.pts-e.pts}),this.captionPackets_.forEach(function(t){t.type<2&&this.dispatchCea608Packet(t)},this),this.captionPackets_.length=0,this.ccStreams_.forEach(function(t){t.flush()},this)},Y.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(t){t.reset()})},Y.prototype.dispatchCea608Packet=function(t){this.setsChannel1Active(t)?this.activeCea608Channel_[t.type]=0:this.setsChannel2Active(t)&&(this.activeCea608Channel_[t.type]=1),null!==this.activeCea608Channel_[t.type]&&this.ccStreams_[(t.type<<1)+this.activeCea608Channel_[t.type]].push(t)},Y.prototype.setsChannel1Active=function(t){return 4096==(30720&t.ccData)},Y.prototype.setsChannel2Active=function(t){return 6144==(30720&t.ccData)};var $={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},K=function(t){return null===t?"":(t=$[t]||t,String.fromCharCode(t))},J=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],Q=function(){for(var t=[],e=15;e--;)t.push("");return t},Z=function t(e,i){t.prototype.init.call(this),this.field_=e||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(t){var e,i,r,n,s;if((e=32639&t.ccData)===this.lastControlCode_)return void(this.lastControlCode_=null);if(4096==(61440&e)?this.lastControlCode_=e:e!==this.PADDING_&&(this.lastControlCode_=null),r=e>>>8,n=255&e,e!==this.PADDING_)if(e===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(e===this.END_OF_CAPTION_)this.clearFormatting(t.pts),this.flushDisplayed(t.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=t.pts;else if(e===this.ROLL_UP_2_ROWS_)this.topRow_=13,this.mode_="rollUp";else if(e===this.ROLL_UP_3_ROWS_)this.topRow_=12,this.mode_="rollUp";else if(e===this.ROLL_UP_4_ROWS_)this.topRow_=11,this.mode_="rollUp";else if(e===this.CARRIAGE_RETURN_)this.clearFormatting(t.pts),this.flushDisplayed(t.pts),this.shiftRowsUp_(),this.startPts_=t.pts;else if(e===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[14]=this.nonDisplayed_[14].slice(0,-1):this.displayed_[14]=this.displayed_[14].slice(0,-1);else if(e===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(t.pts),this.displayed_=Q();else if(e===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=Q();else if(e===this.RESUME_DIRECT_CAPTIONING_)this.mode_="paintOn";else if(this.isSpecialCharacter(r,n))r=(3&r)<<8,s=K(r|n),this[this.mode_](t.pts,s),this.column_++;else if(this.isExtCharacter(r,n))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[14]=this.displayed_[14].slice(0,-1),r=(3&r)<<8,s=K(r|n),this[this.mode_](t.pts,s),this.column_++;else if(this.isMidRowCode(r,n))this.clearFormatting(t.pts),this[this.mode_](t.pts," "),this.column_++,14==(14&n)&&this.addFormatting(t.pts,["i"]),1==(1&n)&&this.addFormatting(t.pts,["u"]);else if(this.isOffsetControlCode(r,n))this.column_+=3&n;else if(this.isPAC(r,n)){var a=J.indexOf(7968&e);a!==this.row_&&(this.clearFormatting(t.pts),this.row_=a),1&n&&-1===this.formatting_.indexOf("u")&&this.addFormatting(t.pts,["u"]),16==(16&e)&&(this.column_=4*((14&e)>>1)),this.isColorPAC(n)&&14==(14&n)&&this.addFormatting(t.pts,["i"])}else this.isNormalChar(r)&&(0===n&&(n=null),s=K(r),s+=K(n),this[this.mode_](t.pts,s),this.column_+=s.length)}};Z.prototype=new W,Z.prototype.flushDisplayed=function(t){var e=this.displayed_.map(function(t){return t.trim()}).join("\n").replace(/^\n+|\n+$/g,"");e.length&&this.trigger("data",{startPts:this.startPts_,endPts:t,text:e,stream:this.name_})},Z.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=Q(),this.nonDisplayed_=Q(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.formatting_=[]},Z.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},Z.prototype.isSpecialCharacter=function(t,e){return t===this.EXT_&&e>=48&&e<=63},Z.prototype.isExtCharacter=function(t,e){return(t===this.EXT_+1||t===this.EXT_+2)&&e>=32&&e<=63},Z.prototype.isMidRowCode=function(t,e){return t===this.EXT_&&e>=32&&e<=47},Z.prototype.isOffsetControlCode=function(t,e){return t===this.OFFSET_&&e>=33&&e<=35},Z.prototype.isPAC=function(t,e){return t>=this.BASE_&&t<this.BASE_+8&&e>=64&&e<=127},Z.prototype.isColorPAC=function(t){return t>=64&&t<=79||t>=96&&t<=127},Z.prototype.isNormalChar=function(t){return t>=32&&t<=127},Z.prototype.addFormatting=function(t,e){this.formatting_=this.formatting_.concat(e);var i=e.reduce(function(t,e){return t+"<"+e+">"},"");this[this.mode_](t,i)},Z.prototype.clearFormatting=function(t){if(this.formatting_.length){var e=this.formatting_.reverse().reduce(function(t,e){return t+"</"+e+">"},"");this.formatting_=[],this[this.mode_](t,e)}},Z.prototype.popOn=function(t,e){var i=this.nonDisplayed_[this.row_];i+=e,this.nonDisplayed_[this.row_]=i},Z.prototype.rollUp=function(t,e){var i=this.displayed_[14];i+=e,this.displayed_[14]=i},Z.prototype.shiftRowsUp_=function(){var t;for(t=0;t<this.topRow_;t++)this.displayed_[t]="";for(t=this.topRow_;t<14;t++)this.displayed_[t]=this.displayed_[t+1];this.displayed_[14]=""},Z.prototype.paintOn=function(){};var tt={CaptionStream:Y,Cea608Stream:Z},et={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21},it=function(t,e){var i=1;for(t>e&&(i=-1);Math.abs(e-t)>4294967296;)t+=8589934592*i;return t},rt=function t(e){var i,r;t.prototype.init.call(this),this.type_=e,this.push=function(t){t.type===this.type_&&(void 0===r&&(r=t.dts),t.dts=it(t.dts,r),t.pts=it(t.pts,r),i=t.dts,this.trigger("data",t))},this.flush=function(){r=i,this.trigger("done")},this.discontinuity=function(){r=void 0,i=void 0}};rt.prototype=new W;var nt,st={TimestampRolloverStream:rt,handleRollover:it},at=function(t,e,i){var r,n="";for(r=e;r<i;r++)n+="%"+("00"+t[r].toString(16)).slice(-2);return n},ot=function(t,e,i){return decodeURIComponent(at(t,e,i))},ut=function(t,e,i){return unescape(at(t,e,i))},lt=function(t){return t[0]<<21|t[1]<<14|t[2]<<7|t[3]},ct={TXXX:function(t){var e;if(3===t.data[0]){for(e=1;e<t.data.length;e++)if(0===t.data[e]){t.description=ot(t.data,1,e),t.value=ot(t.data,e+1,t.data.length).replace(/\0*$/,"");break}t.data=t.value}},WXXX:function(t){var e;if(3===t.data[0])for(e=1;e<t.data.length;e++)if(0===t.data[e]){t.description=ot(t.data,1,e),t.url=ot(t.data,e+1,t.data.length);break}},PRIV:function(t){var e;for(e=0;e<t.data.length;e++)if(0===t.data[e]){t.owner=ut(t.data,0,e);break}t.privateData=t.data.subarray(e+1),t.data=t.privateData}};nt=function(t){var e,i={debug:!(!t||!t.debug),descriptor:t&&t.descriptor},r=0,n=[],s=0;if(nt.prototype.init.call(this),this.dispatchType=et.METADATA_STREAM_TYPE.toString(16),i.descriptor)for(e=0;e<i.descriptor.length;e++)this.dispatchType+=("00"+i.descriptor[e].toString(16)).slice(-2);this.push=function(t){var e,a,o,u,l,c;if("timed-metadata"===t.type){if(t.dataAlignmentIndicator&&(s=0,n.length=0),0===n.length&&(t.data.length<10||t.data[0]!=="I".charCodeAt(0)||t.data[1]!=="D".charCodeAt(0)||t.data[2]!=="3".charCodeAt(0)))return void i.debug;if(n.push(t),s+=t.data.byteLength,1===n.length&&(r=lt(t.data.subarray(6,10)),r+=10),!(s<r)){for(e={data:new Uint8Array(r),frames:[],pts:n[0].pts,dts:n[0].dts},l=0;l<r;)e.data.set(n[0].data.subarray(0,r-l),l),l+=n[0].data.byteLength,s-=n[0].data.byteLength,n.shift();a=10,64&e.data[5]&&(a+=4,a+=lt(e.data.subarray(10,14)),r-=lt(e.data.subarray(16,20)));do{if((o=lt(e.data.subarray(a+4,a+8)))<1)return;if(c=String.fromCharCode(e.data[a],e.data[a+1],e.data[a+2],e.data[a+3]),u={id:c,data:e.data.subarray(a+10,a+o+10)},u.key=u.id,ct[u.id]&&(ct[u.id](u),"com.apple.streaming.transportStreamTimestamp"===u.owner)){var h=u.data,d=(1&h[3])<<30|h[4]<<22|h[5]<<14|h[6]<<6|h[7]>>>2;d*=4,d+=3&h[7],u.timeStamp=d,void 0===e.pts&&void 0===e.dts&&(e.pts=u.timeStamp,e.dts=u.timeStamp),this.trigger("timestamp",u)}e.frames.push(u),a+=10,a+=o}while(a<r);this.trigger("data",e)}}}},nt.prototype=new W;var ht,dt,pt,ft=nt,mt=st.TimestampRolloverStream;ht=function(){var t=new Uint8Array(188),e=0;ht.prototype.init.call(this),this.push=function(i){var r,n=0,s=188;for(e?(r=new Uint8Array(i.byteLength+e),r.set(t.subarray(0,e)),r.set(i,e),e=0):r=i;s<r.byteLength;)71!==r[n]||71!==r[s]?(n++,s++):(this.trigger("data",r.subarray(n,s)),n+=188,s+=188);n<r.byteLength&&(t.set(r.subarray(n),0),e=r.byteLength-n)},this.flush=function(){188===e&&71===t[0]&&(this.trigger("data",t),e=0),this.trigger("done")}},ht.prototype=new W,dt=function(){var t,e,i,r;dt.prototype.init.call(this),r=this,this.packetsWaitingForPmt=[],this.programMapTable=void 0,t=function(t,r){var n=0;r.payloadUnitStartIndicator&&(n+=t[n]+1),"pat"===r.type?e(t.subarray(n),r):i(t.subarray(n),r)},e=function(t,e){e.section_number=t[7],e.last_section_number=t[8],r.pmtPid=(31&t[10])<<8|t[11],e.pmtPid=r.pmtPid},i=function(t,e){var i,n,s,a;if(1&t[5]){for(r.programMapTable={video:null,audio:null,"timed-metadata":{}},i=(15&t[1])<<8|t[2],n=3+i-4,s=(15&t[10])<<8|t[11],a=12+s;a<n;){var o=t[a],u=(31&t[a+1])<<8|t[a+2];o===et.H264_STREAM_TYPE&&null===r.programMapTable.video?r.programMapTable.video=u:o===et.ADTS_STREAM_TYPE&&null===r.programMapTable.audio?r.programMapTable.audio=u:o===et.METADATA_STREAM_TYPE&&(r.programMapTable["timed-metadata"][u]=o),a+=5+((15&t[a+3])<<8|t[a+4])}e.programMapTable=r.programMapTable}},this.push=function(e){var i={},r=4;if(i.payloadUnitStartIndicator=!!(64&e[1]),i.pid=31&e[1],i.pid<<=8,i.pid|=e[2],(48&e[3])>>>4>1&&(r+=e[r]+1),0===i.pid)i.type="pat",t(e.subarray(r),i),this.trigger("data",i);else if(i.pid===this.pmtPid)for(i.type="pmt",t(e.subarray(r),i),this.trigger("data",i);this.packetsWaitingForPmt.length;)this.processPes_.apply(this,this.packetsWaitingForPmt.shift());else void 0===this.programMapTable?this.packetsWaitingForPmt.push([e,r,i]):this.processPes_(e,r,i)},this.processPes_=function(t,e,i){i.pid===this.programMapTable.video?i.streamType=et.H264_STREAM_TYPE:i.pid===this.programMapTable.audio?i.streamType=et.ADTS_STREAM_TYPE:i.streamType=this.programMapTable["timed-metadata"][i.pid],i.type="pes",i.data=t.subarray(e),this.trigger("data",i)}},dt.prototype=new W,dt.STREAM_TYPES={h264:27,adts:15},pt=function(){var t=this,e={data:[],size:0},i={data:[],size:0},r={data:[],size:0},n=function(t,e){var i;e.packetLength=6+(t[4]<<8|t[5]),e.dataAlignmentIndicator=0!=(4&t[6]),i=t[7],192&i&&(e.pts=(14&t[9])<<27|(255&t[10])<<20|(254&t[11])<<12|(255&t[12])<<5|(254&t[13])>>>3,e.pts*=4,e.pts+=(6&t[13])>>>1,e.dts=e.pts,64&i&&(e.dts=(14&t[14])<<27|(255&t[15])<<20|(254&t[16])<<12|(255&t[17])<<5|(254&t[18])>>>3,e.dts*=4,e.dts+=(6&t[18])>>>1)),e.data=t.subarray(9+t[8])},s=function(e,i,r){var s,a=new Uint8Array(e.size),o={type:i},u=0,l=0,c=!1;if(e.data.length&&!(e.size<9)){for(o.trackId=e.data[0].pid,u=0;u<e.data.length;u++)s=e.data[u],a.set(s.data,l),l+=s.data.byteLength;n(a,o),c="video"===i||o.packetLength<=e.size,(r||c)&&(e.size=0,e.data.length=0),c&&t.trigger("data",o)}};pt.prototype.init.call(this),this.push=function(n){({pat:function(){},pes:function(){var t,a;switch(n.streamType){case et.H264_STREAM_TYPE:case et.H264_STREAM_TYPE:t=e,a="video";break;case et.ADTS_STREAM_TYPE:t=i,a="audio";break;case et.METADATA_STREAM_TYPE:t=r,a="timed-metadata";break;default:return}n.payloadUnitStartIndicator&&s(t,a,!0),t.data.push(n),t.size+=n.data.byteLength},pmt:function(){var e={type:"metadata",tracks:[]},i=n.programMapTable;null!==i.video&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+i.video,codec:"avc",type:"video"}),null!==i.audio&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+i.audio,codec:"adts",type:"audio"}),t.trigger("data",e)}})[n.type]()},this.flush=function(){s(e,"video"),s(i,"audio"),s(r,"timed-metadata"),this.trigger("done")}},pt.prototype=new W;var gt={PAT_PID:0,MP2T_PACKET_LENGTH:188,TransportPacketStream:ht,TransportParseStream:dt,ElementaryStream:pt,TimestampRolloverStream:mt,CaptionStream:tt.CaptionStream,Cea608Stream:tt.Cea608Stream,MetadataStream:ft};for(var yt in et)et.hasOwnProperty(yt)&&(gt[yt]=et[yt]);var vt,_t=gt,bt=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];vt=function(){var t;vt.prototype.init.call(this),this.push=function(e){var i,r,n,s,a,o,u=0,l=0;if("audio"===e.type)for(t?(s=t,t=new Uint8Array(s.byteLength+e.data.byteLength),t.set(s),t.set(e.data,s.byteLength)):t=e.data;u+5<t.length;)if(255===t[u]&&240==(246&t[u+1])){if(r=2*(1&~t[u+1]),i=(3&t[u+3])<<11|t[u+4]<<3|(224&t[u+5])>>5,a=1024*(1+(3&t[u+6])),o=9e4*a/bt[(60&t[u+2])>>>2],n=u+i,t.byteLength<n)return;if(this.trigger("data",{pts:e.pts+l*o,dts:e.dts+l*o,sampleCount:a,audioobjecttype:1+(t[u+2]>>>6&3),channelcount:(1&t[u+2])<<2|(192&t[u+3])>>>6,samplerate:bt[(60&t[u+2])>>>2],samplingfrequencyindex:(60&t[u+2])>>>2,samplesize:16,data:t.subarray(u+7+r,n)}),t.byteLength===n)return void(t=void 0);l++,t=t.subarray(n)}else u++},this.flush=function(){this.trigger("done")}},vt.prototype=new W;var Tt,St=vt;Tt=function(t){var e=t.byteLength,i=0,r=0;this.length=function(){return 8*e},this.bitsAvailable=function(){return 8*e+r},this.loadWord=function(){var n=t.byteLength-e,s=new Uint8Array(4),a=Math.min(4,e);if(0===a)throw new Error("no bytes available");s.set(t.subarray(n,n+a)),i=new DataView(s.buffer).getUint32(0),r=8*a,e-=a},this.skipBits=function(t){var n;r>t?(i<<=t,r-=t):(t-=r,n=Math.floor(t/8),t-=8*n,e-=n,this.loadWord(),i<<=t,r-=t)},this.readBits=function(t){var n=Math.min(r,t),s=i>>>32-n;return r-=n,r>0?i<<=n:e>0&&this.loadWord(),n=t-n,n>0?s<<n|this.readBits(n):s},this.skipLeadingZeros=function(){var t;for(t=0;t<r;++t)if(0!=(i&2147483648>>>t))return i<<=t,r-=t,t;return this.loadWord(),t+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var t=this.skipLeadingZeros();return this.readBits(t+1)-1},this.readExpGolomb=function(){var t=this.readUnsignedExpGolomb();return 1&t?1+t>>>1:-1*(t>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()};var kt,Et,Ct,wt=Tt;Et=function(){var t,e,i=0;Et.prototype.init.call(this),this.push=function(r){var n;for(e?(n=new Uint8Array(e.byteLength+r.data.byteLength),n.set(e),n.set(r.data,e.byteLength),e=n):e=r.data;i<e.byteLength-3;i++)if(1===e[i+2]){t=i+5;break}for(;t<e.byteLength;)switch(e[t]){case 0:if(0!==e[t-1]){t+=2;break}if(0!==e[t-2]){t++;break}i+3!==t-2&&this.trigger("data",e.subarray(i+3,t-2));do{t++}while(1!==e[t]&&t<e.length);i=t-2,t+=3;break;case 1:if(0!==e[t-1]||0!==e[t-2]){t+=3;break}this.trigger("data",e.subarray(i+3,t-2)),i=t-2,t+=3;break;default:t+=3}e=e.subarray(i),t-=i,i=0},this.flush=function(){e&&e.byteLength>3&&this.trigger("data",e.subarray(i+3)),e=null,i=0,this.trigger("done")}},Et.prototype=new W,Ct={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},kt=function(){var t,e,i,r,n,s,a,o=new Et;kt.prototype.init.call(this),t=this,this.push=function(t){"video"===t.type&&(e=t.trackId,i=t.pts,r=t.dts,o.push(t))},o.on("data",function(a){var o={trackId:e,pts:i,dts:r,data:a};switch(31&a[0]){case 5:o.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:o.nalUnitType="sei_rbsp",o.escapedRBSP=n(a.subarray(1));break;case 7:o.nalUnitType="seq_parameter_set_rbsp",o.escapedRBSP=n(a.subarray(1)),o.config=s(o.escapedRBSP);break;case 8:o.nalUnitType="pic_parameter_set_rbsp";break;case 9:o.nalUnitType="access_unit_delimiter_rbsp"}t.trigger("data",o)}),o.on("done",function(){t.trigger("done")}),this.flush=function(){o.flush()},a=function(t,e){var i,r,n=8,s=8;for(i=0;i<t;i++)0!==s&&(r=e.readExpGolomb(),s=(n+r+256)%256),n=0===s?n:s},n=function(t){for(var e,i,r=t.byteLength,n=[],s=1;s<r-2;)0===t[s]&&0===t[s+1]&&3===t[s+2]?(n.push(s+2),s+=2):s++;if(0===n.length)return t;e=r-n.length,i=new Uint8Array(e);var a=0;for(s=0;s<e;a++,s++)a===n[0]&&(a++,n.shift()),i[s]=t[a];return i},s=function(t){var e,i,r,n,s,o,u,l,c,h,d,p,f,m=0,g=0,y=0,v=0,_=1;if(e=new wt(t),i=e.readUnsignedByte(),n=e.readUnsignedByte(),r=e.readUnsignedByte(),e.skipUnsignedExpGolomb(),Ct[i]&&(s=e.readUnsignedExpGolomb(),3===s&&e.skipBits(1),e.skipUnsignedExpGolomb(),e.skipUnsignedExpGolomb(),e.skipBits(1),e.readBoolean()))for(d=3!==s?8:12,f=0;f<d;f++)e.readBoolean()&&(f<6?a(16,e):a(64,e));if(e.skipUnsignedExpGolomb(),0===(o=e.readUnsignedExpGolomb()))e.readUnsignedExpGolomb();else if(1===o)for(e.skipBits(1),e.skipExpGolomb(),e.skipExpGolomb(),u=e.readUnsignedExpGolomb(),f=0;f<u;f++)e.skipExpGolomb();if(e.skipUnsignedExpGolomb(),e.skipBits(1),l=e.readUnsignedExpGolomb(),c=e.readUnsignedExpGolomb(),h=e.readBits(1),0===h&&e.skipBits(1),e.skipBits(1),e.readBoolean()&&(m=e.readUnsignedExpGolomb(),g=e.readUnsignedExpGolomb(),y=e.readUnsignedExpGolomb(),v=e.readUnsignedExpGolomb()),e.readBoolean()&&e.readBoolean()){switch(e.readUnsignedByte()){case 1:p=[1,1];break;case 2:p=[12,11];break;case 3:p=[10,11];break;case 4:p=[16,11];break;case 5:p=[40,33];break;case 6:p=[24,11];break;case 7:p=[20,11];break;case 8:p=[32,11];break;case 9:p=[80,33];break;case 10:p=[18,11];break;case 11:p=[15,11];break;case 12:p=[64,33];break;case 13:p=[160,99];break;case 14:p=[4,3];break;case 15:p=[3,2];break;case 16:p=[2,1];break;case 255:p=[e.readUnsignedByte()<<8|e.readUnsignedByte(),e.readUnsignedByte()<<8|e.readUnsignedByte()]}p&&(_=p[0]/p[1])}return{profileIdc:i,levelIdc:r,profileCompatibility:n,width:Math.ceil((16*(l+1)-2*m-2*g)*_),height:(2-h)*(c+1)*16-2*y-2*v}}},kt.prototype=new W;var At,Lt={H264Stream:kt,NalByteStream:Et};At=function(){var t=new Uint8Array,e=0;At.prototype.init.call(this),this.setTimestamp=function(t){e=t},this.parseId3TagSize=function(t,e){var i=t[e+6]<<21|t[e+7]<<14|t[e+8]<<7|t[e+9];return(16&t[e+5])>>4?i+20:i+10},this.parseAdtsSize=function(t,e){var i=(224&t[e+5])>>5,r=t[e+4]<<3;return 6144&t[e+3]|r|i},this.push=function(i){var r,n,s,a,o=0,u=0;for(t.length?(a=t.length,t=new Uint8Array(i.byteLength+a),t.set(t.subarray(0,a)),t.set(i,a)):t=i;t.length-u>=3;)if(t[u]!=="I".charCodeAt(0)||t[u+1]!=="D".charCodeAt(0)||t[u+2]!=="3".charCodeAt(0))if(!0&t[u]&&240==(240&t[u+1])){if(t.length-u<7)break;if((o=this.parseAdtsSize(t,u))>t.length)break;s={type:"audio",data:t.subarray(u,u+o),pts:e,dts:e},this.trigger("data",s),u+=o}else u++;else{if(t.length-u<10)break;if((o=this.parseId3TagSize(t,u))>t.length)break;n={type:"timed-metadata",data:t.subarray(u,u+o)},this.trigger("data",n),u+=o}r=t.length-u,t=r>0?t.subarray(u):new Uint8Array}},At.prototype=new W;var Ot,Pt,It,xt,Dt,Mt,Rt=At,Ut=[33,16,5,32,164,27],Nt=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],Bt=function(t){for(var e=[];t--;)e.push(0);return e},jt={96e3:[Ut,[227,64],Bt(154),[56]],88200:[Ut,[231],Bt(170),[56]],64e3:[Ut,[248,192],Bt(240),[56]],48e3:[Ut,[255,192],Bt(268),[55,148,128],Bt(54),[112]],44100:[Ut,[255,192],Bt(268),[55,163,128],Bt(84),[112]],32e3:[Ut,[255,192],Bt(268),[55,234],Bt(226),[112]],24e3:[Ut,[255,192],Bt(268),[55,255,128],Bt(268),[111,112],Bt(126),[224]],16e3:[Ut,[255,192],Bt(268),[55,255,128],Bt(268),[111,255],Bt(269),[223,108],Bt(195),[1,192]],12e3:[Nt,Bt(268),[3,127,248],Bt(268),[6,255,240],Bt(268),[13,255,224],Bt(268),[27,253,128],Bt(259),[56]],11025:[Nt,Bt(268),[3,127,248],Bt(268),[6,255,240],Bt(268),[13,255,224],Bt(268),[27,255,192],Bt(268),[55,175,128],Bt(108),[112]],8e3:[Nt,Bt(268),[3,121,16],Bt(47),[7]]},Ft=function(t){return Object.keys(t).reduce(function(e,i){return e[i]=new Uint8Array(t[i].reduce(function(t,e){return t.concat(e)},[])),e},{})}(jt);Ot=function(t){return 9e4*t},Pt=function(t,e){return t*e},It=function(t){return t/9e4},xt=function(t,e){return t/e},Dt=function(t,e){return Ot(xt(t,e))},Mt=function(t,e){return Pt(It(t),e)};var Ht,Vt,qt,Wt,Gt,zt,Xt,Yt,$t,Kt,Jt,Qt={secondsToVideoTs:Ot,secondsToAudioTs:Pt,videoTsToSeconds:It,audioTsToSeconds:xt,audioTsToVideoTs:Dt,videoTsToAudioTs:Mt},Zt=Lt.H264Stream,te=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],ee=["width","height","profileIdc","levelIdc","profileCompatibility"];Gt=function(){return{size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0}}},zt=function(t){return t[0]==="I".charCodeAt(0)&&t[1]==="D".charCodeAt(0)&&t[2]==="3".charCodeAt(0)},Kt=function(t,e){var i;if(t.length!==e.length)return!1;for(i=0;i<t.length;i++)if(t[i]!==e[i])return!1;return!0},Jt=function(t){var e,i,r=0;for(e=0;e<t.length;e++)i=t[e],r+=i.data.byteLength;return r},Vt=function(t,e){var i=[],r=0,n=0,s=0,a=1/0;e=e||{},Vt.prototype.init.call(this),this.push=function(e){Xt(t,e),t&&te.forEach(function(i){t[i]=e[i]}),i.push(e)},this.setEarliestDts=function(e){n=e-t.timelineStartInfo.baseMediaDecodeTime},this.setVideoBaseMediaDecodeTime=function(t){a=t},this.setAudioAppendStart=function(t){s=t},this.flush=function(){var n,s,a,o;if(0===i.length)return void this.trigger("done","AudioSegmentStream");n=this.trimAdtsFramesByEarliestDts_(i),t.baseMediaDecodeTime=$t(t,e.keepOriginalTimestamps),this.prefixWithSilence_(t,n),t.samples=this.generateSampleTable_(n),a=V.mdat(this.concatenateFrameData_(n)),i=[],s=V.moof(r,[t]),o=new Uint8Array(s.byteLength+a.byteLength),r++,o.set(s),o.set(a,s.byteLength),Yt(t),this.trigger("data",{track:t,boxes:o}),this.trigger("done","AudioSegmentStream")},this.prefixWithSilence_=function(t,e){var i,r,n,o=0,u=0,l=0,c=0;if(e.length&&(i=Qt.audioTsToVideoTs(t.baseMediaDecodeTime,t.samplerate),o=Math.ceil(9e4/(t.samplerate/1024)),s&&a&&(u=i-Math.max(s,a),l=Math.floor(u/o),c=l*o),!(l<1||c>45e3))){for(r=Ft[t.samplerate],r||(r=e[0].data),n=0;n<l;n++)e.splice(n,0,{data:r});t.baseMediaDecodeTime-=Math.floor(Qt.videoTsToAudioTs(c,t.samplerate))}},this.trimAdtsFramesByEarliestDts_=function(e){return t.minSegmentDts>=n?e:(t.minSegmentDts=1/0,e.filter(function(e){return e.dts>=n&&(t.minSegmentDts=Math.min(t.minSegmentDts,e.dts),t.minSegmentPts=t.minSegmentDts,!0)}))},this.generateSampleTable_=function(t){var e,i,r=[];for(e=0;e<t.length;e++)i=t[e],r.push({size:i.data.byteLength,duration:1024});return r},this.concatenateFrameData_=function(t){var e,i,r=0,n=new Uint8Array(Jt(t));for(e=0;e<t.length;e++)i=t[e],n.set(i.data,r),r+=i.data.byteLength;return n}},Vt.prototype=new W,Ht=function(t,e){var i,r,n=0,s=[],a=[];e=e||{},Ht.prototype.init.call(this),delete t.minPTS,this.gopCache_=[],this.push=function(e){Xt(t,e),"seq_parameter_set_rbsp"!==e.nalUnitType||i||(i=e.config,t.sps=[e.data],ee.forEach(function(e){t[e]=i[e]},this)),"pic_parameter_set_rbsp"!==e.nalUnitType||r||(r=e.data,t.pps=[e.data]),s.push(e)},this.flush=function(){for(var i,r,o,u,l,c;s.length&&"access_unit_delimiter_rbsp"!==s[0].nalUnitType;)s.shift();if(0===s.length)return this.resetStream_(),void this.trigger("done","VideoSegmentStream");if(i=this.groupNalsIntoFrames_(s),o=this.groupFramesIntoGops_(i),o[0][0].keyFrame||(r=this.getGopForFusion_(s[0],t),r?(o.unshift(r),o.byteLength+=r.byteLength,o.nalCount+=r.nalCount,o.pts=r.pts,o.dts=r.dts,o.duration+=r.duration):o=this.extendFirstKeyFrame_(o)),a.length){var h;if(!(h=e.alignGopsAtEnd?this.alignGopsAtEnd_(o):this.alignGopsAtStart_(o)))return this.gopCache_.unshift({gop:o.pop(),pps:t.pps,sps:t.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.resetStream_(),void this.trigger("done","VideoSegmentStream");Yt(t),o=h}Xt(t,o),t.samples=this.generateSampleTable_(o),l=V.mdat(this.concatenateNalData_(o)),t.baseMediaDecodeTime=$t(t,e.keepOriginalTimestamps),this.trigger("processedGopsInfo",o.map(function(t){return{pts:t.pts,dts:t.dts,byteLength:t.byteLength}})),this.gopCache_.unshift({gop:o.pop(),pps:t.pps,sps:t.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.trigger("baseMediaDecodeTime",t.baseMediaDecodeTime),this.trigger("timelineStartInfo",t.timelineStartInfo),u=V.moof(n,[t]),c=new Uint8Array(u.byteLength+l.byteLength),n++,c.set(u),c.set(l,u.byteLength),this.trigger("data",{track:t,boxes:c}),this.resetStream_(),this.trigger("done","VideoSegmentStream")},this.resetStream_=function(){Yt(t),i=void 0,r=void 0},this.getGopForFusion_=function(e){var i,r,n,s,a,o=1/0;for(a=0;a<this.gopCache_.length;a++)s=this.gopCache_[a],n=s.gop,t.pps&&Kt(t.pps[0],s.pps[0])&&t.sps&&Kt(t.sps[0],s.sps[0])&&(n.dts<t.timelineStartInfo.dts||(i=e.dts-n.dts-n.duration)>=-1e4&&i<=45e3&&(!r||o>i)&&(r=s,o=i));return r?r.gop:null},this.extendFirstKeyFrame_=function(t){var e;return!t[0][0].keyFrame&&t.length>1&&(e=t.shift(),t.byteLength-=e.byteLength,t.nalCount-=e.nalCount,t[0][0].dts=e.dts,t[0][0].pts=e.pts,t[0][0].duration+=e.duration),t},this.groupNalsIntoFrames_=function(t){var e,i,r=[],n=[];for(r.byteLength=0,e=0;e<t.length;e++)i=t[e],"access_unit_delimiter_rbsp"===i.nalUnitType?(r.length&&(r.duration=i.dts-r.dts,n.push(r)),r=[i],r.byteLength=i.data.byteLength,r.pts=i.pts,r.dts=i.dts):("slice_layer_without_partitioning_rbsp_idr"===i.nalUnitType&&(r.keyFrame=!0),r.duration=i.dts-r.dts,r.byteLength+=i.data.byteLength,r.push(i));return n.length&&(!r.duration||r.duration<=0)&&(r.duration=n[n.length-1].duration),n.push(r),n},this.groupFramesIntoGops_=function(t){var e,i,r=[],n=[];for(r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=t[0].pts,r.dts=t[0].dts,n.byteLength=0,n.nalCount=0,n.duration=0,n.pts=t[0].pts,n.dts=t[0].dts,e=0;e<t.length;e++)i=t[e],i.keyFrame?(r.length&&(n.push(r),n.byteLength+=r.byteLength,n.nalCount+=r.nalCount,n.duration+=r.duration),r=[i],r.nalCount=i.length,r.byteLength=i.byteLength,r.pts=i.pts,r.dts=i.dts,
r.duration=i.duration):(r.duration+=i.duration,r.nalCount+=i.length,r.byteLength+=i.byteLength,r.push(i));return n.length&&r.duration<=0&&(r.duration=n[n.length-1].duration),n.byteLength+=r.byteLength,n.nalCount+=r.nalCount,n.duration+=r.duration,n.push(r),n},this.generateSampleTable_=function(t,e){var i,r,n,s,a,o=e||0,u=[];for(i=0;i<t.length;i++)for(s=t[i],r=0;r<s.length;r++)a=s[r],n=Gt(),n.dataOffset=o,n.compositionTimeOffset=a.pts-a.dts,n.duration=a.duration,n.size=4*a.length,n.size+=a.byteLength,a.keyFrame&&(n.flags.dependsOn=2),o+=n.size,u.push(n);return u},this.concatenateNalData_=function(t){var e,i,r,n,s,a,o=0,u=t.byteLength,l=t.nalCount,c=u+4*l,h=new Uint8Array(c),d=new DataView(h.buffer);for(e=0;e<t.length;e++)for(n=t[e],i=0;i<n.length;i++)for(s=n[i],r=0;r<s.length;r++)a=s[r],d.setUint32(o,a.data.byteLength),o+=4,h.set(a.data,o),o+=a.data.byteLength;return h},this.alignGopsAtStart_=function(t){var e,i,r,n,s,o,u,l;for(s=t.byteLength,o=t.nalCount,u=t.duration,e=i=0;e<a.length&&i<t.length&&(r=a[e],n=t[i],r.pts!==n.pts);)n.pts>r.pts?e++:(i++,s-=n.byteLength,o-=n.nalCount,u-=n.duration);return 0===i?t:i===t.length?null:(l=t.slice(i),l.byteLength=s,l.duration=u,l.nalCount=o,l.pts=l[0].pts,l.dts=l[0].dts,l)},this.alignGopsAtEnd_=function(t){var e,i,r,n,s,o;for(e=a.length-1,i=t.length-1,s=null,o=!1;e>=0&&i>=0;){if(r=a[e],n=t[i],r.pts===n.pts){o=!0;break}r.pts>n.pts?e--:(e===a.length-1&&(s=i),i--)}if(!o&&null===s)return null;var u;if(0===(u=o?i:s))return t;var l=t.slice(u),c=l.reduce(function(t,e){return t.byteLength+=e.byteLength,t.duration+=e.duration,t.nalCount+=e.nalCount,t},{byteLength:0,duration:0,nalCount:0});return l.byteLength=c.byteLength,l.duration=c.duration,l.nalCount=c.nalCount,l.pts=l[0].pts,l.dts=l[0].dts,l},this.alignGopsWith=function(t){a=t}},Ht.prototype=new W,Xt=function(t,e){"number"==typeof e.pts&&(void 0===t.timelineStartInfo.pts&&(t.timelineStartInfo.pts=e.pts),void 0===t.minSegmentPts?t.minSegmentPts=e.pts:t.minSegmentPts=Math.min(t.minSegmentPts,e.pts),void 0===t.maxSegmentPts?t.maxSegmentPts=e.pts:t.maxSegmentPts=Math.max(t.maxSegmentPts,e.pts)),"number"==typeof e.dts&&(void 0===t.timelineStartInfo.dts&&(t.timelineStartInfo.dts=e.dts),void 0===t.minSegmentDts?t.minSegmentDts=e.dts:t.minSegmentDts=Math.min(t.minSegmentDts,e.dts),void 0===t.maxSegmentDts?t.maxSegmentDts=e.dts:t.maxSegmentDts=Math.max(t.maxSegmentDts,e.dts))},Yt=function(t){delete t.minSegmentDts,delete t.maxSegmentDts,delete t.minSegmentPts,delete t.maxSegmentPts},$t=function(t,e){var i,r,n=t.minSegmentDts;return e||(n-=t.timelineStartInfo.dts),i=t.timelineStartInfo.baseMediaDecodeTime,i+=n,i=Math.max(0,i),"audio"===t.type&&(r=t.samplerate/9e4,i*=r,i=Math.floor(i)),i},Wt=function(t,e){this.numberOfTracks=0,this.metadataStream=e,void 0!==t.remux?this.remuxTracks=!!t.remux:this.remuxTracks=!0,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,Wt.prototype.init.call(this),this.push=function(t){return t.text?this.pendingCaptions.push(t):t.frames?this.pendingMetadata.push(t):(this.pendingTracks.push(t.track),this.pendingBoxes.push(t.boxes),this.pendingBytes+=t.boxes.byteLength,"video"===t.track.type&&(this.videoTrack=t.track),void("audio"===t.track.type&&(this.audioTrack=t.track)))}},Wt.prototype=new W,Wt.prototype.flush=function(t){var e,i,r,n,s=0,a={captions:[],captionStreams:{},metadata:[],info:{}},o=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==t&&"AudioSegmentStream"!==t)return;if(this.remuxTracks)return;if(0===this.pendingTracks.length)return void(++this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}for(this.videoTrack?(o=this.videoTrack.timelineStartInfo.pts,ee.forEach(function(t){a.info[t]=this.videoTrack[t]},this)):this.audioTrack&&(o=this.audioTrack.timelineStartInfo.pts,te.forEach(function(t){a.info[t]=this.audioTrack[t]},this)),1===this.pendingTracks.length?a.type=this.pendingTracks[0].type:a.type="combined",this.emittedTracks+=this.pendingTracks.length,r=V.initSegment(this.pendingTracks),a.initSegment=new Uint8Array(r.byteLength),a.initSegment.set(r),a.data=new Uint8Array(this.pendingBytes),n=0;n<this.pendingBoxes.length;n++)a.data.set(this.pendingBoxes[n],s),s+=this.pendingBoxes[n].byteLength;for(n=0;n<this.pendingCaptions.length;n++)e=this.pendingCaptions[n],e.startTime=e.startPts-o,e.startTime/=9e4,e.endTime=e.endPts-o,e.endTime/=9e4,a.captionStreams[e.stream]=!0,a.captions.push(e);for(n=0;n<this.pendingMetadata.length;n++)i=this.pendingMetadata[n],i.cueTime=i.pts-o,i.cueTime/=9e4,a.metadata.push(i);a.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",a),this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},qt=function(t){var e,i,r=this,n=!0;qt.prototype.init.call(this),t=t||{},this.baseMediaDecodeTime=t.baseMediaDecodeTime||0,this.transmuxPipeline_={},this.setupAacPipeline=function(){var e={};this.transmuxPipeline_=e,e.type="aac",e.metadataStream=new _t.MetadataStream,e.aacStream=new Rt,e.audioTimestampRolloverStream=new _t.TimestampRolloverStream("audio"),e.timedMetadataTimestampRolloverStream=new _t.TimestampRolloverStream("timed-metadata"),e.adtsStream=new St,e.coalesceStream=new Wt(t,e.metadataStream),e.headOfPipeline=e.aacStream,e.aacStream.pipe(e.audioTimestampRolloverStream).pipe(e.adtsStream),e.aacStream.pipe(e.timedMetadataTimestampRolloverStream).pipe(e.metadataStream).pipe(e.coalesceStream),e.metadataStream.on("timestamp",function(t){e.aacStream.setTimestamp(t.timeStamp)}),e.aacStream.on("data",function(n){"timed-metadata"!==n.type||e.audioSegmentStream||(i=i||{timelineStartInfo:{baseMediaDecodeTime:r.baseMediaDecodeTime},codec:"adts",type:"audio"},e.coalesceStream.numberOfTracks++,e.audioSegmentStream=new Vt(i,t),e.adtsStream.pipe(e.audioSegmentStream).pipe(e.coalesceStream))}),e.coalesceStream.on("data",this.trigger.bind(this,"data")),e.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setupTsPipeline=function(){var n={};this.transmuxPipeline_=n,n.type="ts",n.metadataStream=new _t.MetadataStream,n.packetStream=new _t.TransportPacketStream,n.parseStream=new _t.TransportParseStream,n.elementaryStream=new _t.ElementaryStream,n.videoTimestampRolloverStream=new _t.TimestampRolloverStream("video"),n.audioTimestampRolloverStream=new _t.TimestampRolloverStream("audio"),n.timedMetadataTimestampRolloverStream=new _t.TimestampRolloverStream("timed-metadata"),n.adtsStream=new St,n.h264Stream=new Zt,n.captionStream=new _t.CaptionStream,n.coalesceStream=new Wt(t,n.metadataStream),n.headOfPipeline=n.packetStream,n.packetStream.pipe(n.parseStream).pipe(n.elementaryStream),n.elementaryStream.pipe(n.videoTimestampRolloverStream).pipe(n.h264Stream),n.elementaryStream.pipe(n.audioTimestampRolloverStream).pipe(n.adtsStream),n.elementaryStream.pipe(n.timedMetadataTimestampRolloverStream).pipe(n.metadataStream).pipe(n.coalesceStream),n.h264Stream.pipe(n.captionStream).pipe(n.coalesceStream),n.elementaryStream.on("data",function(s){var a;if("metadata"===s.type){for(a=s.tracks.length;a--;)e||"video"!==s.tracks[a].type?i||"audio"!==s.tracks[a].type||(i=s.tracks[a],i.timelineStartInfo.baseMediaDecodeTime=r.baseMediaDecodeTime):(e=s.tracks[a],e.timelineStartInfo.baseMediaDecodeTime=r.baseMediaDecodeTime);e&&!n.videoSegmentStream&&(n.coalesceStream.numberOfTracks++,n.videoSegmentStream=new Ht(e,t),n.videoSegmentStream.on("timelineStartInfo",function(t){i&&(i.timelineStartInfo=t,n.audioSegmentStream.setEarliestDts(t.dts))}),n.videoSegmentStream.on("processedGopsInfo",r.trigger.bind(r,"gopInfo")),n.videoSegmentStream.on("baseMediaDecodeTime",function(t){i&&n.audioSegmentStream.setVideoBaseMediaDecodeTime(t)}),n.h264Stream.pipe(n.videoSegmentStream).pipe(n.coalesceStream)),i&&!n.audioSegmentStream&&(n.coalesceStream.numberOfTracks++,n.audioSegmentStream=new Vt(i,t),n.adtsStream.pipe(n.audioSegmentStream).pipe(n.coalesceStream))}}),n.coalesceStream.on("data",this.trigger.bind(this,"data")),n.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setBaseMediaDecodeTime=function(t){var r=this.transmuxPipeline_;this.baseMediaDecodeTime=t,i&&(i.timelineStartInfo.dts=void 0,i.timelineStartInfo.pts=void 0,Yt(i),i.timelineStartInfo.baseMediaDecodeTime=t,r.audioTimestampRolloverStream&&r.audioTimestampRolloverStream.discontinuity()),e&&(r.videoSegmentStream&&(r.videoSegmentStream.gopCache_=[],r.videoTimestampRolloverStream.discontinuity()),e.timelineStartInfo.dts=void 0,e.timelineStartInfo.pts=void 0,Yt(e),r.captionStream.reset(),e.timelineStartInfo.baseMediaDecodeTime=t),r.timedMetadataTimestampRolloverStream&&r.timedMetadataTimestampRolloverStream.discontinuity()},this.setAudioAppendStart=function(t){i&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(t)},this.alignGopsWith=function(t){e&&this.transmuxPipeline_.videoSegmentStream&&this.transmuxPipeline_.videoSegmentStream.alignGopsWith(t)},this.push=function(t){if(n){var e=zt(t);e&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():e||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),n=!1}this.transmuxPipeline_.headOfPipeline.push(t)},this.flush=function(){n=!0,this.transmuxPipeline_.headOfPipeline.flush()},this.resetCaptions=function(){this.transmuxPipeline_.captionStream&&this.transmuxPipeline_.captionStream.reset()}},qt.prototype=new W;var ie={Transmuxer:qt,VideoSegmentStream:Ht,AudioSegmentStream:Vt,AUDIO_PROPERTIES:te,VIDEO_PROPERTIES:ee},re={generator:V,Transmuxer:ie.Transmuxer,AudioSegmentStream:ie.AudioSegmentStream,VideoSegmentStream:ie.VideoSegmentStream},ne=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},se=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),ae=function(t){t.on("data",function(t){var e=t.initSegment;t.initSegment={data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength};var i=t.data;t.data=i.buffer,F.postMessage({action:"data",segment:t,byteOffset:i.byteOffset,byteLength:i.byteLength},[t.data])}),t.captionStream&&t.captionStream.on("data",function(t){F.postMessage({action:"caption",data:t})}),t.on("done",function(t){F.postMessage({action:"done"})}),t.on("gopInfo",function(t){F.postMessage({action:"gopInfo",gopInfo:t})})},oe=function(){function t(e){ne(this,t),this.options=e||{},this.init()}return se(t,[{key:"init",value:function(){this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new re.Transmuxer(this.options),ae(this.transmuxer)}},{key:"push",value:function(t){var e=new Uint8Array(t.data,t.byteOffset,t.byteLength);this.transmuxer.push(e)}},{key:"reset",value:function(){this.init()}},{key:"setTimestampOffset",value:function(t){var e=t.timestampOffset||0;this.transmuxer.setBaseMediaDecodeTime(Math.round(9e4*e))}},{key:"setAudioAppendStart",value:function(t){this.transmuxer.setAudioAppendStart(Math.ceil(9e4*t.appendStart))}},{key:"flush",value:function(t){this.transmuxer.flush()}},{key:"resetCaptions",value:function(){this.transmuxer.resetCaptions()}},{key:"alignGopsWith",value:function(t){this.transmuxer.alignGopsWith(t.gopsToAlignWith.slice())}}]),t}();new function(t){t.onmessage=function(t){if("init"===t.data.action&&t.data.options)return void(this.messageHandlers=new oe(t.data.options));this.messageHandlers||(this.messageHandlers=new oe),t.data&&t.data.action&&"init"!==t.data.action&&this.messageHandlers[t.data.action]&&this.messageHandlers[t.data.action](t.data)}}(i)}()}),pl=function(t){return/mp4a\.\d+.\d+/i.test(t)},fl=function(t){return/avc1\.[\da-f]+/i.test(t)},ml=function(t){var e={type:"",parameters:{}},i=t.trim().split(";");return e.type=i.shift().trim(),i.forEach(function(t){var i=t.trim().split("=");if(i.length>1){var r=i[0].replace(/"/g,"").trim(),n=i[1].replace(/"/g,"").trim();e.parameters[r]=n}}),e},gl=function(t){return t.map(function(t){return t.replace(/avc1\.(\d+)\.(\d+)/i,function(t,e,i){return"avc1."+("00"+Number(e).toString(16)).slice(-2)+"00"+("00"+Number(i).toString(16)).slice(-2)})})},yl=function(t,e){var i=t.addSourceBuffer(e),r=Object.create(null);r.updating=!1,r.realBuffer_=i;for(var n in i)!function(t){"function"==typeof i[t]?r[t]=function(){return i[t].apply(i,arguments)}:void 0===r[t]&&Object.defineProperty(r,t,{get:function(){return i[t]},set:function(e){return i[t]=e}})}(n);return r},vl=function(t,e,i){if(!e||!t.length)return[];var r=Math.ceil(9e4*(e.currentTime()-i+3)),n=void 0;for(n=0;n<t.length&&!(t[n].pts>r);n++);return t.slice(n)},_l=function(t,e,i){if(!e.length)return t;if(i)return e.slice();var r=e[0].pts,n=0;for(n;n<t.length&&!(t[n].pts>=r);n++);return t.slice(0,n).concat(e)},bl=function(t,e,i,r){for(var n=Math.ceil(9e4*(e-r)),s=Math.ceil(9e4*(i-r)),a=t.slice(),o=t.length;o--&&!(t[o].pts<=s););if(-1===o)return a;for(var u=o+1;u--&&!(t[u].pts<=n););return u=Math.max(u,0),a.splice(u,o-u+1),a},Tl=function(t){function e(i,r){Ie(this,e);var n=De(this,t.call(this,le.EventTarget));n.timestampOffset_=0,n.pendingBuffers_=[],n.bufferUpdating_=!1,n.mediaSource_=i,n.codecs_=r,n.audioCodec_=null,n.videoCodec_=null,n.audioDisabled_=!1,n.appendAudioInitSegment_=!0,n.gopBuffer_=[],n.timeMapping_=0,n.safeAppend_=le.browser.IE_VERSION>=11;var s={remux:!1,alignGopsAtEnd:n.safeAppend_};return n.codecs_.forEach(function(t){pl(t)?n.audioCodec_=t:fl(t)&&(n.videoCodec_=t)}),n.transmuxer_=new dl,n.transmuxer_.postMessage({action:"init",options:s}),n.transmuxer_.onmessage=function(t){return"data"===t.data.action?n.data_(t):"done"===t.data.action?n.done_(t):"gopInfo"===t.data.action?n.appendGopInfo_(t):void 0},Object.defineProperty(n,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(t){"number"==typeof t&&t>=0&&(this.timestampOffset_=t,this.appendAudioInitSegment_=!0,this.gopBuffer_.length=0,this.timeMapping_=0,this.transmuxer_.postMessage({action:"setTimestampOffset",timestampOffset:t}))}}),Object.defineProperty(n,"appendWindowStart",{get:function(){return(this.videoBuffer_||this.audioBuffer_).appendWindowStart},set:function(t){this.videoBuffer_&&(this.videoBuffer_.appendWindowStart=t),this.audioBuffer_&&(this.audioBuffer_.appendWindowStart=t)}}),Object.defineProperty(n,"updating",{get:function(){return!!(this.bufferUpdating_||!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.updating||this.videoBuffer_&&this.videoBuffer_.updating)}}),Object.defineProperty(n,"buffered",{get:function(){var t=null,e=null,i=0,r=[],n=[];if(!this.videoBuffer_&&!this.audioBuffer_)return le.createTimeRange();if(!this.videoBuffer_)return this.audioBuffer_.buffered;if(!this.audioBuffer_)return this.videoBuffer_.buffered;if(this.audioDisabled_)return this.videoBuffer_.buffered;if(0===this.videoBuffer_.buffered.length&&0===this.audioBuffer_.buffered.length)return le.createTimeRange();for(var s=this.videoBuffer_.buffered,a=this.audioBuffer_.buffered,o=s.length;o--;)r.push({time:s.start(o),type:"start"}),r.push({time:s.end(o),type:"end"});for(o=a.length;o--;)r.push({time:a.start(o),type:"start"}),r.push({time:a.end(o),type:"end"});for(r.sort(function(t,e){return t.time-e.time}),o=0;o<r.length;o++)"start"===r[o].type?2===++i&&(t=r[o].time):"end"===r[o].type&&1===--i&&(e=r[o].time),null!==t&&null!==e&&(n.push([t,e]),t=null,e=null);return le.createTimeRanges(n)}}),n}return xe(e,t),e.prototype.data_=function(t){var e=t.data.segment;e.data=new Uint8Array(e.data,t.data.byteOffset,t.data.byteLength),e.initSegment=new Uint8Array(e.initSegment.data,e.initSegment.byteOffset,e.initSegment.byteLength),Qu(this,this.mediaSource_,e),this.pendingBuffers_.push(e)},e.prototype.done_=function(t){if("closed"===this.mediaSource_.readyState)return void(this.pendingBuffers_.length=0);this.processPendingSegments_()},e.prototype.createRealSourceBuffers_=function(){var t=this,e=["audio","video"];e.forEach(function(i){if(t[i+"Codec_"]&&!t[i+"Buffer_"]){var r=null;if(t.mediaSource_[i+"Buffer_"])r=t.mediaSource_[i+"Buffer_"],r.updating=!1;else{var n=i+"Codec_",s=i+'/mp4;codecs="'+t[n]+'"';r=yl(t.mediaSource_.nativeMediaSource_,s),t.mediaSource_[i+"Buffer_"]=r}t[i+"Buffer_"]=r,["update","updatestart","updateend"].forEach(function(n){r.addEventListener(n,function(){if("audio"!==i||!t.audioDisabled_){"updateend"===n&&(t[i+"Buffer_"].updating=!1);return e.every(function(e){return!("audio"!==e||!t.audioDisabled_)||(i===e||!t[e+"Buffer_"]||!t[e+"Buffer_"].updating)})?t.trigger(n):void 0}})})}})},e.prototype.appendBuffer=function(t){if(this.bufferUpdating_=!0,this.audioBuffer_&&this.audioBuffer_.buffered.length){var e=this.audioBuffer_.buffered;this.transmuxer_.postMessage({action:"setAudioAppendStart",appendStart:e.end(e.length-1)})}this.videoBuffer_&&this.transmuxer_.postMessage({action:"alignGopsWith",gopsToAlignWith:vl(this.gopBuffer_,this.mediaSource_.player_,this.timeMapping_)}),this.transmuxer_.postMessage({action:"push",data:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength},[t.buffer]),this.transmuxer_.postMessage({action:"flush"})},e.prototype.appendGopInfo_=function(t){this.gopBuffer_=_l(this.gopBuffer_,t.data.gopInfo,this.safeAppend_)},e.prototype.remove=function(t,e){if(this.videoBuffer_&&(this.videoBuffer_.updating=!0,this.videoBuffer_.remove(t,e),this.gopBuffer_=bl(this.gopBuffer_,t,e,this.timeMapping_)),!this.audioDisabled_&&this.audioBuffer_&&(this.audioBuffer_.updating=!0,this.audioBuffer_.remove(t,e)),Zu(t,e,this.metadataTrack_),this.inbandTextTracks_)for(var i in this.inbandTextTracks_)Zu(t,e,this.inbandTextTracks_[i])},e.prototype.processPendingSegments_=function(){var t={video:{segments:[],bytes:0},audio:{segments:[],bytes:0},captions:[],metadata:[]};t=this.pendingBuffers_.reduce(function(t,e){var i=e.type,r=e.data,n=e.initSegment;return t[i].segments.push(r),t[i].bytes+=r.byteLength,t[i].initSegment=n,e.captions&&(t.captions=t.captions.concat(e.captions)),e.info&&(t[i].info=e.info),e.metadata&&(t.metadata=t.metadata.concat(e.metadata)),t},t),this.videoBuffer_||this.audioBuffer_||(0===t.video.bytes&&(this.videoCodec_=null),0===t.audio.bytes&&(this.audioCodec_=null),this.createRealSourceBuffers_()),t.audio.info&&this.mediaSource_.trigger({type:"audioinfo",info:t.audio.info}),t.video.info&&this.mediaSource_.trigger({type:"videoinfo",info:t.video.info}),this.appendAudioInitSegment_&&(!this.audioDisabled_&&this.audioBuffer_&&(t.audio.segments.unshift(t.audio.initSegment),t.audio.bytes+=t.audio.initSegment.byteLength),this.appendAudioInitSegment_=!1);var e=!1;this.videoBuffer_&&t.video.bytes?(t.video.segments.unshift(t.video.initSegment),t.video.bytes+=t.video.initSegment.byteLength,this.concatAndAppendSegments_(t.video,this.videoBuffer_),il(this,t.captions,t.metadata)):!this.videoBuffer_||!this.audioDisabled_&&this.audioBuffer_||(e=!0),!this.audioDisabled_&&this.audioBuffer_&&this.concatAndAppendSegments_(t.audio,this.audioBuffer_),this.pendingBuffers_.length=0,e&&this.trigger("updateend"),this.bufferUpdating_=!1},e.prototype.concatAndAppendSegments_=function(t,e){var i=0,r=void 0;if(t.bytes){r=new Uint8Array(t.bytes),t.segments.forEach(function(t){r.set(t,i),i+=t.byteLength});try{e.updating=!0,e.appendBuffer(r)}catch(t){this.mediaSource_.player_&&this.mediaSource_.player_.error({code:-3,type:"APPEND_BUFFER_ERR",message:t.message,originalError:t})}}},e.prototype.abort=function(){this.videoBuffer_&&this.videoBuffer_.abort(),!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.abort(),this.transmuxer_&&this.transmuxer_.postMessage({action:"reset"}),this.pendingBuffers_.length=0,this.bufferUpdating_=!1},e}(le.EventTarget),Sl=function(t){function e(){Ie(this,e);var i=De(this,t.call(this)),r=void 0;i.nativeMediaSource_=new ve.MediaSource;for(r in i.nativeMediaSource_)r in e.prototype||"function"!=typeof i.nativeMediaSource_[r]||(i[r]=i.nativeMediaSource_[r].bind(i.nativeMediaSource_));return i.duration_=NaN,Object.defineProperty(i,"duration",{get:function(){return this.duration_===1/0?this.duration_:this.nativeMediaSource_.duration},set:function(t){if(this.duration_=t,t!==1/0)return void(this.nativeMediaSource_.duration=t)}}),Object.defineProperty(i,"seekable",{get:function(){return this.duration_===1/0?le.createTimeRanges([[0,this.nativeMediaSource_.duration]]):this.nativeMediaSource_.seekable}}),Object.defineProperty(i,"readyState",{get:function(){return this.nativeMediaSource_.readyState}}),Object.defineProperty(i,"activeSourceBuffers",{get:function(){return this.activeSourceBuffers_}}),i.sourceBuffers=[],i.activeSourceBuffers_=[],i.updateActiveSourceBuffers_=function(){if(i.activeSourceBuffers_.length=0,1===i.sourceBuffers.length){var t=i.sourceBuffers[0];return t.appendAudioInitSegment_=!0,t.audioDisabled_=!t.audioCodec_,void i.activeSourceBuffers_.push(t)}for(var e=!1,r=!0,n=0;n<i.player_.audioTracks().length;n++){var s=i.player_.audioTracks()[n];if(s.enabled&&"main"!==s.kind){e=!0,r=!1;break}}i.sourceBuffers.forEach(function(t,n){if(t.appendAudioInitSegment_=!0,t.videoCodec_&&t.audioCodec_)t.audioDisabled_=e;else if(t.videoCodec_&&!t.audioCodec_)t.audioDisabled_=!0,r=!1;else if(!t.videoCodec_&&t.audioCodec_&&(t.audioDisabled_=n?r:!r,t.audioDisabled_))return;i.activeSourceBuffers_.push(t)})},i.onPlayerMediachange_=function(){i.sourceBuffers.forEach(function(t){t.appendAudioInitSegment_=!0})},i.onHlsReset_=function(){i.sourceBuffers.forEach(function(t){t.transmuxer_&&t.transmuxer_.postMessage({action:"resetCaptions"})})},i.onHlsSegmentTimeMapping_=function(t){i.sourceBuffers.forEach(function(e){return e.timeMapping_=t.mapping})},["sourceopen","sourceclose","sourceended"].forEach(function(t){this.nativeMediaSource_.addEventListener(t,this.trigger.bind(this))},i),i.on("sourceopen",function(t){var e=ke.querySelector('[src="'+i.url_+'"]');e&&(i.player_=le(e.parentNode),i.player_.tech_.on("hls-reset",i.onHlsReset_),i.player_.tech_.on("hls-segment-time-mapping",i.onHlsSegmentTimeMapping_),i.player_.audioTracks&&i.player_.audioTracks()&&(i.player_.audioTracks().on("change",i.updateActiveSourceBuffers_),i.player_.audioTracks().on("addtrack",i.updateActiveSourceBuffers_),i.player_.audioTracks().on("removetrack",i.updateActiveSourceBuffers_)),i.player_.on("mediachange",i.onPlayerMediachange_))}),i.on("sourceended",function(t){for(var e=el(i.duration),r=0;r<i.sourceBuffers.length;r++){var n=i.sourceBuffers[r],s=n.metadataTrack_&&n.metadataTrack_.cues;s&&s.length&&(s[s.length-1].endTime=e)}}),i.on("sourceclose",function(t){this.sourceBuffers.forEach(function(t){t.transmuxer_&&t.transmuxer_.terminate()}),this.sourceBuffers.length=0,this.player_&&(this.player_.audioTracks&&this.player_.audioTracks()&&(this.player_.audioTracks().off("change",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("addtrack",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("removetrack",this.updateActiveSourceBuffers_)),this.player_.el_&&(this.player_.off("mediachange",this.onPlayerMediachange_),this.player_.tech_.off("hls-reset",this.onHlsReset_),this.player_.tech_.off("hls-segment-time-mapping",this.onHlsSegmentTimeMapping_)))}),i}return xe(e,t),e.prototype.addSeekableRange_=function(t,e){var i=void 0;if(this.duration!==1/0)throw i=new Error("MediaSource.addSeekableRange() can only be invoked when the duration is Infinity"),i.name="InvalidStateError",i.code=11,i;(e>this.nativeMediaSource_.duration||isNaN(this.nativeMediaSource_.duration))&&(this.nativeMediaSource_.duration=e)},e.prototype.addSourceBuffer=function(t){var e=void 0,i=ml(t);if(/^(video|audio)\/mp2t$/i.test(i.type)){var r=[];i.parameters&&i.parameters.codecs&&(r=i.parameters.codecs.split(","),r=gl(r),r=r.filter(function(t){return pl(t)||fl(t)})),0===r.length&&(r=["avc1.4d400d","mp4a.40.2"]),e=new Tl(this,r),0!==this.sourceBuffers.length&&(this.sourceBuffers[0].createRealSourceBuffers_(),e.createRealSourceBuffers_(),this.sourceBuffers[0].audioDisabled_=!0)}else e=this.nativeMediaSource_.addSourceBuffer(t);return this.sourceBuffers.push(e),e},e}(le.EventTarget),kl=0;le.mediaSources={};var El=function(t,e){var i=le.mediaSources[t];if(!i)throw new Error("Media Source not found (Video.js)");i.trigger({type:"sourceopen",swfId:e})},Cl=function(){return!!ve.MediaSource&&!!ve.MediaSource.isTypeSupported&&ve.MediaSource.isTypeSupported('video/mp4;codecs="avc1.4d400d,mp4a.40.2"')},wl=function(){if(this.MediaSource={open:El,supportsNativeMediaSources:Cl},Cl())return new Sl;throw new Error("Cannot use create a virtual MediaSource for this video")};wl.open=El,wl.supportsNativeMediaSources=Cl;var Al={createObjectURL:function(t){var e=void 0;return t instanceof Sl?(e=ve.URL.createObjectURL(t.nativeMediaSource_),t.url_=e,e):t instanceof Sl?(e="blob:vjs-media-source/"+kl,kl++,le.mediaSources[e]=t,e):(e=ve.URL.createObjectURL(t),t.url_=e,e)}};le.MediaSource=wl,le.URL=Al;var Ll=le.EventTarget,Ol=le.mergeOptions,Pl=function(t,e){for(var i=Ol(t,{duration:e.duration,minimumUpdatePeriod:e.minimumUpdatePeriod}),r=0;r<e.playlists.length;r++){var n=nu(i,e.playlists[r]);n&&(i=n)}return eu(e,function(t,e,r,n){if(t.playlists&&t.playlists.length){var s=t.playlists[0].uri,a=nu(i,t.playlists[0]);a&&(i=a,i.mediaGroups[e][r][n].playlists[0]=i.playlists[s])}}),i},Il=function(t){function e(i,r,n,s){Ie(this,e);var a=De(this,t.call(this));if(a.hls_=r,a.withCredentials=n,!i)throw new Error("A non-empty playlist URL or playlist is required");return a.on("minimumUpdatePeriod",function(){a.refreshXml_()}),a.on("mediaupdatetimeout",function(){a.refreshMedia_()}),"string"==typeof i?(a.srcUrl=i,a.state="HAVE_NOTHING",De(a)):(a.masterPlaylistLoader_=s,a.state="HAVE_METADATA",a.started=!0,a.media(i),ve.setTimeout(function(){a.trigger("loadedmetadata")},0),a)}return xe(e,t),e.prototype.dispose=function(){this.stopRequest(),ve.clearTimeout(this.mediaUpdateTimeout)},e.prototype.stopRequest=function(){if(this.request){var t=this.request;this.request=null,t.onreadystatechange=null,t.abort()}},e.prototype.media=function(t){if(!t)return this.media_;if("HAVE_NOTHING"===this.state)throw new Error("Cannot switch media playlist from "+this.state);var e=this.state;if("string"==typeof t){if(!this.master.playlists[t])throw new Error("Unknown playlist URI: "+t);t=this.master.playlists[t]}var i=!this.media_||t.uri!==this.media_.uri;this.state="HAVE_METADATA",i&&(this.media_&&this.trigger("mediachanging"),this.media_=t,this.refreshMedia_(),"HAVE_MASTER"!==e&&this.trigger("mediachange"))},e.prototype.pause=function(){this.stopRequest(),"HAVE_NOTHING"===this.state&&(this.started=!1)},e.prototype.load=function(){if(!this.started)return void this.start();this.trigger("loadedplaylist")},e.prototype.parseMasterXml=function(){var t=Ya(this.masterXml_,{manifestUri:this.srcUrl,clientOffset:this.clientOffset_});t.uri=this.srcUrl;for(var e=0;e<t.playlists.length;e++){var i="placeholder-uri-"+e;t.playlists[e].uri=i,t.playlists[i]=t.playlists[e]}return eu(t,function(e,i,r,n){if(e.playlists&&e.playlists.length){var s="placeholder-uri-"+i+"-"+r+"-"+n;e.playlists[0].uri=s,t.playlists[s]=e.playlists[0]}}),su(t),au(t),t},e.prototype.start=function(){var t=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,i){if(t.request){if(t.request=null,e)return t.error={status:i.status,message:"DASH playlist request error at URL: "+t.srcUrl,responseText:i.responseText,code:2},"HAVE_NOTHING"===t.state&&(t.started=!1),t.trigger("error");t.masterXml_=i.responseText,i.responseHeaders&&i.responseHeaders.date?t.masterLoaded_=Date.parse(i.responseHeaders.date):t.masterLoaded_=Date.now(),t.syncClientServerClock_(t.onClientServerClockSync_.bind(t))}})},e.prototype.syncClientServerClock_=function(t){var e=this,i=$a(this.masterXml_);return null===i?(this.clientOffset_=this.masterLoaded_-Date.now(),t()):"DIRECT"===i.method?(this.clientOffset_=i.value-Date.now(),t()):void(this.request=this.hls_.xhr({uri:Jo(this.srcUrl,i.value),method:i.method,withCredentials:this.withCredentials},function(r,n){if(e.request){if(r)return e.clientOffset_=e.masterLoaded_-Date.now(),t();var s=void 0;s="HEAD"===i.method?n.responseHeaders&&n.responseHeaders.date?Date.parse(n.responseHeaders.date):e.masterLoaded_:Date.parse(n.responseText),e.clientOffset_=s-Date.now(),t()}}))},e.prototype.onClientServerClockSync_=function(){var t=this;this.master=this.parseMasterXml(),this.state="HAVE_MASTER",this.trigger("loadedplaylist"),this.media_||this.media(this.master.playlists[0]),ve.setTimeout(function(){t.trigger("loadedmetadata")},0),this.master.minimumUpdatePeriod&&ve.setTimeout(function(){t.trigger("minimumUpdatePeriod")},this.master.minimumUpdatePeriod)},e.prototype.refreshXml_=function(){var t=this;this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,i){if(t.request){if(t.request=null,e)return t.error={status:i.status,message:"DASH playlist request error at URL: "+t.srcUrl,responseText:i.responseText,code:2},"HAVE_NOTHING"===t.state&&(t.started=!1),t.trigger("error");t.masterXml_=i.responseText;var r=t.parseMasterXml();t.master=Pl(t.master,r),ve.setTimeout(function(){t.trigger("minimumUpdatePeriod")},t.master.minimumUpdatePeriod)}})},e.prototype.refreshMedia_=function(){var t=this,e=void 0,i=void 0;this.masterPlaylistLoader_?(e=this.masterPlaylistLoader_.master,i=this.masterPlaylistLoader_.parseMasterXml()):(e=this.master,i=this.parseMasterXml());var r=Pl(e,i);r?(this.masterPlaylistLoader_?this.masterPlaylistLoader_.master=r:this.master=r,this.media_=r.playlists[this.media_.uri]):this.trigger("playlistunchanged"),this.media().endList||(this.mediaUpdateTimeout=ve.setTimeout(function(){t.trigger("mediaupdatetimeout")},ou(this.media(),!!r))),this.trigger("loadedplaylist")},e}(Ll),xl=function(t){return le.log.debug?le.log.debug.bind(le,"VHS:",t+" >"):function(){}},Dl=function(){function t(e,i,r,n){Ie(this,t),this.callbacks_=[],this.pendingCallback_=null,this.timestampOffset_=0,this.mediaSource=e,this.processedAppend_=!1,this.type_=r,this.mimeType_=i,this.logger_=xl("SourceUpdater["+r+"]["+i+"]"),"closed"===e.readyState?e.addEventListener("sourceopen",this.createSourceBuffer_.bind(this,i,n)):this.createSourceBuffer_(i,n)}return t.prototype.createSourceBuffer_=function(t,e){var i=this;if(this.sourceBuffer_=this.mediaSource.addSourceBuffer(t),this.logger_("created SourceBuffer"),e&&(e.trigger("sourcebufferadded"),this.mediaSource.sourceBuffers.length<2))return void e.on("sourcebufferadded",function(){i.start_()});this.start_()},t.prototype.start_=function(){var t=this;this.started_=!0,this.onUpdateendCallback_=function(){var e=t.pendingCallback_;t.pendingCallback_=null,t.logger_("buffered ["+$u(t.buffered())+"]"),e&&e(),t.runCallback_()},this.sourceBuffer_.addEventListener("updateend",this.onUpdateendCallback_),this.runCallback_()},t.prototype.abort=function(t){var e=this;this.processedAppend_&&this.queueCallback_(function(){e.sourceBuffer_.abort()},t)},t.prototype.appendBuffer=function(t,e){var i=this;this.processedAppend_=!0,this.queueCallback_(function(){i.sourceBuffer_.appendBuffer(t)},e)},t.prototype.buffered=function(){return this.sourceBuffer_?this.sourceBuffer_.buffered:le.createTimeRanges()},t.prototype.remove=function(t,e){var i=this;this.processedAppend_&&this.queueCallback_(function(){i.logger_("remove ["+t+" => "+e+"]"),i.sourceBuffer_.remove(t,e)},pe)},t.prototype.updating=function(){return!this.sourceBuffer_||this.sourceBuffer_.updating||this.pendingCallback_},t.prototype.timestampOffset=function(t){var e=this;return void 0!==t&&(this.queueCallback_(function(){e.sourceBuffer_.timestampOffset=t}),this.timestampOffset_=t),this.timestampOffset_},t.prototype.queueCallback_=function(t,e){this.callbacks_.push([t.bind(this),e]),this.runCallback_()},t.prototype.runCallback_=function(){var t=void 0
;!this.updating()&&this.callbacks_.length&&this.started_&&(t=this.callbacks_.shift(),this.pendingCallback_=t[1],t[0]())},t.prototype.dispose=function(){this.sourceBuffer_.removeEventListener("updateend",this.onUpdateendCallback_),this.sourceBuffer_&&"open"===this.mediaSource.readyState&&this.sourceBuffer_.abort()},t}(),Ml={GOAL_BUFFER_LENGTH:30,MAX_GOAL_BUFFER_LENGTH:60,GOAL_BUFFER_LENGTH_RATE:1,BANDWIDTH_VARIANCE:1.2,BUFFER_LOW_WATER_LINE:0,MAX_BUFFER_LOW_WATER_LINE:30,BUFFER_LOW_WATER_LINE_RATE:1},Rl={FAILURE:2,TIMEOUT:-101,ABORTED:-102},Ul=function(t){var e=void 0;return e=t.offset+t.length-1,"bytes="+t.offset+"-"+e},Nl=function(t){var e={};return t.byterange&&(e.Range=Ul(t.byterange)),e},Bl=function(t){t.forEach(function(t){t.abort()})},jl=function(t){return{bandwidth:t.bandwidth,bytesReceived:t.bytesReceived||0,roundTripTime:t.roundTripTime||0}},Fl=function(t){var e=t.target,i=Date.now()-e.requestTime,r={bandwidth:1/0,bytesReceived:0,roundTripTime:i||0};return r.bytesReceived=t.loaded,r.bandwidth=Math.floor(r.bytesReceived/r.roundTripTime*8*1e3),r},Hl=function(t,e){return e.timedout?{status:e.status,message:"HLS request timed-out at URL: "+e.uri,code:Rl.TIMEOUT,xhr:e}:e.aborted?{status:e.status,message:"HLS request aborted at URL: "+e.uri,code:Rl.ABORTED,xhr:e}:t?{status:e.status,message:"HLS request errored at URL: "+e.uri,code:Rl.FAILURE,xhr:e}:null},Vl=function(t,e){return function(i,r){var n=r.response,s=Hl(i,r);if(s)return e(s,t);if(16!==n.byteLength)return e({status:r.status,message:"Invalid HLS key at URL: "+r.uri,code:Rl.FAILURE,xhr:r},t);var a=new DataView(n);return t.key.bytes=new Uint32Array([a.getUint32(0),a.getUint32(4),a.getUint32(8),a.getUint32(12)]),e(null,t)}},ql=function(t,e){return function(i,r){var n=r.response,s=Hl(i,r);return s?e(s,t):0===n.byteLength?e({status:r.status,message:"Empty HLS segment content at URL: "+r.uri,code:Rl.FAILURE,xhr:r},t):(t.map.bytes=new Uint8Array(r.response),e(null,t))}},Wl=function(t,e){return function(i,r){var n=r.response,s=Hl(i,r);return s?e(s,t):0===n.byteLength?e({status:r.status,message:"Empty HLS segment content at URL: "+r.uri,code:Rl.FAILURE,xhr:r},t):(t.stats=jl(r),t.key?t.encryptedBytes=new Uint8Array(r.response):t.bytes=new Uint8Array(r.response),e(null,t))}},Gl=function(t,e,i){var r=function r(n){if(n.data.source===e.requestId){t.removeEventListener("message",r);var s=n.data.decrypted;return e.bytes=new Uint8Array(s.bytes,s.byteOffset,s.byteLength),i(null,e)}};t.addEventListener("message",r),t.postMessage(ju({source:e.requestId,encrypted:e.encryptedBytes,key:e.key.bytes,iv:e.key.iv}),[e.encryptedBytes.buffer,e.key.bytes.buffer])},zl=function(t){return t.reduce(function(t,e){return e.code>t.code?e:t})},Xl=function(t,e,i){var r=[],n=0;return function(s,a){if(s&&(Bl(t),r.push(s)),(n+=1)===t.length){if(a.endOfAllRequests=Date.now(),r.length>0){var o=zl(r);return i(o,a)}return a.encryptedBytes?Gl(e,a,i):i(null,a)}}},Yl=function(t,e){return function(i){return t.stats=le.mergeOptions(t.stats,Fl(i)),!t.stats.firstBytesReceivedAt&&t.stats.bytesReceived&&(t.stats.firstBytesReceivedAt=Date.now()),e(i,t)}},$l=function(t,e,i,r,n,s){var a=[],o=Xl(a,i,s);if(r.key){var u=le.mergeOptions(e,{uri:r.key.resolvedUri,responseType:"arraybuffer"}),l=Vl(r,o),c=t(u,l);a.push(c)}if(r.map&&!r.map.bytes){var h=le.mergeOptions(e,{uri:r.map.resolvedUri,responseType:"arraybuffer",headers:Nl(r.map)}),d=ql(r,o),p=t(h,d);a.push(p)}var f=le.mergeOptions(e,{uri:r.resolvedUri,responseType:"arraybuffer",headers:Nl(r)}),m=Wl(r,o),g=t(f,m);return g.addEventListener("progress",Yl(r,n)),a.push(g),function(){return Bl(a)}},Kl={videoCodec:"avc1",videoObjectTypeIndicator:".4d400d",audioProfile:"2"},Jl=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e={codecCount:0},i=void 0;return e.codecCount=t.split(",").length,e.codecCount=e.codecCount||2,i=/(^|\s|,)+(avc[13])([^ ,]*)/i.exec(t),i&&(e.videoCodec=i[2],e.videoObjectTypeIndicator=i[3]),e.audioProfile=/(^|\s|,)+mp4a.[0-9A-Fa-f]+\.([0-9A-Fa-f]+)/i.exec(t),e.audioProfile=e.audioProfile&&e.audioProfile[2],e},Ql=function(t){return t.replace(/avc1\.(\d+)\.(\d+)/i,function(t){return gl([t])[0]})},Zl=function(t,e,i){return t+"/"+e+'; codecs="'+i.filter(function(t){return!!t}).join(", ")+'"'},tc=function(t){return t.segments&&t.segments.length&&t.segments[0].map?"mp4":"mp2t"},ec=function(t){var e=t.attributes||{};return e.CODECS?Jl(e.CODECS):Kl},ic=function(t,e){if(!t.mediaGroups.AUDIO||!e)return null;var i=t.mediaGroups.AUDIO[e];if(!i)return null;for(var r in i){var n=i[r];if(n.default&&n.playlists)return Jl(n.playlists[0].attributes.CODECS).audioProfile}return null},rc=function(t,e){var i=tc(e),r=ec(e),n=e.attributes||{},s=!0,a=!1;if(!e)return[];if(t.mediaGroups.AUDIO&&n.AUDIO){var o=t.mediaGroups.AUDIO[n.AUDIO];if(o){a=!0,s=!1;for(var u in o)if(!o[u].uri&&!o[u].playlists){s=!0;break}}}a&&!r.audioProfile&&(s||(r.audioProfile=ic(t,n.AUDIO)),r.audioProfile||(le.log.warn("Multiple audio tracks present but no audio codec string is specified. Attempting to use the default audio codec (mp4a.40.2)"),r.audioProfile=Kl.audioProfile));var l={};r.videoCodec&&(l.video=""+r.videoCodec+r.videoObjectTypeIndicator),r.audioProfile&&(l.audio="mp4a.40."+r.audioProfile);var c=Zl("audio",i,[l.audio]),h=Zl("video",i,[l.video]),d=Zl("video",i,[l.video,l.audio]);return a?!s&&l.video?[h,c]:s||l.video?[d,c]:[c,c]:l.video?[d]:[c]},nc=function(t,e){var i=void 0;return t?(i=ve.getComputedStyle(t),i?i[e]:""):""},sc=function(t,e){var i=t.slice();t.sort(function(t,r){var n=e(t,r);return 0===n?i.indexOf(t)-i.indexOf(r):n})},ac=function(t,e){var i=void 0,r=void 0;return t.attributes.BANDWIDTH&&(i=t.attributes.BANDWIDTH),i=i||ve.Number.MAX_VALUE,e.attributes.BANDWIDTH&&(r=e.attributes.BANDWIDTH),r=r||ve.Number.MAX_VALUE,i-r},oc=function(t,e){var i=void 0,r=void 0;return t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width&&(i=t.attributes.RESOLUTION.width),i=i||ve.Number.MAX_VALUE,e.attributes.RESOLUTION&&e.attributes.RESOLUTION.width&&(r=e.attributes.RESOLUTION.width),r=r||ve.Number.MAX_VALUE,i===r&&t.attributes.BANDWIDTH&&e.attributes.BANDWIDTH?t.attributes.BANDWIDTH-e.attributes.BANDWIDTH:i-r},uc=function(t,e,i,r){var n=t.playlists.map(function(t){var e=void 0,i=void 0,r=void 0;return e=t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width,i=t.attributes.RESOLUTION&&t.attributes.RESOLUTION.height,r=t.attributes.BANDWIDTH,r=r||ve.Number.MAX_VALUE,{bandwidth:r,width:e,height:i,playlist:t}});sc(n,function(t,e){return t.bandwidth-e.bandwidth}),n=n.filter(function(t){return!xu.isIncompatible(t.playlist)});var s=n.filter(function(t){return xu.isEnabled(t.playlist)});s.length||(s=n.filter(function(t){return!xu.isDisabled(t.playlist)}));var a=s.filter(function(t){return t.bandwidth*Ml.BANDWIDTH_VARIANCE<e}),o=a[a.length-1],u=a.filter(function(t){return t.bandwidth===o.bandwidth})[0],l=a.filter(function(t){return t.width&&t.height});sc(l,function(t,e){return t.width-e.width});var c=l.filter(function(t){return t.width===i&&t.height===r});o=c[c.length-1];var h=c.filter(function(t){return t.bandwidth===o.bandwidth})[0],d=void 0,p=void 0,f=void 0;h||(d=l.filter(function(t){return t.width>i||t.height>r}),p=d.filter(function(t){return t.width===d[0].width&&t.height===d[0].height}),o=p[p.length-1],f=p.filter(function(t){return t.bandwidth===o.bandwidth})[0]);var m=f||h||u||s[0]||n[0];return m?m.playlist:null},lc=function(){return uc(this.playlists.master,this.systemBandwidth,parseInt(nc(this.tech_.el(),"width"),10),parseInt(nc(this.tech_.el(),"height"),10))},cc=function(t){var e=t.master,i=t.currentTime,r=t.bandwidth,n=t.duration,s=t.segmentDuration,a=t.timeUntilRebuffer,o=t.currentTimeline,u=t.syncController,l=e.playlists.filter(function(t){return!xu.isIncompatible(t)}),c=l.filter(xu.isEnabled);c.length||(c=l.filter(function(t){return!xu.isDisabled(t)}));var h=c.filter(xu.hasAttribute.bind(null,"BANDWIDTH")),d=h.map(function(t){var e=u.getSyncPoint(t,n,o,i),l=e?1:2;return{playlist:t,rebufferingImpact:xu.estimateSegmentRequestTime(s,r,t)*l-a}}),p=d.filter(function(t){return t.rebufferingImpact<=0});return sc(p,function(t,e){return ac(e.playlist,t.playlist)}),p.length?p[0]:(sc(d,function(t,e){return t.rebufferingImpact-e.rebufferingImpact}),d[0]||null)},hc=function(){var t=this.playlists.master.playlists.filter(xu.isEnabled);return sc(t,function(t,e){return ac(t,e)}),t.filter(function(t){return Jl(t.attributes.CODECS).videoCodec})[0]||null},dc=function(t,e,i){if(!t||!e)return!1;var r=t.segments,n=i===r.length;return t.endList&&"open"===e.readyState&&n},pc=function(t){return"number"==typeof t&&isFinite(t)},fc=function(t,e,i){return"main"===t&&e&&i?i.containsAudio||i.containsVideo?e.containsVideo&&!i.containsVideo?"Only audio found in segment when we expected video. We can't switch to audio only from a stream that had video. To get rid of this message, please add codec information to the manifest.":!e.containsVideo&&i.containsVideo?"Video found in segment when we expected only audio. We can't switch to a stream with video from an audio only stream. To get rid of this message, please add codec information to the manifest.":null:"Neither audio nor video found in segment.":null},mc=function(t,e,i){var r=void 0;return r=t.length&&t.start(0)>0&&t.start(0)<e?t.start(0):e-30,Math.min(r,e-i)},gc=function(t){var e=t.segment,i=e.start,r=e.end,n=t.playlist,s=n.mediaSequence,a=n.id,o=n.segments,u=void 0===o?[]:o,l=t.mediaIndex,c=t.timeline;return["appending ["+l+"] of ["+s+", "+(s+u.length)+"] from playlist ["+a+"]","["+i+" => "+r+"] in timeline ["+c+"]"].join(" ")},yc=function(t){function e(i){arguments.length>1&&void 0!==arguments[1]&&arguments[1];Ie(this,e);var r=De(this,t.call(this));if(!i)throw new TypeError("Initialization settings are required");if("function"!=typeof i.currentTime)throw new TypeError("No currentTime getter specified");if(!i.mediaSource)throw new TypeError("No MediaSource specified");return r.bandwidth=i.bandwidth,r.throughput={rate:0,count:0},r.roundTrip=NaN,r.resetStats_(),r.mediaIndex=null,r.hasPlayed_=i.hasPlayed,r.currentTime_=i.currentTime,r.seekable_=i.seekable,r.seeking_=i.seeking,r.duration_=i.duration,r.mediaSource_=i.mediaSource,r.hls_=i.hls,r.loaderType_=i.loaderType,r.startingMedia_=void 0,r.segmentMetadataTrack_=i.segmentMetadataTrack,r.goalBufferLength_=i.goalBufferLength,r.sourceType_=i.sourceType,r.state_="INIT",r.checkBufferTimeout_=null,r.error_=void 0,r.currentTimeline_=-1,r.pendingSegment_=null,r.mimeType_=null,r.sourceUpdater_=null,r.xhrOptions_=null,r.activeInitSegmentId_=null,r.initSegments_={},r.decrypter_=i.decrypter,r.syncController_=i.syncController,r.syncPoint_={segmentIndex:0,time:0},r.syncController_.on("syncinfoupdate",function(){return r.trigger("syncinfoupdate")}),r.mediaSource_.addEventListener("sourceopen",function(){return r.ended_=!1}),r.fetchAtBuffer_=!1,r.logger_=xl("SegmentLoader["+r.loaderType_+"]"),Object.defineProperty(r,"state",{get:function(){return this.state_},set:function(t){t!==this.state_&&(this.logger_(this.state_+" -> "+t),this.state_=t)}}),r}return xe(e,t),e.prototype.resetStats_=function(){this.mediaBytesTransferred=0,this.mediaRequests=0,this.mediaRequestsAborted=0,this.mediaRequestsTimedout=0,this.mediaRequestsErrored=0,this.mediaTransferDuration=0,this.mediaSecondsLoaded=0},e.prototype.dispose=function(){this.state="DISPOSED",this.pause(),this.abort_(),this.sourceUpdater_&&this.sourceUpdater_.dispose(),this.resetStats_()},e.prototype.abort=function(){if("WAITING"!==this.state)return void(this.pendingSegment_&&(this.pendingSegment_=null));this.abort_(),this.state="READY",this.paused()||this.monitorBuffer_()},e.prototype.abort_=function(){this.pendingSegment_&&this.pendingSegment_.abortRequests(),this.pendingSegment_=null},e.prototype.error=function(t){return void 0!==t&&(this.error_=t),this.pendingSegment_=null,this.error_},e.prototype.endOfStream=function(){this.ended_=!0,this.pause(),this.trigger("ended")},e.prototype.buffered_=function(){return this.sourceUpdater_?this.sourceUpdater_.buffered():le.createTimeRanges()},e.prototype.initSegment=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t)return null;var i=Fu(t),r=this.initSegments_[i];return e&&!r&&t.bytes&&(this.initSegments_[i]=r={resolvedUri:t.resolvedUri,byterange:t.byterange,bytes:t.bytes}),r||t},e.prototype.couldBeginLoading_=function(){return this.playlist_&&(this.sourceUpdater_||this.mimeType_&&"INIT"===this.state)&&!this.paused()},e.prototype.load=function(){if(this.monitorBuffer_(),this.playlist_){if(this.syncController_.setDateTimeMapping(this.playlist_),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();!this.couldBeginLoading_()||"READY"!==this.state&&"INIT"!==this.state||(this.state="READY")}},e.prototype.init_=function(){return this.state="READY",this.sourceUpdater_=new Dl(this.mediaSource_,this.mimeType_,this.loaderType_,this.sourceBufferEmitter_),this.resetEverything(),this.monitorBuffer_()},e.prototype.playlist=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t){var i=this.playlist_,r=this.pendingSegment_;this.playlist_=t,this.xhrOptions_=e,this.hasPlayed_()||(t.syncInfo={mediaSequence:t.mediaSequence,time:0});var n=i?i.id:null;if(this.logger_("playlist update ["+n+" => "+t.id+"]"),this.trigger("syncinfoupdate"),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();if(!i||i.uri!==t.uri)return void(null!==this.mediaIndex&&this.resyncLoader());var s=t.mediaSequence-i.mediaSequence;this.logger_("live window shift ["+s+"]"),null!==this.mediaIndex&&(this.mediaIndex-=s),r&&(r.mediaIndex-=s,r.mediaIndex>=0&&(r.segment=t.segments[r.mediaIndex])),this.syncController_.saveExpiredSegmentInfo(i,t)}},e.prototype.pause=function(){this.checkBufferTimeout_&&(ve.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=null)},e.prototype.paused=function(){return null===this.checkBufferTimeout_},e.prototype.mimeType=function(t,e){this.mimeType_||(this.mimeType_=t,this.sourceBufferEmitter_=e,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_())},e.prototype.resetEverything=function(){this.ended_=!1,this.resetLoader(),this.remove(0,this.duration_()),this.trigger("reseteverything")},e.prototype.resetLoader=function(){this.fetchAtBuffer_=!1,this.resyncLoader()},e.prototype.resyncLoader=function(){this.mediaIndex=null,this.syncPoint_=null,this.abort()},e.prototype.remove=function(t,e){this.sourceUpdater_&&this.sourceUpdater_.remove(t,e),Zu(t,e,this.segmentMetadataTrack_)},e.prototype.monitorBuffer_=function(){this.checkBufferTimeout_&&ve.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=ve.setTimeout(this.monitorBufferTick_.bind(this),1)},e.prototype.monitorBufferTick_=function(){"READY"===this.state&&this.fillBuffer_(),this.checkBufferTimeout_&&ve.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=ve.setTimeout(this.monitorBufferTick_.bind(this),500)},e.prototype.fillBuffer_=function(){if(!this.sourceUpdater_.updating()){this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var t=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(t){if(dc(this.playlist_,this.mediaSource_,t.mediaIndex))return void this.endOfStream();(t.mediaIndex!==this.playlist_.segments.length-1||"ended"!==this.mediaSource_.readyState||this.seeking_())&&((t.timeline!==this.currentTimeline_||null!==t.startOfSegment&&t.startOfSegment<this.sourceUpdater_.timestampOffset())&&(this.syncController_.reset(),t.timestampOffset=t.startOfSegment),this.loadSegment_(t))}}},e.prototype.checkBuffer_=function(t,e,i,r,n,s){var a=0,o=void 0;t.length&&(a=t.end(t.length-1));var u=Math.max(0,a-n);if(!e.segments.length)return null;if(u>=this.goalBufferLength_())return null;if(!r&&u>=1)return null;if(null===s)return i=this.getSyncSegmentCandidate_(e),this.generateSegmentInfo_(e,i,null,!0);if(null!==i){var l=e.segments[i];return o=l&&l.end?l.end:a,this.generateSegmentInfo_(e,i+1,o,!1)}if(this.fetchAtBuffer_){var c=xu.getMediaInfoForTime(e,a,s.segmentIndex,s.time);i=c.mediaIndex,o=c.startTime}else{var h=xu.getMediaInfoForTime(e,n,s.segmentIndex,s.time);i=h.mediaIndex,o=h.startTime}return this.generateSegmentInfo_(e,i,o,!1)},e.prototype.getSyncSegmentCandidate_=function(t){var e=this;if(-1===this.currentTimeline_)return 0;var i=t.segments.map(function(t,e){return{timeline:t.timeline,segmentIndex:e}}).filter(function(t){return t.timeline===e.currentTimeline_});return i.length?i[Math.min(i.length-1,1)].segmentIndex:Math.max(t.segments.length-1,0)},e.prototype.generateSegmentInfo_=function(t,e,i,r){if(e<0||e>=t.segments.length)return null;var n=t.segments[e];return{requestId:"segment-loader-"+Math.random(),uri:n.resolvedUri,mediaIndex:e,isSyncRequest:r,startOfSegment:i,playlist:t,bytes:null,encryptedBytes:null,timestampOffset:null,timeline:n.timeline,duration:n.duration,segment:n}},e.prototype.abortRequestEarly_=function(t){if(this.hls_.tech_.paused()||!this.xhrOptions_.timeout||!this.playlist_.attributes.BANDWIDTH)return!1;if(Date.now()-(t.firstBytesReceivedAt||Date.now())<1e3)return!1;var e=this.currentTime_(),i=t.bandwidth,r=this.pendingSegment_.duration,n=xu.estimateSegmentRequestTime(r,i,this.playlist_,t.bytesReceived),s=Ku(this.buffered_(),e,this.hls_.tech_.playbackRate())-1;if(n<=s)return!1;var a=cc({master:this.hls_.playlists.master,currentTime:e,bandwidth:i,duration:this.duration_(),segmentDuration:r,timeUntilRebuffer:s,currentTimeline:this.currentTimeline_,syncController:this.syncController_});if(a){var o=n-s,u=o-a.rebufferingImpact,l=.5;return s<=1/30&&(l=1),!a.playlist||a.playlist.uri===this.playlist_.uri||u<l?!1:(this.bandwidth=a.playlist.attributes.BANDWIDTH*Ml.BANDWIDTH_VARIANCE+1,this.abort(),this.trigger("earlyabort"),!0)}},e.prototype.handleProgress_=function(t,e){this.pendingSegment_&&e.requestId===this.pendingSegment_.requestId&&!this.abortRequestEarly_(e.stats)&&this.trigger("progress")},e.prototype.loadSegment_=function(t){this.state="WAITING",this.pendingSegment_=t,this.trimBackBuffer_(t),t.abortRequests=$l(this.hls_.xhr,this.xhrOptions_,this.decrypter_,this.createSimplifiedSegmentObj_(t),this.handleProgress_.bind(this),this.segmentRequestFinished_.bind(this))},e.prototype.trimBackBuffer_=function(t){var e=mc(this.seekable_(),this.currentTime_(),this.playlist_.targetDuration||10);e>0&&this.remove(0,e)},e.prototype.createSimplifiedSegmentObj_=function(t){var e=t.segment,i={resolvedUri:e.resolvedUri,byterange:e.byterange,requestId:t.requestId};if(e.key){var r=e.key.iv||new Uint32Array([0,0,0,t.mediaIndex+t.playlist.mediaSequence]);i.key={resolvedUri:e.key.resolvedUri,iv:r}}return e.map&&(i.map=this.initSegment(e.map)),i},e.prototype.segmentRequestFinished_=function(t,e){if(this.mediaRequests+=1,e.stats&&(this.mediaBytesTransferred+=e.stats.bytesReceived,this.mediaTransferDuration+=e.stats.roundTripTime),!this.pendingSegment_)return void(this.mediaRequestsAborted+=1);if(e.requestId===this.pendingSegment_.requestId){if(t)return this.pendingSegment_=null,this.state="READY",t.code===Rl.ABORTED?void(this.mediaRequestsAborted+=1):(this.pause(),t.code===Rl.TIMEOUT?(this.mediaRequestsTimedout+=1,this.bandwidth=1,this.roundTrip=NaN,void this.trigger("bandwidthupdate")):(this.mediaRequestsErrored+=1,this.error(t),void this.trigger("error")));this.bandwidth=e.stats.bandwidth,this.roundTrip=e.stats.roundTripTime,e.map&&(e.map=this.initSegment(e.map,!0)),this.processSegmentResponse_(e)}},e.prototype.processSegmentResponse_=function(t){var e=this.pendingSegment_;e.bytes=t.bytes,t.map&&(e.segment.map.bytes=t.map.bytes),e.endOfAllRequests=t.endOfAllRequests,this.handleSegment_()},e.prototype.handleSegment_=function(){var t=this;if(!this.pendingSegment_)return void(this.state="READY");var e=this.pendingSegment_,i=e.segment,r=this.syncController_.probeSegmentInfo(e);void 0===this.startingMedia_&&r&&(r.containsAudio||r.containsVideo)&&(this.startingMedia_={containsAudio:r.containsAudio,containsVideo:r.containsVideo});var n=fc(this.loaderType_,this.startingMedia_,r);if(n)return this.error({message:n,blacklistDuration:1/0}),void this.trigger("error");if(e.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");null!==e.timestampOffset&&e.timestampOffset!==this.sourceUpdater_.timestampOffset()&&(this.sourceUpdater_.timestampOffset(e.timestampOffset),this.trigger("timestampoffset"));var s=this.syncController_.mappingForTimeline(e.timeline);if(null!==s&&this.trigger({type:"segmenttimemapping",mapping:s}),this.state="APPENDING",i.map){var a=Fu(i.map);if(!this.activeInitSegmentId_||this.activeInitSegmentId_!==a){var o=this.initSegment(i.map);this.sourceUpdater_.appendBuffer(o.bytes,function(){t.activeInitSegmentId_=a})}}e.byteLength=e.bytes.byteLength,"number"==typeof i.start&&"number"==typeof i.end?this.mediaSecondsLoaded+=i.end-i.start:this.mediaSecondsLoaded+=i.duration,this.logger_(gc(e)),this.sourceUpdater_.appendBuffer(e.bytes,this.handleUpdateEnd_.bind(this))},e.prototype.handleUpdateEnd_=function(){if(!this.pendingSegment_)return this.state="READY",void(this.paused()||this.monitorBuffer_());var t=this.pendingSegment_,e=t.segment,i=null!==this.mediaIndex;if(this.pendingSegment_=null,this.recordThroughput_(t),this.addSegmentMetadataCue_(t),this.state="READY",this.mediaIndex=t.mediaIndex,this.fetchAtBuffer_=!0,this.currentTimeline_=t.timeline,this.trigger("syncinfoupdate"),e.end&&this.currentTime_()-e.end>3*t.playlist.targetDuration)return void this.resetEverything();i&&this.trigger("bandwidthupdate"),this.trigger("progress"),dc(t.playlist,this.mediaSource_,t.mediaIndex+1)&&this.endOfStream(),this.paused()||this.monitorBuffer_()},e.prototype.recordThroughput_=function(t){var e=this.throughput.rate,i=Date.now()-t.endOfAllRequests+1,r=Math.floor(t.byteLength/i*8*1e3);this.throughput.rate+=(r-e)/++this.throughput.count},e.prototype.addSegmentMetadataCue_=function(t){if(this.segmentMetadataTrack_){var e=t.segment,i=e.start,r=e.end;if(pc(i)&&pc(r)){Zu(i,r,this.segmentMetadataTrack_);var n=ve.WebKitDataCue||ve.VTTCue,s={uri:t.uri,timeline:t.timeline,playlist:t.playlist.uri,start:i,end:r},a=JSON.stringify(s),o=new n(i,r,a);o.value=s,this.segmentMetadataTrack_.addCue(o)}}},e}(le.EventTarget),vc=new Uint8Array("\n\n".split("").map(function(t){return t.charCodeAt(0)})),_c=function(t){return String.fromCharCode.apply(null,t)},bc=function(t){function e(i){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ie(this,e);var n=De(this,t.call(this,i,r));return n.mediaSource_=null,n.subtitlesTrack_=null,n}return xe(e,t),e.prototype.buffered_=function(){if(!this.subtitlesTrack_||!this.subtitlesTrack_.cues.length)return le.createTimeRanges();var t=this.subtitlesTrack_.cues,e=t[0].startTime,i=t[t.length-1].startTime;return le.createTimeRanges([[e,i]])},e.prototype.initSegment=function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t)return null;var i=Fu(t),r=this.initSegments_[i];if(e&&!r&&t.bytes){var n=vc.byteLength+t.bytes.byteLength,s=new Uint8Array(n);s.set(t.bytes),s.set(vc,t.bytes.byteLength),this.initSegments_[i]=r={resolvedUri:t.resolvedUri,byterange:t.byterange,bytes:s}}return r||t},e.prototype.couldBeginLoading_=function(){return this.playlist_&&this.subtitlesTrack_&&!this.paused()},e.prototype.init_=function(){return this.state="READY",this.resetEverything(),this.monitorBuffer_()},e.prototype.track=function(t){return void 0===t?this.subtitlesTrack_:(this.subtitlesTrack_=t,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_(),this.subtitlesTrack_)},e.prototype.remove=function(t,e){Zu(t,e,this.subtitlesTrack_)},e.prototype.fillBuffer_=function(){var t=this;this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var e=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(e=this.skipEmptySegments_(e)){if(null===this.syncController_.timestampOffsetForTimeline(e.timeline)){var i=function(){t.state="READY",t.paused()||t.monitorBuffer_()};return this.syncController_.one("timestampoffset",i),void(this.state="WAITING_ON_TIMELINE")}this.loadSegment_(e)}},e.prototype.skipEmptySegments_=function(t){for(;t&&t.segment.empty;)t=this.generateSegmentInfo_(t.playlist,t.mediaIndex+1,t.startOfSegment+t.duration,t.isSyncRequest);return t},e.prototype.handleSegment_=function(){var t=this;if(!this.pendingSegment_||!this.subtitlesTrack_)return void(this.state="READY");this.state="APPENDING";var e=this.pendingSegment_,i=e.segment;if("function"!=typeof ve.WebVTT&&this.subtitlesTrack_&&this.subtitlesTrack_.tech_){var r=function(){t.handleSegment_()};return this.state="WAITING_ON_VTTJS",this.subtitlesTrack_.tech_.one("vttjsloaded",r),void this.subtitlesTrack_.tech_.one("vttjserror",function(){t.subtitlesTrack_.tech_.off("vttjsloaded",r),t.error({message:"Error loading vtt.js"}),t.state="READY",t.pause(),t.trigger("error")})}i.requested=!0;try{this.parseVTTCues_(e)}catch(t){return this.error({message:t.message}),this.state="READY",this.pause(),this.trigger("error")}if(this.updateTimeMapping_(e,this.syncController_.timelines[e.timeline],this.playlist_),e.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");e.byteLength=e.bytes.byteLength,this.mediaSecondsLoaded+=i.duration,e.cues.length&&this.remove(e.cues[0].endTime,e.cues[e.cues.length-1].endTime),e.cues.forEach(function(e){t.subtitlesTrack_.addCue(e)}),this.handleUpdateEnd_()},e.prototype.parseVTTCues_=function(t){var e=void 0,i=!1;"function"==typeof ve.TextDecoder?e=new ve.TextDecoder("utf8"):(e=ve.WebVTT.StringDecoder(),i=!0);var r=new ve.WebVTT.Parser(ve,ve.vttjs,e);if(t.cues=[],t.timestampmap={MPEGTS:0,LOCAL:0},r.oncue=t.cues.push.bind(t.cues),r.ontimestampmap=function(e){return t.timestampmap=e},r.onparsingerror=function(t){le.log.warn("Error encountered when parsing cues: "+t.message)},t.segment.map){var n=t.segment.map.bytes;i&&(n=_c(n)),r.parse(n)}var s=t.bytes;i&&(s=_c(s)),r.parse(s),r.flush()},e.prototype.updateTimeMapping_=function(t,e,i){var r=t.segment;if(e){if(!t.cues.length)return void(r.empty=!0);var n=t.timestampmap,s=n.MPEGTS/9e4-n.LOCAL+e.mapping;if(t.cues.forEach(function(t){t.startTime+=s,t.endTime+=s}),!i.syncInfo){var a=t.cues[0].startTime,o=t.cues[t.cues.length-1].startTime;i.syncInfo={mediaSequence:i.mediaSequence+t.mediaIndex,time:Math.min(a,o-r.duration)}}}},e}(yc),Tc=function(t,e){for(var i=t.cues,r=0;r<i.length;r++){var n=i[r];if(e>=n.adStartTime&&e<=n.adEndTime)return n}return null},Sc=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(t.segments)for(var r=i,n=void 0,s=0;s<t.segments.length;s++){var a=t.segments[s];if(n||(n=Tc(e,r+a.duration/2)),n){if("cueIn"in a){n.endTime=r,n.adEndTime=r,r+=a.duration,n=null;continue}if(r<n.endTime){r+=a.duration;continue}n.endTime+=a.duration}else if("cueOut"in a&&(n=new ve.VTTCue(r,r+a.duration,a.cueOut),n.adStartTime=r,n.adEndTime=r+parseFloat(a.cueOut),e.addCue(n)),"cueOutCont"in a){var o=void 0,u=void 0,l=a.cueOutCont.split("/").map(parseFloat);o=l[0],u=l[1],n=new ve.VTTCue(r,r+a.duration,""),n.adStartTime=r-o,n.adEndTime=n.adStartTime+u,e.addCue(n)}r+=a.duration}},kc=jo.inspect,Ec=[{name:"VOD",run:function(t,e,i,r,n){if(i!==1/0){return{time:0,segmentIndex:0}}return null}},{name:"ProgramDateTime",run:function(t,e,i,r,n){if(!t.datetimeToDisplayTime)return null;var s=e.segments||[],a=null,o=null;n=n||0;for(var u=0;u<s.length;u++){var l=s[u];if(l.dateTimeObject){var c=l.dateTimeObject.getTime()/1e3,h=c+t.datetimeToDisplayTime,d=Math.abs(n-h);if(null!==o&&o<d)break;o=d,a={time:h,segmentIndex:u}}}return a}},{name:"Segment",run:function(t,e,i,r,n){var s=e.segments||[],a=null,o=null;n=n||0;for(var u=0;u<s.length;u++){var l=s[u];if(l.timeline===r&&void 0!==l.start){var c=Math.abs(n-l.start);if(null!==o&&o<c)break;(!a||null===o||o>=c)&&(o=c,a={time:l.start,segmentIndex:u})}}return a}},{name:"Discontinuity",run:function(t,e,i,r,n){var s=null;if(n=n||0,e.discontinuityStarts&&e.discontinuityStarts.length)for(var a=null,o=0;o<e.discontinuityStarts.length;o++){var u=e.discontinuityStarts[o],l=e.discontinuitySequence+o+1,c=t.discontinuities[l];if(c){var h=Math.abs(n-c.time);if(null!==a&&a<h)break;(!s||null===a||a>=h)&&(a=h,s={time:c.time,segmentIndex:u})}}return s}},{name:"Playlist",run:function(t,e,i,r,n){if(e.syncInfo){return{time:e.syncInfo.time,segmentIndex:e.syncInfo.mediaSequence-e.mediaSequence}}return null}}],Cc=function(t){function e(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];Ie(this,e);var i=De(this,t.call(this));return i.inspectCache_=void 0,i.timelines=[],i.discontinuities=[],i.datetimeToDisplayTime=null,i.logger_=xl("SyncController"),i}return xe(e,t),e.prototype.getSyncPoint=function(t,e,i,r){var n=this.runStrategies_(t,e,i,r);return n.length?this.selectSyncPoint_(n,{key:"time",value:r}):null},e.prototype.getExpiredTime=function(t,e){if(!t||!t.segments)return null;var i=this.runStrategies_(t,e,t.discontinuitySequence,0);if(!i.length)return null;var r=this.selectSyncPoint_(i,{key:"segmentIndex",value:0});return r.segmentIndex>0&&(r.time*=-1),Math.abs(r.time+fu(t,r.segmentIndex,0))},e.prototype.runStrategies_=function(t,e,i,r){for(var n=[],s=0;s<Ec.length;s++){var a=Ec[s],o=a.run(this,t,e,i,r);o&&(o.strategy=a.name,n.push({strategy:a.name,syncPoint:o}))}return n},e.prototype.selectSyncPoint_=function(t,e){for(var i=t[0].syncPoint,r=Math.abs(t[0].syncPoint[e.key]-e.value),n=t[0].strategy,s=1;s<t.length;s++){var a=Math.abs(t[s].syncPoint[e.key]-e.value);a<r&&(r=a,i=t[s].syncPoint,n=t[s].strategy)}return this.logger_("syncPoint for ["+e.key+": "+e.value+"] chosen with strategy ["+n+"]: [time:"+i.time+", segmentIndex:"+i.segmentIndex+"]"),i},e.prototype.saveExpiredSegmentInfo=function(t,e){for(var i=e.mediaSequence-t.mediaSequence,r=i-1;r>=0;r--){var n=t.segments[r];if(n&&void 0!==n.start){e.syncInfo={mediaSequence:t.mediaSequence+r,time:n.start},this.logger_("playlist refresh sync: [time:"+e.syncInfo.time+", mediaSequence: "+e.syncInfo.mediaSequence+"]"),this.trigger("syncinfoupdate");break}}},e.prototype.setDateTimeMapping=function(t){if(!this.datetimeToDisplayTime&&t.segments&&t.segments.length&&t.segments[0].dateTimeObject){var e=t.segments[0].dateTimeObject.getTime()/1e3;this.datetimeToDisplayTime=-e}},e.prototype.reset=function(){this.inspectCache_=void 0},e.prototype.probeSegmentInfo=function(t){var e=t.segment,i=t.playlist,r=void 0;return r=e.map?this.probeMp4Segment_(t):this.probeTsSegment_(t),r&&this.calculateSegmentTimeMapping_(t,r)&&(this.saveDiscontinuitySyncInfo_(t),i.syncInfo||(i.syncInfo={mediaSequence:i.mediaSequence+t.mediaIndex,time:e.start})),r},e.prototype.probeMp4Segment_=function(t){var e=t.segment,i=Za.timescale(e.map.bytes),r=Za.startTime(i,t.bytes);return null!==t.timestampOffset&&(t.timestampOffset-=r),{start:r,end:r+e.duration}},e.prototype.probeTsSegment_=function(t){var e=kc(t.bytes,this.inspectCache_),i=void 0,r=void 0;return e?(e.video&&2===e.video.length?(this.inspectCache_=e.video[1].dts,i=e.video[0].dtsTime,r=e.video[1].dtsTime):e.audio&&2===e.audio.length&&(this.inspectCache_=e.audio[1].dts,i=e.audio[0].dtsTime,r=e.audio[1].dtsTime),{start:i,end:r,containsVideo:e.video&&2===e.video.length,containsAudio:e.audio&&2===e.audio.length}):null},e.prototype.timestampOffsetForTimeline=function(t){return void 0===this.timelines[t]?null:this.timelines[t].time},e.prototype.mappingForTimeline=function(t){return void 0===this.timelines[t]?null:this.timelines[t].mapping},e.prototype.calculateSegmentTimeMapping_=function(t,e){var i=t.segment,r=this.timelines[t.timeline];if(null!==t.timestampOffset)r={time:t.startOfSegment,mapping:t.startOfSegment-e.start},this.timelines[t.timeline]=r,this.trigger("timestampoffset"),
this.logger_("time mapping for timeline "+t.timeline+": [time: "+r.time+"] [mapping: "+r.mapping+"]"),i.start=t.startOfSegment,i.end=e.end+r.mapping;else{if(!r)return!1;i.start=e.start+r.mapping,i.end=e.end+r.mapping}return!0},e.prototype.saveDiscontinuitySyncInfo_=function(t){var e=t.playlist,i=t.segment;if(i.discontinuity)this.discontinuities[i.timeline]={time:i.start,accuracy:0};else if(e.discontinuityStarts&&e.discontinuityStarts.length)for(var r=0;r<e.discontinuityStarts.length;r++){var n=e.discontinuityStarts[r],s=e.discontinuitySequence+r+1,a=n-t.mediaIndex,o=Math.abs(a);if(!this.discontinuities[s]||this.discontinuities[s].accuracy>o){var u=void 0;u=a<0?i.start-fu(e,t.mediaIndex,n):i.end+fu(e,t.mediaIndex+1,n),this.discontinuities[s]={time:u,accuracy:o}}}},e}(le.EventTarget),wc=new ce("./decrypter-worker.worker.js",function(t,e){var i=this;!function(){function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function r(t,e){return e={exports:{}},t(e,e.exports),e.exports}var n,s=void 0!==t?t:"undefined"!=typeof global?global:void 0!==i?i:{};n=void 0!==t?t:void 0!==s?s:void 0!==i?i:{};var a=n,o=r(function(t,e){function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=function(){var t=[[[],[],[],[],[]],[[],[],[],[],[]]],e=t[0],i=t[1],r=e[4],n=i[4],s=void 0,a=void 0,o=void 0,u=[],l=[],c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0;for(s=0;s<256;s++)l[(u[s]=s<<1^283*(s>>7))^s]=s;for(a=o=0;!r[a];a^=c||1,o=l[o]||1)for(p=o^o<<1^o<<2^o<<3^o<<4,p=p>>8^255&p^99,r[a]=p,n[p]=a,d=u[h=u[c=u[a]]],m=16843009*d^65537*h^257*c^16843008*a,f=257*u[p]^16843008*p,s=0;s<4;s++)e[s][a]=f=f<<24^f>>>8,i[s][p]=m=m<<24^m>>>8;for(s=0;s<5;s++)e[s]=e[s].slice(0),i[s]=i[s].slice(0);return t},s=null,a=function(){function t(e){i(this,t),s||(s=n()),this._tables=[[s[0][0].slice(),s[0][1].slice(),s[0][2].slice(),s[0][3].slice(),s[0][4].slice()],[s[1][0].slice(),s[1][1].slice(),s[1][2].slice(),s[1][3].slice(),s[1][4].slice()]];var r=void 0,a=void 0,o=void 0,u=void 0,l=void 0,c=this._tables[0][4],h=this._tables[1],d=e.length,p=1;if(4!==d&&6!==d&&8!==d)throw new Error("Invalid aes key size");for(u=e.slice(0),l=[],this._key=[u,l],r=d;r<4*d+28;r++)o=u[r-1],(r%d==0||8===d&&r%d==4)&&(o=c[o>>>24]<<24^c[o>>16&255]<<16^c[o>>8&255]<<8^c[255&o],r%d==0&&(o=o<<8^o>>>24^p<<24,p=p<<1^283*(p>>7))),u[r]=u[r-d]^o;for(a=0;r;a++,r--)o=u[3&a?r:r-4],l[a]=r<=4||a<4?o:h[0][c[o>>>24]]^h[1][c[o>>16&255]]^h[2][c[o>>8&255]]^h[3][c[255&o]]}return r(t,[{key:"decrypt",value:function(t,e,i,r,n,s){var a=this._key[1],o=t^a[0],u=r^a[1],l=i^a[2],c=e^a[3],h=void 0,d=void 0,p=void 0,f=a.length/4-2,m=void 0,g=4,y=this._tables[1],v=y[0],_=y[1],b=y[2],T=y[3],S=y[4];for(m=0;m<f;m++)h=v[o>>>24]^_[u>>16&255]^b[l>>8&255]^T[255&c]^a[g],d=v[u>>>24]^_[l>>16&255]^b[c>>8&255]^T[255&o]^a[g+1],p=v[l>>>24]^_[c>>16&255]^b[o>>8&255]^T[255&u]^a[g+2],c=v[c>>>24]^_[o>>16&255]^b[u>>8&255]^T[255&l]^a[g+3],g+=4,o=h,u=d,l=p;for(m=0;m<4;m++)n[(3&-m)+s]=S[o>>>24]<<24^S[u>>16&255]<<16^S[l>>8&255]<<8^S[255&c]^a[g++],h=o,o=u,u=l,l=c,c=h}}]),t}();e.default=a,t.exports=e.default});e(o);var u=r(function(t,e){function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=function(){function t(){i(this,t),this.listeners={}}return r(t,[{key:"on",value:function(t,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(e)}},{key:"off",value:function(t,e){var i=void 0;return!!this.listeners[t]&&(i=this.listeners[t].indexOf(e),this.listeners[t].splice(i,1),i>-1)}},{key:"trigger",value:function(t){var e=void 0,i=void 0,r=void 0,n=void 0;if(e=this.listeners[t])if(2===arguments.length)for(r=e.length,i=0;i<r;++i)e[i].call(this,arguments[1]);else for(n=Array.prototype.slice.call(arguments,1),r=e.length,i=0;i<r;++i)e[i].apply(this,n)}},{key:"dispose",value:function(){this.listeners={}}},{key:"pipe",value:function(t){this.on("data",function(e){t.push(e)})}}]),t}();e.default=n,t.exports=e.default});e(u);var l=r(function(t,e){function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":Pe(e)));t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),s=function(t,e,i){for(var r=!0;r;){var n=t,s=e,a=i;r=!1,null===n&&(n=Function.prototype);var o=Object.getOwnPropertyDescriptor(n,s);if(void 0!==o){if("value"in o)return o.value;var u=o.get;if(void 0===u)return;return u.call(a)}var l=Object.getPrototypeOf(n);if(null===l)return;t=l,e=s,i=a,r=!0,o=l=void 0}},a=function(t){return t&&t.__esModule?t:{default:t}}(u),o=function(t){function e(){i(this,e),s(Object.getPrototypeOf(e.prototype),"constructor",this).call(this,a.default),this.jobs=[],this.delay=1,this.timeout_=null}return r(e,t),n(e,[{key:"processJob_",value:function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null}},{key:"push",value:function(t){this.jobs.push(t),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))}}]),e}(a.default);e.default=o,t.exports=e.default});e(l);var c,h=function(t){var e=c[t.byteLength%16||0],i=new Uint8Array(t.byteLength+e.length);return i.set(t),i.set(e,t.byteLength),i};c=[[16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16],[15,15,15,15,15,15,15,15,15,15,15,15,15,15,15],[14,14,14,14,14,14,14,14,14,14,14,14,14,14],[13,13,13,13,13,13,13,13,13,13,13,13,13],[12,12,12,12,12,12,12,12,12,12,12,12],[11,11,11,11,11,11,11,11,11,11,11],[10,10,10,10,10,10,10,10,10,10],[9,9,9,9,9,9,9,9,9],[8,8,8,8,8,8,8,8],[7,7,7,7,7,7,7],[6,6,6,6,6,6],[5,5,5,5,5],[4,4,4,4],[3,3,3],[2,2],[1]];var d=function(t){return t.subarray(0,t.byteLength-t[t.byteLength-1])},p=h,f=d,m={pad:p,unpad:f},g=r(function(t,e){function i(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),s=i(o),a=i(l),u=function(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24},c=function(t,e,i){var r=new Int32Array(t.buffer,t.byteOffset,t.byteLength>>2),n=new s.default(Array.prototype.slice.call(e)),a=new Uint8Array(t.byteLength),o=new Int32Array(a.buffer),l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0,g=void 0,y=void 0;for(l=i[0],c=i[1],h=i[2],d=i[3],y=0;y<r.length;y+=4)p=u(r[y]),f=u(r[y+1]),m=u(r[y+2]),g=u(r[y+3]),n.decrypt(p,f,m,g,o,y),o[y]=u(o[y]^l),o[y+1]=u(o[y+1]^c),o[y+2]=u(o[y+2]^h),o[y+3]=u(o[y+3]^d),l=p,c=f,h=m,d=g;return a};e.decrypt=c;var h=function(){function t(e,i,n,s){r(this,t);var o=t.STEP,l=new Int32Array(e.buffer),c=new Uint8Array(e.byteLength),h=0;for(this.asyncStream_=new a.default,this.asyncStream_.push(this.decryptChunk_(l.subarray(h,h+o),i,n,c)),h=o;h<l.length;h+=o)n=new Uint32Array([u(l[h-4]),u(l[h-3]),u(l[h-2]),u(l[h-1])]),this.asyncStream_.push(this.decryptChunk_(l.subarray(h,h+o),i,n,c));this.asyncStream_.push(function(){s(null,(0,m.unpad)(c))})}return n(t,[{key:"decryptChunk_",value:function(t,e,i,r){return function(){var n=c(t,e,i);r.set(n,t.byteOffset)}}}],[{key:"STEP",get:function(){return 32e3}}]),t}();e.Decrypter=h,e.default={Decrypter:h,decrypt:c}});e(g);var y=(g.decrypt,g.Decrypter,r(function(t,e){Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){return t&&t.__esModule?t:{default:t}}(l);e.default={decrypt:g.decrypt,Decrypter:g.Decrypter,AsyncStream:i.default},t.exports=e.default})),v=e(y),_=function(t){var e={};return Object.keys(t).forEach(function(i){var r=t[i];ArrayBuffer.isView(r)?e[i]={bytes:r.buffer,byteOffset:r.byteOffset,byteLength:r.byteLength}:e[i]=r}),e},b=v.Decrypter;new function(t){t.onmessage=function(t){var e=t.data,i=new Uint8Array(e.encrypted.bytes,e.encrypted.byteOffset,e.encrypted.byteLength),r=new Uint32Array(e.key.bytes,e.key.byteOffset,e.key.byteLength/4),n=new Uint32Array(e.iv.bytes,e.iv.byteOffset,e.iv.byteLength/4);new b(i,r,n,function(t,i){a.postMessage(_({source:e.source,decrypted:i}),[i.buffer])})}}(i)}()}),Ac=function(t){var e=t.default?"main":"alternative";return t.characteristics&&t.characteristics.indexOf("public.accessibility.describes-video")>=0&&(e="main-desc"),e},Lc=function(t,e){t.abort(),t.pause(),e&&e.activePlaylistLoader&&(e.activePlaylistLoader.pause(),e.activePlaylistLoader=null)},Oc=function(t,e){e.activePlaylistLoader=t,t.load()},Pc=function(t,e){return function(){var i=e.segmentLoaders,r=i[t],n=i.main,s=e.mediaTypes[t],a=s.activeTrack(),o=s.activeGroup(a),u=s.activePlaylistLoader;if(Lc(r,s),o){if(!o.playlistLoader)return void(u&&n.resetEverything());r.resyncLoader(),Oc(o.playlistLoader,s)}}},Ic=function(t,e){return function(){var i=e.segmentLoaders,r=i[t],n=i.main,s=e.mediaTypes[t],a=s.activeTrack(),o=s.activeGroup(a),u=s.activePlaylistLoader;if(Lc(r,s),o){if(!o.playlistLoader)return void n.resetEverything();if(u===o.playlistLoader)return void Oc(o.playlistLoader,s);r.track&&r.track(a),r.resetEverything(),Oc(o.playlistLoader,s)}}},xc={AUDIO:function(t,e){return function(){var i=e.segmentLoaders[t],r=e.mediaTypes[t],n=e.blacklistCurrentPlaylist;Lc(i,r);var s=r.activeTrack(),a=r.activeGroup(),o=(a.filter(function(t){return t.default})[0]||a[0]).id,u=r.tracks[o];if(s===u)return void n({message:"Problem encountered loading the default audio track."});le.log.warn("Problem encountered loading the alternate audio track.Switching back to default.");for(var l in r.tracks)r.tracks[l].enabled=r.tracks[l]===u;r.onTrackChanged()}},SUBTITLES:function(t,e){return function(){var i=e.segmentLoaders[t],r=e.mediaTypes[t];le.log.warn("Problem encountered loading the subtitle track.Disabling subtitle track."),Lc(i,r);var n=r.activeTrack();n&&(n.mode="disabled"),r.onTrackChanged()}}},Dc={AUDIO:function(t,e,i){if(e){var r=i.tech,n=i.requestOptions,s=i.segmentLoaders[t];e.on("loadedmetadata",function(){var t=e.media();s.playlist(t,n),(!r.paused()||t.endList&&"none"!==r.preload())&&s.load()}),e.on("loadedplaylist",function(){s.playlist(e.media(),n),r.paused()||s.load()}),e.on("error",xc[t](t,i))}},SUBTITLES:function(t,e,i){var r=i.tech,n=i.requestOptions,s=i.segmentLoaders[t],a=i.mediaTypes[t];e.on("loadedmetadata",function(){var t=e.media();s.playlist(t,n),s.track(a.activeTrack()),(!r.paused()||t.endList&&"none"!==r.preload())&&s.load()}),e.on("loadedplaylist",function(){s.playlist(e.media(),n),r.paused()||s.load()}),e.on("error",xc[t](t,i))}},Mc=function(t,e){return function(i){return i.attributes[t]===e}},Rc=function(t){return function(e){return e.resolvedUri===t}},Uc={AUDIO:function(t,e){var i=e.hls,r=e.sourceType,n=e.segmentLoaders[t],s=e.requestOptions.withCredentials,a=e.master,o=a.mediaGroups,u=a.playlists,l=e.mediaTypes[t],c=l.groups,h=l.tracks,d=e.masterPlaylistLoader;o[t]&&0!==Object.keys(o[t]).length||(o[t]={main:{default:{default:!0}}});for(var p in o[t]){c[p]||(c[p]=[]);var f=u.filter(Mc(t,p));for(var m in o[t][p]){var g=o[t][p][m];f.filter(Rc(g.resolvedUri)).length&&delete g.resolvedUri;var y=void 0;if(y=g.resolvedUri?new uu(g.resolvedUri,i,s):g.playlists&&"dash"===r?new Il(g.playlists[0],i,s,d):null,g=le.mergeOptions({id:m,playlistLoader:y},g),Dc[t](t,g.playlistLoader,e),c[p].push(g),void 0===h[m]){var v=new le.AudioTrack({id:m,kind:Ac(g),enabled:!1,language:g.language,default:g.default,label:m});h[m]=v}}}n.on("error",xc[t](t,e))},SUBTITLES:function(t,e){var i=e.tech,r=e.hls,n=e.sourceType,s=e.segmentLoaders[t],a=e.requestOptions.withCredentials,o=e.master.mediaGroups,u=e.mediaTypes[t],l=u.groups,c=u.tracks,h=e.masterPlaylistLoader;for(var d in o[t]){l[d]||(l[d]=[]);for(var p in o[t][d])if(!o[t][d][p].forced){var f=o[t][d][p],m=void 0;if("hls"===n?m=new uu(f.resolvedUri,r,a):"dash"===n&&(m=new Il(f.playlists[0],r,a,h)),f=le.mergeOptions({id:p,playlistLoader:m},f),Dc[t](t,f.playlistLoader,e),l[d].push(f),void 0===c[p]){var g=i.addRemoteTextTrack({id:p,kind:"subtitles",enabled:!1,language:f.language,label:p},!1).track;c[p]=g}}}s.on("error",xc[t](t,e))},"CLOSED-CAPTIONS":function(t,e){var i=e.tech,r=e.master.mediaGroups,n=e.mediaTypes[t],s=n.groups,a=n.tracks;for(var o in r[t]){s[o]||(s[o]=[]);for(var u in r[t][o]){var l=r[t][o][u];if(l.instreamId.match(/CC\d/)&&(s[o].push(le.mergeOptions({id:u},l)),void 0===a[u])){var c=i.addRemoteTextTrack({id:l.instreamId,kind:"captions",enabled:!1,language:l.language,label:u},!1).track;a[u]=c}}}}},Nc=function(t,e){return function(i){var r=e.masterPlaylistLoader,n=e.mediaTypes[t].groups,s=r.media();if(!s)return null;var a=null;return s.attributes[t]&&(a=n[s.attributes[t]]),a=a||n.main,void 0===i?a:null===i?null:a.filter(function(t){return t.id===i.id})[0]||null}},Bc={AUDIO:function(t,e){return function(){var i=e.mediaTypes[t].tracks;for(var r in i)if(i[r].enabled)return i[r];return null}},SUBTITLES:function(t,e){return function(){var i=e.mediaTypes[t].tracks;for(var r in i)if("showing"===i[r].mode)return i[r];return null}}},jc=function(t){["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){Uc[e](e,t)});var e=t.mediaTypes,i=t.masterPlaylistLoader,r=t.tech,n=t.hls;["AUDIO","SUBTITLES"].forEach(function(i){e[i].activeGroup=Nc(i,t),e[i].activeTrack=Bc[i](i,t),e[i].onGroupChanged=Pc(i,t),e[i].onTrackChanged=Ic(i,t)});var s=e.AUDIO.activeGroup(),a=(s.filter(function(t){return t.default})[0]||s[0]).id;e.AUDIO.tracks[a].enabled=!0,e.AUDIO.onTrackChanged(),i.on("mediachange",function(){["AUDIO","SUBTITLES"].forEach(function(t){return e[t].onGroupChanged()})});var o=function(){e.AUDIO.onTrackChanged(),r.trigger({type:"usage",name:"hls-audio-change"})};r.audioTracks().addEventListener("change",o),r.remoteTextTracks().addEventListener("change",e.SUBTITLES.onTrackChanged),n.on("dispose",function(){r.audioTracks().removeEventListener("change",o),r.remoteTextTracks().removeEventListener("change",e.SUBTITLES.onTrackChanged)}),r.clearTracks("audio");for(var u in e.AUDIO.tracks)r.audioTracks().addTrack(e.AUDIO.tracks[u])},Fc=function(){var t={};return["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){t[e]={groups:{},tracks:{},activePlaylistLoader:null,activeGroup:pe,activeTrack:pe,onGroupChanged:pe,onTrackChanged:pe}}),t},Hc=void 0,Vc=["mediaRequests","mediaRequestsAborted","mediaRequestsTimedout","mediaRequestsErrored","mediaTransferDuration","mediaBytesTransferred"],qc=function(t){return this.audioSegmentLoader_[t]+this.mainSegmentLoader_[t]},Wc=function(t){function e(i){Ie(this,e);var r=De(this,t.call(this)),n=i.url,s=i.withCredentials,a=i.tech,o=i.bandwidth,u=i.externHls,l=i.useCueTags,c=i.blacklistDuration,h=i.enableLowInitialPlaylist,d=i.sourceType;if(!n)throw new Error("A non-empty playlist URL is required");Hc=u,r.withCredentials=s,r.tech_=a,r.hls_=a.hls,r.sourceType_=d,r.useCueTags_=l,r.blacklistDuration=c,r.enableLowInitialPlaylist=h,r.useCueTags_&&(r.cueTagsTrack_=r.tech_.addTextTrack("metadata","ad-cues"),r.cueTagsTrack_.inBandMetadataTrackDispatchType=""),r.requestOptions_={withCredentials:r.withCredentials,timeout:null},r.mediaTypes_=Fc(),r.mediaSource=new le.MediaSource,r.mediaSource.addEventListener("sourceopen",r.handleSourceOpen_.bind(r)),r.seekable_=le.createTimeRanges(),r.hasPlayed_=function(){return!1},r.syncController_=new Cc(i),r.segmentMetadataTrack_=a.addRemoteTextTrack({kind:"metadata",label:"segment-metadata"},!1).track,r.decrypter_=new wc;var p={hls:r.hls_,mediaSource:r.mediaSource,currentTime:r.tech_.currentTime.bind(r.tech_),seekable:function(){return r.seekable()},seeking:function(){return r.tech_.seeking()},duration:function(){return r.mediaSource.duration},hasPlayed:function(){return r.hasPlayed_()},goalBufferLength:function(){return r.goalBufferLength()},bandwidth:o,syncController:r.syncController_,decrypter:r.decrypter_,sourceType:r.sourceType_};return r.masterPlaylistLoader_="dash"===r.sourceType_?new Il(n,r.hls_,r.withCredentials):new uu(n,r.hls_,r.withCredentials),r.setupMasterPlaylistLoaderListeners_(),r.mainSegmentLoader_=new yc(le.mergeOptions(p,{segmentMetadataTrack:r.segmentMetadataTrack_,loaderType:"main"}),i),r.audioSegmentLoader_=new yc(le.mergeOptions(p,{loaderType:"audio"}),i),r.subtitleSegmentLoader_=new bc(le.mergeOptions(p,{loaderType:"vtt"}),i),r.setupSegmentLoaderListeners_(),Vc.forEach(function(t){r[t+"_"]=qc.bind(r,t)}),r.logger_=xl("MPC"),r.masterPlaylistLoader_.load(),r}return xe(e,t),e.prototype.setupMasterPlaylistLoaderListeners_=function(){var t=this;this.masterPlaylistLoader_.on("loadedmetadata",function(){var e=t.masterPlaylistLoader_.media(),i=1.5*t.masterPlaylistLoader_.targetDuration*1e3;Iu(t.masterPlaylistLoader_.master,t.masterPlaylistLoader_.media())?t.requestOptions_.timeout=0:t.requestOptions_.timeout=i,e.endList&&"none"!==t.tech_.preload()&&(t.mainSegmentLoader_.playlist(e,t.requestOptions_),t.mainSegmentLoader_.load()),jc({sourceType:t.sourceType_,segmentLoaders:{AUDIO:t.audioSegmentLoader_,SUBTITLES:t.subtitleSegmentLoader_,main:t.mainSegmentLoader_},tech:t.tech_,requestOptions:t.requestOptions_,masterPlaylistLoader:t.masterPlaylistLoader_,hls:t.hls_,master:t.master(),mediaTypes:t.mediaTypes_,blacklistCurrentPlaylist:t.blacklistCurrentPlaylist.bind(t)}),t.triggerPresenceUsage_(t.master(),e);try{t.setupSourceBuffers_()}catch(e){return le.log.warn("Failed to create SourceBuffers",e),t.mediaSource.endOfStream("decode")}t.setupFirstPlay(),t.trigger("selectedinitialmedia")}),this.masterPlaylistLoader_.on("loadedplaylist",function(){var e=t.masterPlaylistLoader_.media();if(!e){t.excludeUnsupportedVariants_();var i=void 0;return t.enableLowInitialPlaylist&&(i=t.selectInitialPlaylist()),i||(i=t.selectPlaylist()),t.initialMedia_=i,void t.masterPlaylistLoader_.media(t.initialMedia_)}if(t.useCueTags_&&t.updateAdCues_(e),t.mainSegmentLoader_.playlist(e,t.requestOptions_),t.updateDuration(),t.tech_.paused()||t.mainSegmentLoader_.load(),!e.endList){var r=function(){var e=t.seekable();0!==e.length&&t.mediaSource.addSeekableRange_(e.start(0),e.end(0))};if(t.duration()!==1/0){var n=function e(){t.duration()===1/0?r():t.tech_.one("durationchange",e)};t.tech_.one("durationchange",n)}else r()}}),this.masterPlaylistLoader_.on("error",function(){t.blacklistCurrentPlaylist(t.masterPlaylistLoader_.error)}),this.masterPlaylistLoader_.on("mediachanging",function(){t.mainSegmentLoader_.abort(),t.mainSegmentLoader_.pause()}),this.masterPlaylistLoader_.on("mediachange",function(){var e=t.masterPlaylistLoader_.media(),i=1.5*t.masterPlaylistLoader_.targetDuration*1e3;Iu(t.masterPlaylistLoader_.master,t.masterPlaylistLoader_.media())?t.requestOptions_.timeout=0:t.requestOptions_.timeout=i,t.mainSegmentLoader_.playlist(e,t.requestOptions_),t.mainSegmentLoader_.load(),t.tech_.trigger({type:"mediachange",bubbles:!0})}),this.masterPlaylistLoader_.on("playlistunchanged",function(){var e=t.masterPlaylistLoader_.media();t.stuckAtPlaylistEnd_(e)&&(t.blacklistCurrentPlaylist({message:"Playlist no longer updating."}),t.tech_.trigger("playliststuck"))}),this.masterPlaylistLoader_.on("renditiondisabled",function(){t.tech_.trigger({type:"usage",name:"hls-rendition-disabled"})}),this.masterPlaylistLoader_.on("renditionenabled",function(){t.tech_.trigger({type:"usage",name:"hls-rendition-enabled"})})},e.prototype.triggerPresenceUsage_=function(t,e){var i=t.mediaGroups||{},r=!0,n=Object.keys(i.AUDIO);for(var s in i.AUDIO)for(var a in i.AUDIO[s]){var o=i.AUDIO[s][a];o.uri||(r=!1)}r&&this.tech_.trigger({type:"usage",name:"hls-demuxed"}),Object.keys(i.SUBTITLES).length&&this.tech_.trigger({type:"usage",name:"hls-webvtt"}),Hc.Playlist.isAes(e)&&this.tech_.trigger({type:"usage",name:"hls-aes"}),Hc.Playlist.isFmp4(e)&&this.tech_.trigger({type:"usage",name:"hls-fmp4"}),n.length&&Object.keys(i.AUDIO[n[0]]).length>1&&this.tech_.trigger({type:"usage",name:"hls-alternate-audio"}),this.useCueTags_&&this.tech_.trigger({type:"usage",name:"hls-playlist-cue-tags"})},e.prototype.setupSegmentLoaderListeners_=function(){var t=this;this.mainSegmentLoader_.on("bandwidthupdate",function(){var e=t.selectPlaylist(),i=t.masterPlaylistLoader_.media(),r=t.tech_.buffered(),n=r.length?r.end(r.length-1)-t.tech_.currentTime():0,s=t.bufferLowWaterLine();(!i.endList||t.duration()<Ml.MAX_BUFFER_LOW_WATER_LINE||e.attributes.BANDWIDTH<i.attributes.BANDWIDTH||n>=s)&&t.masterPlaylistLoader_.media(e),t.tech_.trigger("bandwidthupdate")}),this.mainSegmentLoader_.on("progress",function(){t.trigger("progress")}),this.mainSegmentLoader_.on("error",function(){t.blacklistCurrentPlaylist(t.mainSegmentLoader_.error())}),this.mainSegmentLoader_.on("syncinfoupdate",function(){t.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("timestampoffset",function(){t.tech_.trigger({type:"usage",name:"hls-timestamp-offset"})}),this.audioSegmentLoader_.on("syncinfoupdate",function(){t.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("ended",function(){t.onEndOfStream()}),this.mainSegmentLoader_.on("earlyabort",function(){t.blacklistCurrentPlaylist({message:"Aborted early because there isn't enough bandwidth to complete the request without rebuffering."},120)}),this.mainSegmentLoader_.on("reseteverything",function(){t.tech_.trigger("hls-reset")}),this.mainSegmentLoader_.on("segmenttimemapping",function(e){t.tech_.trigger({type:"hls-segment-time-mapping",mapping:e.mapping})}),this.audioSegmentLoader_.on("ended",function(){t.onEndOfStream()})},e.prototype.mediaSecondsLoaded_=function(){return Math.max(this.audioSegmentLoader_.mediaSecondsLoaded+this.mainSegmentLoader_.mediaSecondsLoaded)},e.prototype.load=function(){this.mainSegmentLoader_.load(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.load(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.load()},e.prototype.fastQualityChange_=function(){var t=this.selectPlaylist();t!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(t),this.mainSegmentLoader_.resetLoader())},e.prototype.play=function(){if(!this.setupFirstPlay()){this.tech_.ended()&&this.tech_.setCurrentTime(0),this.hasPlayed_()&&this.load();var t=this.tech_.seekable();return this.tech_.duration()===1/0&&this.tech_.currentTime()<t.start(0)?this.tech_.setCurrentTime(t.end(t.length-1)):void 0}},e.prototype.setupFirstPlay=function(){var t=this,e=this.masterPlaylistLoader_.media();if(!e||this.tech_.paused()||this.hasPlayed_())return!1;if(!e.endList){var i=this.seekable();if(!i.length)return!1;if(le.browser.IE_VERSION&&0===this.tech_.readyState())return this.tech_.one("loadedmetadata",function(){t.trigger("firstplay"),t.tech_.setCurrentTime(i.end(0)),t.hasPlayed_=function(){return!0}}),!1;this.trigger("firstplay"),this.tech_.setCurrentTime(i.end(0))}return this.hasPlayed_=function(){return!0},this.load(),!0},e.prototype.handleSourceOpen_=function(){try{this.setupSourceBuffers_()}catch(t){return le.log.warn("Failed to create Source Buffers",t),this.mediaSource.endOfStream("decode")}this.tech_.autoplay()&&this.tech_.play(),this.trigger("sourceopen")},e.prototype.onEndOfStream=function(){var t=this.mainSegmentLoader_.ended_;this.mediaTypes_.AUDIO.activePlaylistLoader&&(t=!this.mainSegmentLoader_.startingMedia_||this.mainSegmentLoader_.startingMedia_.containsVideo?t&&this.audioSegmentLoader_.ended_:this.audioSegmentLoader_.ended_),t&&this.mediaSource.endOfStream()},e.prototype.stuckAtPlaylistEnd_=function(t){if(!this.seekable().length)return!1;var e=this.syncController_.getExpiredTime(t,this.mediaSource.duration);if(null===e)return!1;var i=Hc.Playlist.playlistEnd(t,e),r=this.tech_.currentTime(),n=this.tech_.buffered();if(!n.length)return i-r<=.1;var s=n.end(n.length-1);return s-r<=.1&&i-s<=.1},e.prototype.blacklistCurrentPlaylist=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments[1],i=void 0,r=void 0;if(i=t.playlist||this.masterPlaylistLoader_.media(),e=e||t.blacklistDuration||this.blacklistDuration,!i){this.error=t;try{return this.mediaSource.endOfStream("network")}catch(t){return this.trigger("error")}}var n=1===this.masterPlaylistLoader_.master.playlists.filter(Cu).length;return n?(le.log.warn("Problem encountered with the current HLS playlist. Trying again since it is the final playlist."),this.tech_.trigger("retryplaylist"),this.masterPlaylistLoader_.load(n)):(i.excludeUntil=Date.now()+1e3*e,this.tech_.trigger("blacklistplaylist"),this.tech_.trigger({type:"usage",name:"hls-rendition-blacklisted"}),r=this.selectPlaylist(),le.log.warn("Problem encountered with the current HLS playlist."+(t.message?" "+t.message:"")+" Switching to another playlist."),this.masterPlaylistLoader_.media(r))},e.prototype.pauseLoading=function(){this.mainSegmentLoader_.pause(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.pause(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.pause()},e.prototype.setCurrentTime=function(t){var e=zu(this.tech_.buffered(),t);return this.masterPlaylistLoader_&&this.masterPlaylistLoader_.media()&&this.masterPlaylistLoader_.media().segments?e&&e.length?t:(this.mainSegmentLoader_.resetEverything(),this.mainSegmentLoader_.abort(),this.mediaTypes_.AUDIO.activePlaylistLoader&&(this.audioSegmentLoader_.resetEverything(),this.audioSegmentLoader_.abort()),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&(this.subtitleSegmentLoader_.resetEverything(),this.subtitleSegmentLoader_.abort()),void this.load()):0},e.prototype.duration=function(){return this.masterPlaylistLoader_?this.mediaSource?this.mediaSource.duration:Hc.Playlist.duration(this.masterPlaylistLoader_.media()):0},e.prototype.seekable=function(){return this.seekable_},e.prototype.onSyncInfoUpdate_=function(){var t=void 0,e=void 0;if(this.masterPlaylistLoader_){var i=this.masterPlaylistLoader_.media();if(i){var r=this.syncController_.getExpiredTime(i,this.mediaSource.duration);if(null!==r&&(t=Hc.Playlist.seekable(i,r),0!==t.length)){if(this.mediaTypes_.AUDIO.activePlaylistLoader){if(i=this.mediaTypes_.AUDIO.activePlaylistLoader.media(),null===(r=this.syncController_.getExpiredTime(i,this.mediaSource.duration)))return;if(e=Hc.Playlist.seekable(i,r),0===e.length)return}e?e.start(0)>t.end(0)||t.start(0)>e.end(0)?this.seekable_=t:this.seekable_=le.createTimeRanges([[e.start(0)>t.start(0)?e.start(0):t.start(0),e.end(0)<t.end(0)?e.end(0):t.end(0)]]):this.seekable_=t,this.logger_("seekable updated ["+$u(this.seekable_)+"]"),this.tech_.trigger("seekablechanged")}}}},e.prototype.updateDuration=function(){var t=this,e=this.mediaSource.duration,i=Hc.Playlist.duration(this.masterPlaylistLoader_.media()),r=this.tech_.buffered(),n=function e(){t.mediaSource.duration=i,t.tech_.trigger("durationchange"),t.mediaSource.removeEventListener("sourceopen",e)};r.length>0&&(i=Math.max(i,r.end(r.length-1))),e!==i&&("open"!==this.mediaSource.readyState?this.mediaSource.addEventListener("sourceopen",n):n())},e.prototype.dispose=function(){var t=this;this.decrypter_.terminate(),this.masterPlaylistLoader_.dispose(),this.mainSegmentLoader_.dispose(),["AUDIO","SUBTITLES"].forEach(function(e){var i=t.mediaTypes_[e].groups;for(var r in i)i[r].forEach(function(t){t.playlistLoader&&t.playlistLoader.dispose()})}),this.audioSegmentLoader_.dispose(),this.subtitleSegmentLoader_.dispose()},e.prototype.master=function(){return this.masterPlaylistLoader_.master},e.prototype.media=function(){return this.masterPlaylistLoader_.media()||this.initialMedia_},e.prototype.setupSourceBuffers_=function(){var t=this.masterPlaylistLoader_.media(),e=void 0;if(t&&"open"===this.mediaSource.readyState){if(e=rc(this.masterPlaylistLoader_.master,t),e.length<1)return this.error="No compatible SourceBuffer configuration for the variant stream:"+t.resolvedUri,this.mediaSource.endOfStream("decode");this.configureLoaderMimeTypes_(e),this.excludeIncompatibleVariants_(t)}},e.prototype.configureLoaderMimeTypes_=function(t){var e=t.length>1&&-1===t[0].indexOf(",")&&t[0]!==t[1]?new le.EventTarget:null;this.mainSegmentLoader_.mimeType(t[0],e),t[1]&&this.audioSegmentLoader_.mimeType(t[1],e)},e.prototype.excludeUnsupportedVariants_=function(){this.master().playlists.forEach(function(t){t.attributes.CODECS&&ve.MediaSource&&ve.MediaSource.isTypeSupported&&!ve.MediaSource.isTypeSupported('video/mp4; codecs="'+Ql(t.attributes.CODECS)+'"')&&(t.excludeUntil=1/0)})},e.prototype.excludeIncompatibleVariants_=function(t){var e=2,i=null,r=void 0;t.attributes.CODECS&&(r=Jl(t.attributes.CODECS),i=r.videoCodec,e=r.codecCount),this.master().playlists.forEach(function(t){var r={codecCount:2,videoCodec:null};t.attributes.CODECS&&(r=Jl(t.attributes.CODECS)),r.codecCount!==e&&(t.excludeUntil=1/0),r.videoCodec!==i&&(t.excludeUntil=1/0)})},e.prototype.updateAdCues_=function(t){var e=0,i=this.seekable();i.length&&(e=i.start(0)),Sc(t,this.cueTagsTrack_,e)},e.prototype.goalBufferLength=function(){var t=this.tech_.currentTime(),e=Ml.GOAL_BUFFER_LENGTH,i=Ml.GOAL_BUFFER_LENGTH_RATE,r=Math.max(e,Ml.MAX_GOAL_BUFFER_LENGTH);return Math.min(e+t*i,r)},e.prototype.bufferLowWaterLine=function(){var t=this.tech_.currentTime(),e=Ml.BUFFER_LOW_WATER_LINE,i=Ml.BUFFER_LOW_WATER_LINE_RATE,r=Math.max(e,Ml.MAX_BUFFER_LOW_WATER_LINE);return Math.min(e+t*i,r)},e}(le.EventTarget),Gc=function(t,e,i){return function(r){var n=t.master.playlists[e],s=Eu(n),a=Cu(n);return void 0===r?a:(r?delete n.disabled:n.disabled=!0,r===a||s||(i(),r?t.trigger("renditionenabled"):t.trigger("renditiondisabled")),r)}},zc=function t(e,i,r){Ie(this,t);var n=e.masterPlaylistController_.fastQualityChange_.bind(e.masterPlaylistController_);if(i.attributes.RESOLUTION){var s=i.attributes.RESOLUTION;this.width=s.width,this.height=s.height}this.bandwidth=i.attributes.BANDWIDTH,this.id=r,this.enabled=Gc(e.playlists,i.uri,n)},Xc=function(t){var e=t.playlists;t.representations=function(){return e.master.playlists.filter(function(t){return!Eu(t)}).map(function(e,i){return new zc(t,e,e.uri)})}},Yc=["seeking","seeked","pause","playing","error"],$c=function(){function t(e){var i=this;Ie(this,t),this.tech_=e.tech,this.seekable=e.seekable,this.consecutiveUpdates=0,this.lastRecordedTime=null,this.timer_=null,this.checkCurrentTimeTimeout_=null,this.logger_=xl("PlaybackWatcher"),this.logger_("initialize");var r=function(){return i.monitorCurrentTime_()},n=function(){return i.techWaiting_()},s=function(){return i.cancelTimer_()},a=function(){return i.fixesBadSeeks_()};this.tech_.on("seekablechanged",a),this.tech_.on("waiting",n),this.tech_.on(Yc,s),this.tech_.on("canplay",r),this.dispose=function(){i.logger_("dispose"),i.tech_.off("seekablechanged",a),i.tech_.off("waiting",n),i.tech_.off(Yc,s),i.tech_.off("canplay",r),i.checkCurrentTimeTimeout_&&ve.clearTimeout(i.checkCurrentTimeTimeout_),i.cancelTimer_()}}return t.prototype.monitorCurrentTime_=function(){this.checkCurrentTime_(),
this.checkCurrentTimeTimeout_&&ve.clearTimeout(this.checkCurrentTimeTimeout_),this.checkCurrentTimeTimeout_=ve.setTimeout(this.monitorCurrentTime_.bind(this),250)},t.prototype.checkCurrentTime_=function(){if(this.tech_.seeking()&&this.fixesBadSeeks_())return this.consecutiveUpdates=0,void(this.lastRecordedTime=this.tech_.currentTime());if(!this.tech_.paused()&&!this.tech_.seeking()){var t=this.tech_.currentTime(),e=this.tech_.buffered();if(this.lastRecordedTime===t&&(!e.length||t+.1>=e.end(e.length-1)))return this.techWaiting_();this.consecutiveUpdates>=5&&t===this.lastRecordedTime?(this.consecutiveUpdates++,this.waiting_()):t===this.lastRecordedTime?this.consecutiveUpdates++:(this.consecutiveUpdates=0,this.lastRecordedTime=t)}},t.prototype.cancelTimer_=function(){this.consecutiveUpdates=0,this.timer_&&(this.logger_("cancelTimer_"),clearTimeout(this.timer_)),this.timer_=null},t.prototype.fixesBadSeeks_=function(){var t=this.tech_.seeking(),e=this.seekable(),i=this.tech_.currentTime(),r=void 0;if(t&&this.afterSeekableWindow_(e,i)){r=e.end(e.length-1)}if(t&&this.beforeSeekableWindow_(e,i)){r=e.start(0)+.1}return void 0!==r&&(this.logger_("Trying to seek outside of seekable at time "+i+" with seekable range "+$u(e)+". Seeking to "+r+"."),this.tech_.setCurrentTime(r),!0)},t.prototype.waiting_=function(){if(!this.techWaiting_()){var t=this.tech_.currentTime(),e=this.tech_.buffered(),i=zu(e,t);return i.length&&t+3<=i.end(0)?(this.cancelTimer_(),this.tech_.setCurrentTime(t),this.logger_("Stopped at "+t+" while inside a buffered region ["+i.start(0)+" -> "+i.end(0)+"]. Attempting to resume playback by seeking to the current time."),void this.tech_.trigger({type:"usage",name:"hls-unknown-waiting"})):void 0}},t.prototype.techWaiting_=function(){var t=this.seekable(),e=this.tech_.currentTime();if(this.tech_.seeking()&&this.fixesBadSeeks_())return!0;if(this.tech_.seeking()||null!==this.timer_)return!0;if(this.beforeSeekableWindow_(t,e)){var i=t.end(t.length-1);return this.logger_("Fell out of live window at time "+e+". Seeking to live point (seekable end) "+i),this.cancelTimer_(),this.tech_.setCurrentTime(i),this.tech_.trigger({type:"usage",name:"hls-live-resync"}),!0}var r=this.tech_.buffered(),n=Xu(r,e);if(this.videoUnderflow_(n,r,e))return this.cancelTimer_(),this.tech_.setCurrentTime(e),this.tech_.trigger({type:"usage",name:"hls-video-underflow"}),!0;if(n.length>0){var s=n.start(0)-e;return this.logger_("Stopped at "+e+", setting timer for "+s+", seeking to "+n.start(0)),this.timer_=setTimeout(this.skipTheGap_.bind(this),1e3*s,e),!0}return!1},t.prototype.afterSeekableWindow_=function(t,e){return!!t.length&&e>t.end(t.length-1)+.1},t.prototype.beforeSeekableWindow_=function(t,e){return!!(t.length&&t.start(0)>0&&e<t.start(0)-.1)},t.prototype.videoUnderflow_=function(t,e,i){if(0===t.length){var r=this.gapFromVideoUnderflow_(e,i);if(r)return this.logger_("Encountered a gap in video from "+r.start+" to "+r.end+". Seeking to current time "+i),!0}return!1},t.prototype.skipTheGap_=function(t){var e=this.tech_.buffered(),i=this.tech_.currentTime(),r=Xu(e,i);this.cancelTimer_(),0!==r.length&&i===t&&(this.logger_("skipTheGap_:","currentTime:",i,"scheduled currentTime:",t,"nextRange start:",r.start(0)),this.tech_.setCurrentTime(r.start(0)+1/30),this.tech_.trigger({type:"usage",name:"hls-gap-skip"}))},t.prototype.gapFromVideoUnderflow_=function(t,e){for(var i=Yu(t),r=0;r<i.length;r++){var n=i.start(r),s=i.end(r);if(e-n<4&&e-n>2)return{start:n,end:s}}return null},t}(),Kc={errorInterval:30,getSource:function(t){return t(this.tech({IWillNotUseThisInPlugins:!0}).currentSource_)}},Jc=function t(e,i){var r=0,n=0,s=le.mergeOptions(Kc,i);e.ready(function(){e.trigger({type:"usage",name:"hls-error-reload-initialized"})});var a=function(){n&&e.currentTime(n)},o=function(t){null!==t&&void 0!==t&&(n=e.duration()!==1/0&&e.currentTime()||0,e.one("loadedmetadata",a),e.src(t),e.trigger({type:"usage",name:"hls-error-reload"}),e.play())},u=function(){return Date.now()-r<1e3*s.errorInterval?void e.trigger({type:"usage",name:"hls-error-reload-canceled"}):s.getSource&&"function"==typeof s.getSource?(r=Date.now(),s.getSource.call(e,o)):void le.log.error("ERROR: reloadSourceOnError - The option getSource must be a function!")},l=function t(){e.off("loadedmetadata",a),e.off("error",u),e.off("dispose",t)},c=function(i){l(),t(e,i)};e.on("error",u),e.on("dispose",l),e.reloadSourceOnError=c},Qc=function(t){Jc(this,t)},Zc=Ko.Decrypter,th=Ko.AsyncStream,eh=Ko.decrypt,ih={PlaylistLoader:uu,Playlist:xu,Decrypter:Zc,AsyncStream:th,decrypt:eh,utils:Wu,STANDARD_PLAYLIST_SELECTOR:lc,INITIAL_PLAYLIST_SELECTOR:hc,comparePlaylistBandwidth:ac,comparePlaylistResolution:oc,xhr:Ru()};["GOAL_BUFFER_LENGTH","MAX_GOAL_BUFFER_LENGTH","GOAL_BUFFER_LENGTH_RATE","BUFFER_LOW_WATER_LINE","MAX_BUFFER_LOW_WATER_LINE","BUFFER_LOW_WATER_LINE_RATE","BANDWIDTH_VARIANCE"].forEach(function(t){Object.defineProperty(ih,t,{get:function(){return le.log.warn("using Hls."+t+" is UNSAFE be sure you know what you are doing"),Ml[t]},set:function(e){if(le.log.warn("using Hls."+t+" is UNSAFE be sure you know what you are doing"),"number"!=typeof e||e<0)return void le.log.warn("value of Hls."+t+" must be greater than or equal to 0");Ml[t]=e}})});var rh=function(t){return/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i.test(t)?"hls":/^application\/dash\+xml/i.test(t)?"dash":null},nh=function(t,e){for(var i=e.media(),r=-1,n=0;n<t.length;n++)if(t[n].id===i.uri){r=n;break}t.selectedIndex_=r,t.trigger({selectedIndex:r,type:"change"})},sh=function(t,e){e.representations().forEach(function(e){t.addQualityLevel(e)}),nh(t,e.playlists)};ih.canPlaySource=function(){return le.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var ah=function(t,e,i){if(!t)return t;var r={};for(var n in t)r[n]={audioContentType:'audio/mp4; codecs="'+i.attributes.CODECS+'"',videoContentType:'video/mp4; codecs="'+e.attributes.CODECS+'"'},"string"==typeof t[n]&&(r[n].url=t[n]);return{keySystems:le.mergeOptions(t,r)}},oh=function(t){if("dash"===t.options_.sourceType){var e=le.players[t.tech_.options_.playerId];e.eme&&(e.eme.options=le.mergeOptions(e.eme.options,ah(t.source_.keySystems,t.playlists.media(),t.masterPlaylistController_.mediaTypes_.AUDIO.activePlaylistLoader.media())))}};ih.supportsNativeHls=function(){var t=ke.createElement("video");return!!le.getTech("Html5").isSupported()&&["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"].some(function(e){return/maybe|probably/i.test(t.canPlayType(e))})}(),ih.supportsNativeDash=function(){return!!le.getTech("Html5").isSupported()&&/maybe|probably/i.test(ke.createElement("video").canPlayType("application/dash+xml"))}(),ih.supportsTypeNatively=function(t){return"hls"===t?ih.supportsNativeHls:"dash"===t&&ih.supportsNativeDash},ih.isSupported=function(){return le.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var uh=le.getComponent("Component"),lh=function(t){function e(i,r,n){Ie(this,e);var s=De(this,t.call(this,r,n.hls));if(r.options_&&r.options_.playerId){var a=le(r.options_.playerId);a.hasOwnProperty("hls")||Object.defineProperty(a,"hls",{get:function(){return le.log.warn("player.hls is deprecated. Use player.tech_.hls instead."),r.trigger({type:"usage",name:"hls-player-access"}),s}}),a.vhs=s,a.dash=s}if(s.tech_=r,s.source_=i,s.stats={},s.ignoreNextSeekingEvent_=!1,s.setOptions_(),s.options_.overrideNative&&(r.featuresNativeVideoTracks||r.featuresNativeAudioTracks))throw new Error("Overriding native HLS requires emulated tracks. See https://git.io/vMpjB");return s.on(ke,["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],function(t){var e=ke.fullscreenElement||ke.webkitFullscreenElement||ke.mozFullScreenElement||ke.msFullscreenElement;e&&e.contains(s.tech_.el())&&s.masterPlaylistController_.fastQualityChange_()}),s.on(s.tech_,"seeking",function(){if(this.ignoreNextSeekingEvent_)return void(this.ignoreNextSeekingEvent_=!1);this.setCurrentTime(this.tech_.currentTime())}),s.on(s.tech_,"error",function(){this.masterPlaylistController_&&this.masterPlaylistController_.pauseLoading()}),s.on(s.tech_,"play",s.play),s}return xe(e,t),e.prototype.setOptions_=function(){var t=this;this.options_.withCredentials=this.options_.withCredentials||!1,"number"!=typeof this.options_.blacklistDuration&&(this.options_.blacklistDuration=300),"number"!=typeof this.options_.bandwidth&&(this.options_.bandwidth=4194304),this.options_.enableLowInitialPlaylist=this.options_.enableLowInitialPlaylist&&4194304===this.options_.bandwidth,["withCredentials","bandwidth"].forEach(function(e){void 0!==t.source_[e]&&(t.options_[e]=t.source_[e])}),this.bandwidth=this.options_.bandwidth},e.prototype.src=function(t,e){var i=this;t&&(this.setOptions_(),this.options_.url=this.source_.src,this.options_.tech=this.tech_,this.options_.externHls=ih,this.options_.sourceType=rh(e),this.masterPlaylistController_=new Wc(this.options_),this.playbackWatcher_=new $c(le.mergeOptions(this.options_,{seekable:function(){return i.seekable()}})),this.masterPlaylistController_.on("error",function(){le.players[i.tech_.options_.playerId].error(i.masterPlaylistController_.error)}),this.masterPlaylistController_.selectPlaylist=this.selectPlaylist?this.selectPlaylist.bind(this):ih.STANDARD_PLAYLIST_SELECTOR.bind(this),this.masterPlaylistController_.selectInitialPlaylist=ih.INITIAL_PLAYLIST_SELECTOR.bind(this),this.playlists=this.masterPlaylistController_.masterPlaylistLoader_,this.mediaSource=this.masterPlaylistController_.mediaSource,Object.defineProperties(this,{selectPlaylist:{get:function(){return this.masterPlaylistController_.selectPlaylist},set:function(t){this.masterPlaylistController_.selectPlaylist=t.bind(this)}},throughput:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.throughput.rate},set:function(t){this.masterPlaylistController_.mainSegmentLoader_.throughput.rate=t,this.masterPlaylistController_.mainSegmentLoader_.throughput.count=1}},bandwidth:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.bandwidth},set:function(t){this.masterPlaylistController_.mainSegmentLoader_.bandwidth=t,this.masterPlaylistController_.mainSegmentLoader_.throughput={rate:0,count:0}}},systemBandwidth:{get:function(){var t=1/(this.bandwidth||1),e=void 0;return e=this.throughput>0?1/this.throughput:0,Math.floor(1/(t+e))},set:function(){le.log.error('The "systemBandwidth" property is read-only')}}}),Object.defineProperties(this.stats,{bandwidth:{get:function(){return i.bandwidth||0},enumerable:!0},mediaRequests:{get:function(){return i.masterPlaylistController_.mediaRequests_()||0},enumerable:!0},mediaRequestsAborted:{get:function(){return i.masterPlaylistController_.mediaRequestsAborted_()||0},enumerable:!0},mediaRequestsTimedout:{get:function(){return i.masterPlaylistController_.mediaRequestsTimedout_()||0},enumerable:!0},mediaRequestsErrored:{get:function(){return i.masterPlaylistController_.mediaRequestsErrored_()||0},enumerable:!0},mediaTransferDuration:{get:function(){return i.masterPlaylistController_.mediaTransferDuration_()||0},enumerable:!0},mediaBytesTransferred:{get:function(){return i.masterPlaylistController_.mediaBytesTransferred_()||0},enumerable:!0},mediaSecondsLoaded:{get:function(){return i.masterPlaylistController_.mediaSecondsLoaded_()||0},enumerable:!0},buffered:{get:function(){return Ju(i.tech_.buffered())},enumerable:!0},currentTime:{get:function(){return i.tech_.currentTime()},enumerable:!0},currentSource:{get:function(){return i.tech_.currentSource_},enumerable:!0},currentTech:{get:function(){return i.tech_.name_},enumerable:!0},duration:{get:function(){return i.tech_.duration()},enumerable:!0},master:{get:function(){return i.playlists.master},enumerable:!0},playerDimensions:{get:function(){return i.tech_.currentDimensions()},enumerable:!0},seekable:{get:function(){return Ju(i.tech_.seekable())},enumerable:!0},timestamp:{get:function(){return Date.now()},enumerable:!0},videoPlaybackQuality:{get:function(){return i.tech_.getVideoPlaybackQuality()},enumerable:!0}}),this.tech_.one("canplay",this.masterPlaylistController_.setupFirstPlay.bind(this.masterPlaylistController_)),this.masterPlaylistController_.on("selectedinitialmedia",function(){Xc(i),oh(i)}),this.on(this.masterPlaylistController_,"progress",function(){this.tech_.trigger("progress")}),this.on(this.masterPlaylistController_,"firstplay",function(){this.ignoreNextSeekingEvent_=!0}),this.tech_.ready(function(){return i.setupQualityLevels_()}),this.tech_.el()&&this.tech_.src(le.URL.createObjectURL(this.masterPlaylistController_.mediaSource)))},e.prototype.setupQualityLevels_=function(){var t=this,e=le.players[this.tech_.options_.playerId];e&&e.qualityLevels&&(this.qualityLevels_=e.qualityLevels(),this.masterPlaylistController_.on("selectedinitialmedia",function(){sh(t.qualityLevels_,t)}),this.playlists.on("mediachange",function(){nh(t.qualityLevels_,t.playlists)}))},e.prototype.play=function(){this.masterPlaylistController_.play()},e.prototype.setCurrentTime=function(t){this.masterPlaylistController_.setCurrentTime(t)},e.prototype.duration=function(){return this.masterPlaylistController_.duration()},e.prototype.seekable=function(){return this.masterPlaylistController_.seekable()},e.prototype.dispose=function(){this.playbackWatcher_&&this.playbackWatcher_.dispose(),this.masterPlaylistController_&&this.masterPlaylistController_.dispose(),this.qualityLevels_&&this.qualityLevels_.dispose(),t.prototype.dispose.call(this)},e}(uh),ch={name:"videojs-http-streaming",VERSION:"0.8.0",canHandleSource:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=le.mergeOptions(le.options,e);return ch.canPlayType(t.type,i)},handleSource:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=le.mergeOptions(le.options,i);return e.hls=new lh(t,e,r),e.hls.xhr=Ru(),e.hls.src(t.src,t.type),e.hls},canPlayType:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=le.mergeOptions(le.options,e),r=i.hls.overrideNative,n=rh(t);return!n||ih.supportsTypeNatively(n)&&!r?"":"maybe"}};return void 0!==le.MediaSource&&void 0!==le.URL||(le.MediaSource=wl,le.URL=Al),wl.supportsNativeMediaSources()&&le.getTech("Html5").registerSourceHandler(ch,0),le.HlsHandler=lh,le.HlsSourceHandler=ch,le.Hls=ih,le.use||le.registerComponent("Hls",ih),le.options.hls=le.options.hls||{},le.registerPlugin?le.registerPlugin("reloadSourceOnError",Qc):le.plugin("reloadSourceOnError",Qc),le});