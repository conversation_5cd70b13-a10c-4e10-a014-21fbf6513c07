/*
CryptoJS v3.1.2
code.google.com/p/crypto-js
(c) 2009-2013 by <PERSON>. All rights reserved.
code.google.com/p/crypto-js/wiki/License
*/
CryptoJS.mode.CTR=function(){var b=CryptoJS.lib.BlockCipherMode.extend(),g=b.Encryptor=b.extend({processBlock:function(b,f){var a=this._cipher,e=a.blockSize,c=this._iv,d=this._counter;c&&(d=this._counter=c.slice(0),this._iv=void 0);c=d.slice(0);a.encryptBlock(c,0);d[e-1]=d[e-1]+1|0;for(a=0;a<e;a++)b[f+a]^=c[a]}});b.Decryptor=g;return b}();
