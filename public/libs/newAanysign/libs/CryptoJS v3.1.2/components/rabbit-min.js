/*
CryptoJS v3.1.2
code.google.com/p/crypto-js
(c) 2009-2013 by <PERSON>. All rights reserved.
code.google.com/p/crypto-js/wiki/License
*/
(function(){function g(){for(var b=this._X,d=this._C,a=0;8>a;a++)f[a]=d[a];d[0]=d[0]+**********+this._b|0;d[1]=d[1]+**********+(d[0]>>>0<f[0]>>>0?1:0)|0;d[2]=d[2]+886263092+(d[1]>>>0<f[1]>>>0?1:0)|0;d[3]=d[3]+**********+(d[2]>>>0<f[2]>>>0?1:0)|0;d[4]=d[4]+**********+(d[3]>>>0<f[3]>>>0?1:0)|0;d[5]=d[5]+886263092+(d[4]>>>0<f[4]>>>0?1:0)|0;d[6]=d[6]+**********+(d[5]>>>0<f[5]>>>0?1:0)|0;d[7]=d[7]+**********+(d[6]>>>0<f[6]>>>0?1:0)|0;this._b=d[7]>>>0<f[7]>>>0?1:0;for(a=0;8>a;a++){var h=b[a]+d[a],e=h&65535,
g=h>>>16;c[a]=((e*e>>>17)+e*g>>>15)+g*g^((h&**********)*h|0)+((h&65535)*h|0)}b[0]=c[0]+(c[7]<<16|c[7]>>>16)+(c[6]<<16|c[6]>>>16)|0;b[1]=c[1]+(c[0]<<8|c[0]>>>24)+c[7]|0;b[2]=c[2]+(c[1]<<16|c[1]>>>16)+(c[0]<<16|c[0]>>>16)|0;b[3]=c[3]+(c[2]<<8|c[2]>>>24)+c[1]|0;b[4]=c[4]+(c[3]<<16|c[3]>>>16)+(c[2]<<16|c[2]>>>16)|0;b[5]=c[5]+(c[4]<<8|c[4]>>>24)+c[3]|0;b[6]=c[6]+(c[5]<<16|c[5]>>>16)+(c[4]<<16|c[4]>>>16)|0;b[7]=c[7]+(c[6]<<8|c[6]>>>24)+c[5]|0}var j=CryptoJS,k=j.lib.StreamCipher,e=[],f=[],c=[],l=j.algo.Rabbit=
k.extend({_doReset:function(){for(var b=this._key.words,d=this.cfg.iv,a=0;4>a;a++)b[a]=(b[a]<<8|b[a]>>>24)&16711935|(b[a]<<24|b[a]>>>8)&4278255360;for(var c=this._X=[b[0],b[3]<<16|b[2]>>>16,b[1],b[0]<<16|b[3]>>>16,b[2],b[1]<<16|b[0]>>>16,b[3],b[2]<<16|b[1]>>>16],b=this._C=[b[2]<<16|b[2]>>>16,b[0]&**********|b[1]&65535,b[3]<<16|b[3]>>>16,b[1]&**********|b[2]&65535,b[0]<<16|b[0]>>>16,b[2]&**********|b[3]&65535,b[1]<<16|b[1]>>>16,b[3]&**********|b[0]&65535],a=this._b=0;4>a;a++)g.call(this);for(a=0;8>
a;a++)b[a]^=c[a+4&7];if(d){var a=d.words,d=a[0],a=a[1],d=(d<<8|d>>>24)&16711935|(d<<24|d>>>8)&4278255360,a=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360,c=d>>>16|a&**********,e=a<<16|d&65535;b[0]^=d;b[1]^=c;b[2]^=a;b[3]^=e;b[4]^=d;b[5]^=c;b[6]^=a;b[7]^=e;for(a=0;4>a;a++)g.call(this)}},_doProcessBlock:function(b,c){var a=this._X;g.call(this);e[0]=a[0]^a[5]>>>16^a[3]<<16;e[1]=a[2]^a[7]>>>16^a[5]<<16;e[2]=a[4]^a[1]>>>16^a[7]<<16;e[3]=a[6]^a[3]>>>16^a[1]<<16;for(a=0;4>a;a++)e[a]=(e[a]<<8|e[a]>>>24)&
16711935|(e[a]<<24|e[a]>>>8)&4278255360,b[c+a]^=e[a]},blockSize:4,ivSize:2});j.Rabbit=k._createHelper(l)})();
