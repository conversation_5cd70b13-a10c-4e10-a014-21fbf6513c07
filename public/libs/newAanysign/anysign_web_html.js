//页面解析到当前为止所有的script标签
var jspath = document.scripts;
//js[js.length - 1] 就是当前的js文件的路径
jspath = jspath[jspath.length - 1].src.substring(0, jspath[jspath.length - 1].src.lastIndexOf("/") + 1);
//输出当前js文件所在的目录
console.info(jspath);

(function($,win,doc){
	// doc.write('<!-- 签名弹框 -->');
	// doc.write('<div id="dialog"  style="font-size: 30pt;z-index: 999;">')
	// doc.write('<!--<p class="close"><a href="#" onclick="closeBg();">关闭</a></p> -->')
	// doc.write('<!-- 显示签名区域-->');
	// doc.write('<div id="anysign_title" style="color:#333333;" width="100%" height="10%">请<span style="font-size:20pt;"> </span>签名</div>');
	// doc.write('<div id="container"  onmousedown = "return false;">');
	// doc.write('<canvas id="anysignCanvas" width="100%" height="70%"></canvas>');
	// doc.write('</div>');
	// doc.write('<div id="btnContainerOuter" width="100%">');
	// doc.write('<div id="btnContainerInner" style="text-align: center;  font-size:5pt;" width="100%">')
	// doc.write('<input id="btnCancel" type="button" class="button orange" value="取 消" onclick="cancelSign();">');
	// doc.write('<input id="btnClear" type="button" class="button orange" value="清 屏" onclick="javascript:clear_canvas();">');
	// doc.write('<input id="btnOK" type="button" class="button orange" value="确 定" onclick="sign_confirm();"/>');
	// doc.write('</div>');
	// doc.write('</div>') 
	
	doc.write('<div id="comment_dialog" style=" display:none;position: fixed;overflow: hidden;">');   
	doc.write('<div id="leftView">');   
	doc.write('<p id="comment_title" style="padding: 15px 10px 5px 10px;"></p>');   
	doc.write('<div id="signImage" class="signImagecss"></div>');   
	doc.write('</div>');   
	doc.write('<div id="tmpcanvascss" class="tmpcanvascss">');   
	doc.write('<div id="signTitle"></div>');   
	doc.write('<canvas id="comment_canvas"></canvas>');   
	doc.write('</div>');   
	doc.write('<div id="comment_btnContainerInner" class="comment_btncontainer" style="clear: both;margin-top: 20px;display: -webkit-flex;display: flex;justify-content:center;">');   
	doc.write('<input id="comment_ok" type="button" class="button orange" value="确 定">');   
	doc.write('<input id="comment_back" type="button" class="button orange" value="后退">');   
	doc.write('<input id="comment_cancel" type="button" class="button orange" value="取 消">');   
	doc.write('</div>');   
	doc.write('</div>');
	   
	doc.write('<!-- 单字签名 -->');   
	doc.write('<div id="single_dialog" style=" display:none;">');   
	doc.write('<div id="leftView">');   
	doc.write('<p id="anysign_title" style="padding: 20px 0; color: #333333;"></p>');   
	doc.write('<div style="position: relative;">');   
	doc.write('<div style="position: absolute; top:-10px; left: 30px; width: 100px; background: #fff; padding-left:20px; height:15px;">');   
	doc.write('<img style="float: left;" width="15px" height="15px" src="'+jspath+'image/bi.png" align="absmiddle"> 手写签名');   
	doc.write('</div>');   
	doc.write('<div id="single_signImage" class="signImagecss" style="overflow: hidden;"></div>');   
	doc.write('</div>');   
	doc.write('</div>');   
	doc.write('<div id="single_tmpcanvascss" class="single_tmpcanvascss">');   
	doc.write('<div id="single_signTitle"></div>');   
	doc.write('<canvas id="single_anysignCanvas"></canvas>');   
	doc.write('</div>');   
	doc.write('<div id="btnContainerInner" class="btncontainer" style="clear: both;display: -webkit-flex;display: flex;justify-content: space-between; padding: 12px 15px;flex-wrap: wrap;">');   
	doc.write('<div id="btnOK" class="button_new">确 定</div>');   
	doc.write('<div id="btnCancel" class="button_new" style="float:right;">取 消</div>');   
	doc.write('<div id="btnClear" class="button_new" style="">清空</div>');   
	doc.write('<div id="btnBack" class="button_new" style="float:right;">后退</div>');   
	doc.write('</div>');   
	doc.write('</div>'); 
	
	doc.write('<div id="dialog" style=" display:none;">');   
	doc.write('<!-- 显示签名区域-->');
	doc.write('<div id="anysign_title" style="color:#333333;" width="100%" height="10%">请投保人<span style="font-size:20pt;"> 李 明</span>签名</div>');   
	doc.write('<div id="container" onmousedown="return false;">');   
	doc.write('<canvas id="anysignCanvas" width="2"></canvas>');   
	doc.write('</div>');   
	doc.write('<div id="single_scrollbar" style="text-align: center;  vertical-align:middle; " width="100%">');   
	doc.write('<span id="single_scroll_text"> *滑动操作：</span>');   
	doc.write('<input id="single_scrollbar_up" type="button" class="button orange" value="左移" />');   
	doc.write('<input id="single_scrollbar_down" type="button" class="button orange" value="右移" />');   
	doc.write('</div>');   
	doc.write('<div id="btnContainerOuter" width="100%">');   
	doc.write('<div id="btnContainerInner" style="text-align: center;   font-size:5pt;" width="100%">');   
	doc.write('<input id="btnOK" type="button" class="button orange" value="确 定" onclick="sign_confirm();" />');   
	doc.write('<input id="btnClear" type="button" class="button orange" value="清 屏" onclick="javascript:clear_canvas();">');   
	doc.write('<input id="btnCancel" type="button" class="button orange" value="取 消" onclick="cancelSign();">');   
	doc.write('</div>');   
	doc.write('</div>');   
	doc.write('</div>');   
	   
	
})(jQuery,window,document)
			
document.onreadystatechange = setAlertTitle;

            var apiInstance;
            var fileData;
            var ocrCapture;
            
      var DATA_CANNOT_PARSED = "10003"; //输入数据项无法解析
      var SERVICE_SYSTEM_EXCEPTION = "10011"; //服务系统异常错误
      var RECOGNITION_RESULT_EMPTY = "10100"; //识别结果为空
      var CONNECTION_SERVICE_TIMEOUT = "10101"; //连接识别服务超时
      var CONNECTION_RECOGNITION_EXCEPTION = "10102"; //连接识别服务异常
      var SUCCESS = "0"; //识别成功
      var RECOGNITION_FALSE = "-1";//识别错误
      var RESULT_OK = 0; //操作成功
      var CALLBACK_TYPE_SIGNATURE = 10; //签名框点击确认之后的回调，回调中包含签名快照
      var CALLBACK_TYPE_DIALOG_CANCEL = 11; //点击签名框"取消"按钮时的回调，同时也会触发dismiss回调
      var CALLBACK_TYPE_COMMENTSIGN = 12; //批注签名框点击确认之后的回调，回调中包含签名快照
      var CALLBACK_TYPE_GETVERSION = 13; //获得版本号
      
      var RESULT_ERROR = -1; //操作失败
      var EC_API_NOT_INITED = 1; //接口未初始化错误
//      var CALLBACK_TYPE_START_RECORDING = 14;
//      var CALLBACK_TYPE_STOP_RECORDING = 15;

            function setAlertTitle()
            {
                document.title = "电子签名";
            }



 //配置模板数据
            function testSetTemplateData(orderNo)
            {

               // var formData = "{\"bjcaxssrequest\":{\"submitinfo\":[{\"username\":\"测星雨\",\"identitycardnbr\":\"320902198901191012\"},{\"username\":\"测星雨123\",\"identitycardnbr\": \"320902198901191012\"}]}}";

				var formData = "<html><head></head><body><div id=\"colArea\" class=\"pageFormContent\" style=\"width:95%;background:#f9fbf9;display:block;\"><div class=\"unit\"><label class=\"fontLabel\">keyword：</label></div><div class=\"unit\"><label class=\"fontLabel\">列名2：</label></div><div class=\"unit\"><label class=\"fontLabel\">列名3：</label></div></div></body></html>";
               //文件数据
//          var formData = fileData;
                //var businessId = "20210324112455523011397";//集成信手书业务的唯一标识
                var businessId = orderNo;//集成信手书业务的唯一标识

                var template_serial = "4000";//用于生成PDF的模板ID


                var res;

                //配置JSON格式签名原文
                /**
                 * 设置表单数据，每次业务都需要set一次
                 * @param template_type 签名所用的模板类型
                 * @param contentUtf8Str 表单数据，类型为Utf8字符串
                 * @param businessId 业务工单号
                 * @param template_serial 模板序列号
                 * @returns {*} 是否设置成功
                 */
                res = apiInstance.setTemplate(TemplateType.PDF,formData,businessId,template_serial);

                if(res)
                {
				 console.log("setTemplateData success");
                    return res;
                }
                else
                {
					console.log("setTemplateData error");
                    return res;
                }
            }
            
//选择文件
function  handleFiles(files)
      {
          if(files.length)
          {
              var file = files[0];
              var reader = new FileReader();

              reader.onload = function FileReaderOnload() {
                var buffer = reader.result;
                var uint8Array = new Uint8Array(reader.result);
                var bufStr = "";
                var bufarray = Base64.encodeUint8Array(uint8Array);
                bufStr = bufarray;
                fileData = bufStr;
              };
              reader.readAsArrayBuffer(file);
          }
      }
      
//添加单签签名框
function testAddSignatureObj(objId,keyword,personName,titleName)
            {

                var context_id = objId;
                var signer = new Signer(personName, "11011111111");

                /**
                 * 根据坐标定位签名方式
                 * @param left 签名图片最左边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
                 * @param top 签名图片顶边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
                 * @param right 签名图片最右边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
                 * @param bottom 签名图片底边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
                 * @param pageNo 签名在PDF中的页码，从1开始
                 * @param unit 坐标系单位，目前支持"dp"和"pt"
                 * @constructor
                 */
                /*if(objId == 20){
                var signerRule = new SignRule_XYZ(100.0, 110.1, 180.2, 50.3, 1, "pt");
                }else{
                var signerRule = new SignRule_XYZ(100.0, 110.1, 180.2, 50.3, 1, "pt");
                }*/


        /**
         * 关键字定位方式，寻找PDF中的关键字，根据关键字位置放置签名图片
         * @param keyword 关键字
         * @param keyWordAlignMethod 签字图片和关键字位置关系：等于1时，签字图片和关键字矩形重心重合
         *                            等于2时，签字图片位于关键字正下方，中心线对齐；等于3时，签字图片位于关键字正右方，中心线对齐；
         *                            等于4时，签字图片左上角和关键字右下角重合，可能额外附加偏移量，详见构造函数的offset参数
         * @param keyWordOffset 当keyWordAlignMethod非零时，额外附加的偏移量，单位pt
         * @param pageNo 寻找关键字的PDF起始页码
         * @param KWIndex KWIndex 第几个关键字
         * @constructor
         */
              var signerRule = new SignRule_KeyWord(keyword,3,0,1,1);
        //var signerRule = new SignRule_KeyWordV2("签名算法",50,0,1,1);

                /**
                 *根据关键字定位签名位置
                 * @param keyWord 关键字字面值
                 * @param xOffset X轴偏移量，适配关键字和规则
                 * @param yOffset Y轴偏移量，适配关键字和规则
                 * @param pageNo 签名在PDF中的页码，第几页查找关键字，正数为正序，当是负数为倒序
                 * @param KWIndex KWIndex 第几个关键字
                 * @constructor
                 */
//                var signerRule = new SignRule_KeyWord("签名算法",100,100,1,1);
            //      var signerRule = new SignRule_KeyWordV2("投保人签名",130,1,1,1);

                /**
                 * 关键字定位方式，寻找PDF中的关键字，根据关键字位置放置签名图片
                 * @param keyword 关键字
                 * @param keyWordAlignMethod 签字图片和关键字位置关系：等于0时，签字图片和关键字矩形重心重合
                 *                            等于1时，签字图片位于关键字正下方，中心线对齐；等于2时，签字图片位于关键字正右方，中心线对齐；
                 *                            等于3时，签字图片左上角和关键字右下角重合，可能额外附加偏移量，详见构造函数的offset参数
                 * @param keyWordOffset 当keyWordAlignMethod非零时，额外附加的偏移量，单位pt
                 * @param pageNo 寻找关键字的PDF起始页码
                 * @param KWIndex KWIndex 第几个关键字
                 * @constructor
                 */
//                var signerRule = new SignRule_KeyWordV2("关键字", "0", 10, 1,1);


                var signatureConfig = new SignatureConfig(signer,signerRule);
//                   1:时间在上、2：时间在下、3：时间在右
                var timeTag = new TimeTag(1,"yyMMdd hh:mm:ss");
                //signatureConfig.timeTag = timeTag;
                signatureConfig.singleWidth = 80;
                signatureConfig.singleHeight = 80;
                signatureConfig.title = titleName;
                signatureConfig.penColor = "#333333";
                signatureConfig.signature_stroke_width = 12;//签名笔划粗细
                signatureConfig.isTSS = false;//是否开始时间戳服务
                signatureConfig.signatureImgRatio = 9.0;//签名图片清晰度
                signatureConfig.nessesary = false;
                signatureConfig.isdistinguish = false;
                signatureConfig.signature_max_times = 5;//手写识别最大次数，取值1-99，默认为3
                signatureConfig.ocrCapture = ocrCapture;
                signatureConfig.isShowBgText = true;
                var res = apiInstance.addSignatureObj(context_id,signatureConfig);
                if(res)
                {
					console.log("addSignatureObj "+context_id+" success");
                    return res;
                }
                else
                {
                    console.log("addSignatureObj "+context_id+" error");
                    return res;
                }
            }


//添加批签签名框
function testAddCommentObj(objId,keyword,personName,titleName)
{

	var context_id = objId;
	var signer = new Signer("李！明", "11011111111");

	/**
	 * 根据坐标定位签名方式
	 * @param left 签名图片最左边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
	 * @param top 签名图片顶边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
	 * @param right 签名图片最右边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
	 * @param bottom 签名图片底边坐标值，相对于PDF当页最左下角(0,0)点，向上和向右分别为X轴、Y轴正方向
	 * @param pageNo 签名在PDF中的页码，从1开始
	 * @param unit 坐标系单位，目前支持"dp"和"pt"
	 * @constructor
	 */
	var signerRule = new SignRule_XYZ(84.0, 523.0, 400.0, 477.0, 1, "dp");



	   /**
	* 关键字定位方式，寻找PDF中的关键字，根据关键字位置放置签名图片
	* @param keyword 关键字
	* @param keyWordAlignMethod 签字图片和关键字位置关系：等于1时，签字图片和关键字矩形重心重合
	*                           等于2时，签字图片位于关键字正下方，中心线对齐；等于3时，签字图片位于关键字正右方，中心线对齐；
	*                            等于4时，签字图片左上角和关键字右下角重合，可能额外附加偏移量，详见构造函数的offset参数
	* @param keyWordOffset 当keyWordAlignMethod非零时，额外附加的偏移量，单位pt
	* @param pageNo 寻找关键字的PDF起始页码
	* @param KWIndex KWIndex 第几个关键字
	* @constructor
	*/
	//              var signerRule = new SignRule_KeyWord("默认",4,0,1,1);


	var commonSignText = "";
	var commentConfig = new CommentConfig(signer,signerRule);
	// commentConfig.commitment = "本人已阅读保险条款，产品说明书和投保提示书，了解本产品的特点和保单利益的不确定性。";
	commentConfig.commitment = commonSignText;
	//commentConfig.commitment = "本人";
	commentConfig.mass_word_height = 50;
	commentConfig.mass_word_width = 50;
	commentConfig.mass_words_in_single_line = 38;
	commentConfig.penColor = "#333333";
	commentConfig.comment_stroke_width = 8;//批注笔划粗细
	commentConfig.isTSS = false;//是否开始时间戳服务
	commentConfig.commentImgRatio = 9.0;//批注图片清晰度
	commentConfig.nessesary = false;
	commentConfig.isdistinguish = true;
	commentConfig.comment_max_times = 5;//手写识别最大次数，取值1-99，默认为3
	commentConfig.ocrCapture = ocrCapture;
	commentConfig.mass_dlg_type = CommentInputType.WhiteBoard;
	var res = apiInstance.addCommentObj(context_id,commentConfig);
	if(res)
	{
	    console.log("addCommentObj "+context_id+" success");
	    return res;
	}
	else
	{
	   console.log("addCommentObj "+context_id+" error");
	    return res;
	}
}

 //demo总入口
function testAnySign(channel,imgId,keyword,personName,titleName,num){
	var res;
	      
	      //识别回调接口
	      var identify_callback = function(errCode){
	        if(errCode == SUCCESS){
	          return;
	        }
	          if(errCode == DATA_CANNOT_PARSED) {
			  myAlert("输入数据项无法解析！",'',['确定']);
	} else if(errCode == SERVICE_SYSTEM_EXCEPTION) {
			   myAlert("服务系统异常错误！",'',['确定']);
	} else if(errCode == RECOGNITION_RESULT_EMPTY) {
			  myAlert("识别结果为空！",'',['确定']);
	} else if(errCode == CONNECTION_SERVICE_TIMEOUT) {
			  myAlert("连接识别服务超时！",'',['确定']);
	} else if(errCode == CONNECTION_RECOGNITION_EXCEPTION) {
			  myAlert("连接识别服务异常！",'',['确定']);
	} else if(errCode == RECOGNITION_FALSE) {
			   myAlert("手写比对不通过，建议使用正楷字体工整重写！",'',['确定']);
	}else{
			  myAlert(errCode,'',['确定']);
	}
	      }
	
	var callback = function(context_id,context_type,val){
		  
		document.getElementById("other").style.display = "block";
		var srcImg = "";
		if(context_type == CALLBACK_TYPE_SIGNATURE){
			//签名回显
			srcImg = "data:image/png;base64," + val;
		}
		else if(context_type == CALLBACK_TYPE_COMMENTSIGN){
		  //签名回显
			srcImg = "data:image/png;base64," + val;
		}

		//setAlertTitle();
		//alert("收到浏览器回调：" + "context_id：" + context_id + " context_type：" + context_type + " value：" + val);
		//console.log(imgId);
		var encData = apiInstance.getUploadDataGram();
		// console.log(encData);
		returnImgUrl(srcImg,imgId,encData,personName,num);
		// saveAnysignData(srcImg, encData, imgId.substr(0,channel.indexOf('_')),imgId.substr(channel.indexOf('_')+1), '2', personName);
		//$("#"+imgId).show();
	};//测试回调，将回调数据显示

	////////////////////////////////////////////////
	
	//设置签名算法，默认为RSA，可以设置成SM2
	EncAlgType.EncAlg = "RSA";
	    
	apiInstance = new AnySignApi();
	var channel = "999999";//渠道号，由信手书提供，请咨询项目经理 。生产渠道号需要填写真实的
	//初始化签名接口
	res = apiInstance.initAnySignApi(callback,channel);
	
	if(!res){
	  console.log("init error");
	}else{
	
	}
	////////////////////////////////////////////////
	
	//开启识别
	// ocrCapture = new OCRCapture();
	// ocrCapture.text = "刘";
	// ocrCapture.IPAdress = "http://*************:11204/HWRV2/RecvServlet";
	// ocrCapture.appID = "123";
	// ocrCapture.count = 5;
	// ocrCapture.language = Language.CHS;
	// ocrCapture.resolution = 80;
	// ocrCapture.serviceID = "999999";

	// setIdentifyCallBack(identify_callback);
	
	ocrCapture = new OCRCapture();
	        ocrCapture.text = "a";
	//ocrCapture.IPAdress = "http://*************:11204/HWRV2/RecvServlet";
	ocrCapture.IPAdress = writedistinguish+"/HWR/RecvServlet";
	//console.log("网管地址域名：" + writedistinguish);
	ocrCapture.appID = "123";
	ocrCapture.count = 5;
	ocrCapture.language = Language.CHS;
	ocrCapture.resolution = 80;
	ocrCapture.serviceID = "999999";
	
	setIdentifyCallBack(identify_callback);

	///////////////////////////////////////////////

	//注册单字签字对象20
	res = testAddSignatureObj(20,keyword,personName,titleName);
	if(!res){
	    console.log("testAddSignatureObj error");
	    return;
	}else{
	
	}

	res = testAddSignatureObj(21,keyword,personName,titleName);
	if(!res){
	  console.log("testAddSignatureObj error");
	  return;
	}else{
	
	}

	res = testAddCommentObj(30,keyword,personName,titleName);
	if(!res){
	    console.log("testAddCommentObj error");
	    return;
	}else{
	
	}

	////////////////////////////////////////////////

	//注册一个单位签章

	 var signer = new Signer("小明","110xxxxxx");
	/**
	 * 使用服务器规则配置签名
	 * @param tid 服务器端生成的配置规则
	 * @constructor
	 */
	var signerRule = new SignRule_Tid("111");
	var cachet_config = new CachetConfig(signer, signerRule, false);

	// res = apiInstance.addCachetObj(cachet_config);
//              ////////////////////////////////////////////////
//
//              if(!res){
//                  alert("addCachetObj error");
//              }else{
//
//              }
	////////////////////////////////////////////////

	//将配置提交
	res = apiInstance.commitConfig();
	
	if(res){
	  console.log("Init ALL 提交配置成功");
	}else{
	  console.log("Init ALL 提交配置失败");
	}

	////////////////////////////////////////////////

}

			function testSignatureStatus()
            {
                console.log("getSignatureStatus :" + apiInstance.getSignatureStatus());
            }

            function testCommentStatus()
            {
                console.log("getCommentStatus :" + apiInstance.getCommentStatus());
            }
            
            function testIsReadyToUpload()
            {
                console.log("testIsReadyToUpload :" + apiInstance.isReadyToUpload());
            }

//生成签名加密数据
			function testGenData()
            {
                var res = document.getElementById('result');

                try
                {
                    res.value = apiInstance.getUploadDataGram();
                    console.log("value"+res.value);
                }
                catch(err)
                {
                    console.log(err);
                }
            }

//弹出签名框签名
function testPopupDialog(imgId,orderNo,keyword,personName,titleName,num){
	//testAnySign('30025162',imgId,keyword,personName,titleName,num);
	testAnySign('30025612',imgId,keyword,personName,titleName,num);
	testSetTemplateData(orderNo);
	//show_agreement(21);
	var context_id = num;
	if(!apiInstance){
	      myAlert("信手书接口没有初始化",'',['确定']);
	      return;
	}
	if(context_id == 21){
	 switch (apiInstance.showSingleSignDialog(context_id))
	  {
		case RESULT_OK:
	//document.getElementById("other").style.display = "none";
			break;
		case EC_API_NOT_INITED:
			myAlert("信手书接口没有初始化",'',['确定'])
			break;
		case EC_WRONG_CONTEXT_ID:
			myAlert("没有配置相应context_id的签字对象",'',['确定'])
			break;
	  }
	}else if(context_id == 20){
	 switch (apiInstance.showSignatureDialog(context_id))
	  {
		case RESULT_OK:
	document.getElementById("other").style.display = "none";
			break;
		case EC_API_NOT_INITED:
			myAlert("信手书接口没有初始化",'',['确定'])
			break;
		case EC_WRONG_CONTEXT_ID:
			myAlert("没有配置相应context_id的签字对象",'',['确定'])
			break;
	  }
	}else if(context_id == 30){
		testCommentDialog(context_id);
	}
}

 //手写识别准确度，范围1-10的整数，数字越小越精确
            ocr_result_num = 2;

//弹出协议框
			var temp_id = "";

            var showAgreement = false; //选择是否弹出告知协议框

            function show_agreement(context_id) {
              if(showAgreement == true) {
                temp_id = context_id;
                document.getElementById("agreement_layer").style.display = "block";
              }
              else {
                testPopupDialog(context_id);
              }
            }

			function agreement_cancel() {
              document.getElementById("agreement_layer").style.display = "none";
            }

            function agreement_ok() {
              document.getElementById("agreement_layer").style.display = "none";
              testPopupDialog(temp_id);
            }
            
			function setIdentifyCallBack(callback){
              if(!apiInstance){
                    myAlert("信手书接口没有初始化",'',['确定']);
                    return;
              }
              apiInstance.setIdentifyCallBack(callback);
            }

//弹出批注签名框
			function testCommentDialog(context_id)
            {
              if(!apiInstance){
                    myAlert("信手书接口没有初始化",'',['确定']);
                    return;
              }
                switch (apiInstance.showCommentDialog(context_id))
                {
                    case RESULT_OK:
                document.getElementById("other").style.display = "none";
                        break;
                    case EC_API_NOT_INITED:
						myAlert("信手书接口没有初始化",'',['确定']);
                        break;
                    case EC_WRONG_CONTEXT_ID:
						myAlert("没有配置相应context_id的签字对象",'',['确定']);
                        break;
                    case EC_COMMENT_ALREADY_SHOW:
						myAlert("批注签名框已弹出，请勿重复操作！",'',['确定']);
                }

            }

//获取签名api版本信息
			function testGetVersion()
            {
                console.log(apiInstance.getVersion());
            }

//获取设备操作系统信息
			function testGetOsInfo()
            {
                console.log(apiInstance.getOSInfo());
                console.log(navigator.userAgent);
                console.log(window.__wxjs_is_wkwebview);
            }
            
//jane
function testAddEvidence(result)
            {
//                MTExMTEx 为原文"111111" base64编码后数据
//                alert(apiInstance.addEvidence("20","MTExMTEx","AMR","2","0"));
                console.log(apiInstance.addEvidence(20,"<html><head><title></title><meta http-equiv='Content-Type' content='text/html; charset=UTF-8' /></head><body><div><div><label>keyword:</label></div><div><label>列名2：</label></div><div><label>列名3：</label></div></div></body></html>",DataFormat.IMAGE_JPEG,BioType.PHOTO_SIGNER_IDENTITY_CARD_BACK,0));
            }

			function CopyText(TextAreaName){
              document.querySelector('#'+TextAreaName).select();
              document.execCommand('copy');
            }
