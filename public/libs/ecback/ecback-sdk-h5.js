!function (t, e) { "object" == typeof exports && "undefined" != typeof module ? e(exports) : "function" == typeof define && define.amd ? define(["exports"], e) : e((t = "undefined" != typeof globalThis ? globalThis : t || self).pako = {}) }(this, (function (t) { "use strict"; function e(t) { let e = t.length; for (; --e >= 0;)t[e] = 0 } const a = 256, s = 286, n = 30, r = 15, i = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0]), _ = new Uint8Array([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13]), l = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7]), h = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]), o = new Array(576); e(o); const d = new Array(60); e(d); const u = new Array(512); e(u); const f = new Array(256); e(f); const c = new Array(29); e(c); const p = new Array(n); function g(t, e, a, s, n) { this.static_tree = t, this.extra_bits = e, this.extra_base = a, this.elems = s, this.max_length = n, this.has_stree = t && t.length } let w, m, b; function y(t, e) { this.dyn_tree = t, this.max_code = 0, this.stat_desc = e } e(p); const v = t => t < 256 ? u[t] : u[256 + (t >>> 7)], z = (t, e) => { t.pending_buf[t.pending++] = 255 & e, t.pending_buf[t.pending++] = e >>> 8 & 255 }, k = (t, e, a) => { t.bi_valid > 16 - a ? (t.bi_buf |= e << t.bi_valid & 65535, z(t, t.bi_buf), t.bi_buf = e >> 16 - t.bi_valid, t.bi_valid += a - 16) : (t.bi_buf |= e << t.bi_valid & 65535, t.bi_valid += a) }, x = (t, e, a) => { k(t, a[2 * e], a[2 * e + 1]) }, A = (t, e) => { let a = 0; do { a |= 1 & t, t >>>= 1, a <<= 1 } while (--e > 0); return a >>> 1 }, E = (t, e, a) => { const s = new Array(16); let n, i, _ = 0; for (n = 1; n <= r; n++)_ = _ + a[n - 1] << 1, s[n] = _; for (i = 0; i <= e; i++) { let e = t[2 * i + 1]; 0 !== e && (t[2 * i] = A(s[e]++, e)) } }, Z = t => { let e; for (e = 0; e < s; e++)t.dyn_ltree[2 * e] = 0; for (e = 0; e < n; e++)t.dyn_dtree[2 * e] = 0; for (e = 0; e < 19; e++)t.bl_tree[2 * e] = 0; t.dyn_ltree[512] = 1, t.opt_len = t.static_len = 0, t.sym_next = t.matches = 0 }, U = t => { t.bi_valid > 8 ? z(t, t.bi_buf) : t.bi_valid > 0 && (t.pending_buf[t.pending++] = t.bi_buf), t.bi_buf = 0, t.bi_valid = 0 }, R = (t, e, a, s) => { const n = 2 * e, r = 2 * a; return t[n] < t[r] || t[n] === t[r] && s[e] <= s[a] }, S = (t, e, a) => { const s = t.heap[a]; let n = a << 1; for (; n <= t.heap_len && (n < t.heap_len && R(e, t.heap[n + 1], t.heap[n], t.depth) && n++, !R(e, s, t.heap[n], t.depth));)t.heap[a] = t.heap[n], a = n, n <<= 1; t.heap[a] = s }, T = (t, e, s) => { let n, r, l, h, o = 0; if (0 !== t.sym_next) do { n = 255 & t.pending_buf[t.sym_buf + o++], n += (255 & t.pending_buf[t.sym_buf + o++]) << 8, r = t.pending_buf[t.sym_buf + o++], 0 === n ? x(t, r, e) : (l = f[r], x(t, l + a + 1, e), h = i[l], 0 !== h && (r -= c[l], k(t, r, h)), n--, l = v(n), x(t, l, s), h = _[l], 0 !== h && (n -= p[l], k(t, n, h))) } while (o < t.sym_next); x(t, 256, e) }, L = (t, e) => { const a = e.dyn_tree, s = e.stat_desc.static_tree, n = e.stat_desc.has_stree, i = e.stat_desc.elems; let _, l, h, o = -1; for (t.heap_len = 0, t.heap_max = 573, _ = 0; _ < i; _++)0 !== a[2 * _] ? (t.heap[++t.heap_len] = o = _, t.depth[_] = 0) : a[2 * _ + 1] = 0; for (; t.heap_len < 2;)h = t.heap[++t.heap_len] = o < 2 ? ++o : 0, a[2 * h] = 1, t.depth[h] = 0, t.opt_len--, n && (t.static_len -= s[2 * h + 1]); for (e.max_code = o, _ = t.heap_len >> 1; _ >= 1; _--)S(t, a, _); h = i; do { _ = t.heap[1], t.heap[1] = t.heap[t.heap_len--], S(t, a, 1), l = t.heap[1], t.heap[--t.heap_max] = _, t.heap[--t.heap_max] = l, a[2 * h] = a[2 * _] + a[2 * l], t.depth[h] = (t.depth[_] >= t.depth[l] ? t.depth[_] : t.depth[l]) + 1, a[2 * _ + 1] = a[2 * l + 1] = h, t.heap[1] = h++, S(t, a, 1) } while (t.heap_len >= 2); t.heap[--t.heap_max] = t.heap[1], ((t, e) => { const a = e.dyn_tree, s = e.max_code, n = e.stat_desc.static_tree, i = e.stat_desc.has_stree, _ = e.stat_desc.extra_bits, l = e.stat_desc.extra_base, h = e.stat_desc.max_length; let o, d, u, f, c, p, g = 0; for (f = 0; f <= r; f++)t.bl_count[f] = 0; for (a[2 * t.heap[t.heap_max] + 1] = 0, o = t.heap_max + 1; o < 573; o++)d = t.heap[o], f = a[2 * a[2 * d + 1] + 1] + 1, f > h && (f = h, g++), a[2 * d + 1] = f, d > s || (t.bl_count[f]++, c = 0, d >= l && (c = _[d - l]), p = a[2 * d], t.opt_len += p * (f + c), i && (t.static_len += p * (n[2 * d + 1] + c))); if (0 !== g) { do { for (f = h - 1; 0 === t.bl_count[f];)f--; t.bl_count[f]--, t.bl_count[f + 1] += 2, t.bl_count[h]--, g -= 2 } while (g > 0); for (f = h; 0 !== f; f--)for (d = t.bl_count[f]; 0 !== d;)u = t.heap[--o], u > s || (a[2 * u + 1] !== f && (t.opt_len += (f - a[2 * u + 1]) * a[2 * u], a[2 * u + 1] = f), d--) } })(t, e), E(a, o, t.bl_count) }, F = (t, e, a) => { let s, n, r = -1, i = e[1], _ = 0, l = 7, h = 4; for (0 === i && (l = 138, h = 3), e[2 * (a + 1) + 1] = 65535, s = 0; s <= a; s++)n = i, i = e[2 * (s + 1) + 1], ++_ < l && n === i || (_ < h ? t.bl_tree[2 * n] += _ : 0 !== n ? (n !== r && t.bl_tree[2 * n]++, t.bl_tree[32]++) : _ <= 10 ? t.bl_tree[34]++ : t.bl_tree[36]++, _ = 0, r = n, 0 === i ? (l = 138, h = 3) : n === i ? (l = 6, h = 3) : (l = 7, h = 4)) }, O = (t, e, a) => { let s, n, r = -1, i = e[1], _ = 0, l = 7, h = 4; for (0 === i && (l = 138, h = 3), s = 0; s <= a; s++)if (n = i, i = e[2 * (s + 1) + 1], !(++_ < l && n === i)) { if (_ < h) do { x(t, n, t.bl_tree) } while (0 != --_); else 0 !== n ? (n !== r && (x(t, n, t.bl_tree), _--), x(t, 16, t.bl_tree), k(t, _ - 3, 2)) : _ <= 10 ? (x(t, 17, t.bl_tree), k(t, _ - 3, 3)) : (x(t, 18, t.bl_tree), k(t, _ - 11, 7)); _ = 0, r = n, 0 === i ? (l = 138, h = 3) : n === i ? (l = 6, h = 3) : (l = 7, h = 4) } }; let D = !1; const N = (t, e, a, s) => { k(t, 0 + (s ? 1 : 0), 3), U(t), z(t, a), z(t, ~a), a && t.pending_buf.set(t.window.subarray(e, e + a), t.pending), t.pending += a }; var I = (t, e, s, n) => { let r, i, _ = 0; t.level > 0 ? (2 === t.strm.data_type && (t.strm.data_type = (t => { let e, s = 4093624447; for (e = 0; e <= 31; e++, s >>>= 1)if (1 & s && 0 !== t.dyn_ltree[2 * e]) return 0; if (0 !== t.dyn_ltree[18] || 0 !== t.dyn_ltree[20] || 0 !== t.dyn_ltree[26]) return 1; for (e = 32; e < a; e++)if (0 !== t.dyn_ltree[2 * e]) return 1; return 0 })(t)), L(t, t.l_desc), L(t, t.d_desc), _ = (t => { let e; for (F(t, t.dyn_ltree, t.l_desc.max_code), F(t, t.dyn_dtree, t.d_desc.max_code), L(t, t.bl_desc), e = 18; e >= 3 && 0 === t.bl_tree[2 * h[e] + 1]; e--); return t.opt_len += 3 * (e + 1) + 5 + 5 + 4, e })(t), r = t.opt_len + 3 + 7 >>> 3, i = t.static_len + 3 + 7 >>> 3, i <= r && (r = i)) : r = i = s + 5, s + 4 <= r && -1 !== e ? N(t, e, s, n) : 4 === t.strategy || i === r ? (k(t, 2 + (n ? 1 : 0), 3), T(t, o, d)) : (k(t, 4 + (n ? 1 : 0), 3), ((t, e, a, s) => { let n; for (k(t, e - 257, 5), k(t, a - 1, 5), k(t, s - 4, 4), n = 0; n < s; n++)k(t, t.bl_tree[2 * h[n] + 1], 3); O(t, t.dyn_ltree, e - 1), O(t, t.dyn_dtree, a - 1) })(t, t.l_desc.max_code + 1, t.d_desc.max_code + 1, _ + 1), T(t, t.dyn_ltree, t.dyn_dtree)), Z(t), n && U(t) }, C = { _tr_init: t => { D || ((() => { let t, e, a, h, y; const v = new Array(16); for (a = 0, h = 0; h < 28; h++)for (c[h] = a, t = 0; t < 1 << i[h]; t++)f[a++] = h; for (f[a - 1] = h, y = 0, h = 0; h < 16; h++)for (p[h] = y, t = 0; t < 1 << _[h]; t++)u[y++] = h; for (y >>= 7; h < n; h++)for (p[h] = y << 7, t = 0; t < 1 << _[h] - 7; t++)u[256 + y++] = h; for (e = 0; e <= r; e++)v[e] = 0; for (t = 0; t <= 143;)o[2 * t + 1] = 8, t++, v[8]++; for (; t <= 255;)o[2 * t + 1] = 9, t++, v[9]++; for (; t <= 279;)o[2 * t + 1] = 7, t++, v[7]++; for (; t <= 287;)o[2 * t + 1] = 8, t++, v[8]++; for (E(o, 287, v), t = 0; t < n; t++)d[2 * t + 1] = 5, d[2 * t] = A(t, 5); w = new g(o, i, 257, s, r), m = new g(d, _, 0, n, r), b = new g(new Array(0), l, 0, 19, 7) })(), D = !0), t.l_desc = new y(t.dyn_ltree, w), t.d_desc = new y(t.dyn_dtree, m), t.bl_desc = new y(t.bl_tree, b), t.bi_buf = 0, t.bi_valid = 0, Z(t) }, _tr_stored_block: N, _tr_flush_block: I, _tr_tally: (t, e, s) => (t.pending_buf[t.sym_buf + t.sym_next++] = e, t.pending_buf[t.sym_buf + t.sym_next++] = e >> 8, t.pending_buf[t.sym_buf + t.sym_next++] = s, 0 === e ? t.dyn_ltree[2 * s]++ : (t.matches++, e--, t.dyn_ltree[2 * (f[s] + a + 1)]++, t.dyn_dtree[2 * v(e)]++), t.sym_next === t.sym_end), _tr_align: t => { k(t, 2, 3), x(t, 256, o), (t => { 16 === t.bi_valid ? (z(t, t.bi_buf), t.bi_buf = 0, t.bi_valid = 0) : t.bi_valid >= 8 && (t.pending_buf[t.pending++] = 255 & t.bi_buf, t.bi_buf >>= 8, t.bi_valid -= 8) })(t) } }; var B = (t, e, a, s) => { let n = 65535 & t | 0, r = t >>> 16 & 65535 | 0, i = 0; for (; 0 !== a;) { i = a > 2e3 ? 2e3 : a, a -= i; do { n = n + e[s++] | 0, r = r + n | 0 } while (--i); n %= 65521, r %= 65521 } return n | r << 16 | 0 }; const H = new Uint32Array((() => { let t, e = []; for (var a = 0; a < 256; a++) { t = a; for (var s = 0; s < 8; s++)t = 1 & t ? 3988292384 ^ t >>> 1 : t >>> 1; e[a] = t } return e })()); var M = (t, e, a, s) => { const n = H, r = s + a; t ^= -1; for (let a = s; a < r; a++)t = t >>> 8 ^ n[255 & (t ^ e[a])]; return -1 ^ t }, P = { 2: "need dictionary", 1: "stream end", 0: "", "-1": "file error", "-2": "stream error", "-3": "data error", "-4": "insufficient memory", "-5": "buffer error", "-6": "incompatible version" }, j = { Z_NO_FLUSH: 0, Z_PARTIAL_FLUSH: 1, Z_SYNC_FLUSH: 2, Z_FULL_FLUSH: 3, Z_FINISH: 4, Z_BLOCK: 5, Z_TREES: 6, Z_OK: 0, Z_STREAM_END: 1, Z_NEED_DICT: 2, Z_ERRNO: -1, Z_STREAM_ERROR: -2, Z_DATA_ERROR: -3, Z_MEM_ERROR: -4, Z_BUF_ERROR: -5, Z_NO_COMPRESSION: 0, Z_BEST_SPEED: 1, Z_BEST_COMPRESSION: 9, Z_DEFAULT_COMPRESSION: -1, Z_FILTERED: 1, Z_HUFFMAN_ONLY: 2, Z_RLE: 3, Z_FIXED: 4, Z_DEFAULT_STRATEGY: 0, Z_BINARY: 0, Z_TEXT: 1, Z_UNKNOWN: 2, Z_DEFLATED: 8 }; const { _tr_init: K, _tr_stored_block: Y, _tr_flush_block: G, _tr_tally: X, _tr_align: W } = C, { Z_NO_FLUSH: q, Z_PARTIAL_FLUSH: J, Z_FULL_FLUSH: Q, Z_FINISH: V, Z_BLOCK: $, Z_OK: tt, Z_STREAM_END: et, Z_STREAM_ERROR: at, Z_DATA_ERROR: st, Z_BUF_ERROR: nt, Z_DEFAULT_COMPRESSION: rt, Z_FILTERED: it, Z_HUFFMAN_ONLY: _t, Z_RLE: lt, Z_FIXED: ht, Z_DEFAULT_STRATEGY: ot, Z_UNKNOWN: dt, Z_DEFLATED: ut } = j, ft = 258, ct = 262, pt = 42, gt = 113, wt = 666, mt = (t, e) => (t.msg = P[e], e), bt = t => 2 * t - (t > 4 ? 9 : 0), yt = t => { let e = t.length; for (; --e >= 0;)t[e] = 0 }, vt = t => { let e, a, s, n = t.w_size; e = t.hash_size, s = e; do { a = t.head[--s], t.head[s] = a >= n ? a - n : 0 } while (--e); e = n, s = e; do { a = t.prev[--s], t.prev[s] = a >= n ? a - n : 0 } while (--e) }; let zt = (t, e, a) => (e << t.hash_shift ^ a) & t.hash_mask; const kt = t => { const e = t.state; let a = e.pending; a > t.avail_out && (a = t.avail_out), 0 !== a && (t.output.set(e.pending_buf.subarray(e.pending_out, e.pending_out + a), t.next_out), t.next_out += a, e.pending_out += a, t.total_out += a, t.avail_out -= a, e.pending -= a, 0 === e.pending && (e.pending_out = 0)) }, xt = (t, e) => { G(t, t.block_start >= 0 ? t.block_start : -1, t.strstart - t.block_start, e), t.block_start = t.strstart, kt(t.strm) }, At = (t, e) => { t.pending_buf[t.pending++] = e }, Et = (t, e) => { t.pending_buf[t.pending++] = e >>> 8 & 255, t.pending_buf[t.pending++] = 255 & e }, Zt = (t, e, a, s) => { let n = t.avail_in; return n > s && (n = s), 0 === n ? 0 : (t.avail_in -= n, e.set(t.input.subarray(t.next_in, t.next_in + n), a), 1 === t.state.wrap ? t.adler = B(t.adler, e, n, a) : 2 === t.state.wrap && (t.adler = M(t.adler, e, n, a)), t.next_in += n, t.total_in += n, n) }, Ut = (t, e) => { let a, s, n = t.max_chain_length, r = t.strstart, i = t.prev_length, _ = t.nice_match; const l = t.strstart > t.w_size - ct ? t.strstart - (t.w_size - ct) : 0, h = t.window, o = t.w_mask, d = t.prev, u = t.strstart + ft; let f = h[r + i - 1], c = h[r + i]; t.prev_length >= t.good_match && (n >>= 2), _ > t.lookahead && (_ = t.lookahead); do { if (a = e, h[a + i] === c && h[a + i - 1] === f && h[a] === h[r] && h[++a] === h[r + 1]) { r += 2, a++; do { } while (h[++r] === h[++a] && h[++r] === h[++a] && h[++r] === h[++a] && h[++r] === h[++a] && h[++r] === h[++a] && h[++r] === h[++a] && h[++r] === h[++a] && h[++r] === h[++a] && r < u); if (s = ft - (u - r), r = u - ft, s > i) { if (t.match_start = e, i = s, s >= _) break; f = h[r + i - 1], c = h[r + i] } } } while ((e = d[e & o]) > l && 0 != --n); return i <= t.lookahead ? i : t.lookahead }, Rt = t => { const e = t.w_size; let a, s, n; do { if (s = t.window_size - t.lookahead - t.strstart, t.strstart >= e + (e - ct) && (t.window.set(t.window.subarray(e, e + e - s), 0), t.match_start -= e, t.strstart -= e, t.block_start -= e, t.insert > t.strstart && (t.insert = t.strstart), vt(t), s += e), 0 === t.strm.avail_in) break; if (a = Zt(t.strm, t.window, t.strstart + t.lookahead, s), t.lookahead += a, t.lookahead + t.insert >= 3) for (n = t.strstart - t.insert, t.ins_h = t.window[n], t.ins_h = zt(t, t.ins_h, t.window[n + 1]); t.insert && (t.ins_h = zt(t, t.ins_h, t.window[n + 3 - 1]), t.prev[n & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = n, n++, t.insert--, !(t.lookahead + t.insert < 3));); } while (t.lookahead < ct && 0 !== t.strm.avail_in) }, St = (t, e) => { let a, s, n, r = t.pending_buf_size - 5 > t.w_size ? t.w_size : t.pending_buf_size - 5, i = 0, _ = t.strm.avail_in; do { if (a = 65535, n = t.bi_valid + 42 >> 3, t.strm.avail_out < n) break; if (n = t.strm.avail_out - n, s = t.strstart - t.block_start, a > s + t.strm.avail_in && (a = s + t.strm.avail_in), a > n && (a = n), a < r && (0 === a && e !== V || e === q || a !== s + t.strm.avail_in)) break; i = e === V && a === s + t.strm.avail_in ? 1 : 0, Y(t, 0, 0, i), t.pending_buf[t.pending - 4] = a, t.pending_buf[t.pending - 3] = a >> 8, t.pending_buf[t.pending - 2] = ~a, t.pending_buf[t.pending - 1] = ~a >> 8, kt(t.strm), s && (s > a && (s = a), t.strm.output.set(t.window.subarray(t.block_start, t.block_start + s), t.strm.next_out), t.strm.next_out += s, t.strm.avail_out -= s, t.strm.total_out += s, t.block_start += s, a -= s), a && (Zt(t.strm, t.strm.output, t.strm.next_out, a), t.strm.next_out += a, t.strm.avail_out -= a, t.strm.total_out += a) } while (0 === i); return _ -= t.strm.avail_in, _ && (_ >= t.w_size ? (t.matches = 2, t.window.set(t.strm.input.subarray(t.strm.next_in - t.w_size, t.strm.next_in), 0), t.strstart = t.w_size, t.insert = t.strstart) : (t.window_size - t.strstart <= _ && (t.strstart -= t.w_size, t.window.set(t.window.subarray(t.w_size, t.w_size + t.strstart), 0), t.matches < 2 && t.matches++, t.insert > t.strstart && (t.insert = t.strstart)), t.window.set(t.strm.input.subarray(t.strm.next_in - _, t.strm.next_in), t.strstart), t.strstart += _, t.insert += _ > t.w_size - t.insert ? t.w_size - t.insert : _), t.block_start = t.strstart), t.high_water < t.strstart && (t.high_water = t.strstart), i ? 4 : e !== q && e !== V && 0 === t.strm.avail_in && t.strstart === t.block_start ? 2 : (n = t.window_size - t.strstart, t.strm.avail_in > n && t.block_start >= t.w_size && (t.block_start -= t.w_size, t.strstart -= t.w_size, t.window.set(t.window.subarray(t.w_size, t.w_size + t.strstart), 0), t.matches < 2 && t.matches++, n += t.w_size, t.insert > t.strstart && (t.insert = t.strstart)), n > t.strm.avail_in && (n = t.strm.avail_in), n && (Zt(t.strm, t.window, t.strstart, n), t.strstart += n, t.insert += n > t.w_size - t.insert ? t.w_size - t.insert : n), t.high_water < t.strstart && (t.high_water = t.strstart), n = t.bi_valid + 42 >> 3, n = t.pending_buf_size - n > 65535 ? 65535 : t.pending_buf_size - n, r = n > t.w_size ? t.w_size : n, s = t.strstart - t.block_start, (s >= r || (s || e === V) && e !== q && 0 === t.strm.avail_in && s <= n) && (a = s > n ? n : s, i = e === V && 0 === t.strm.avail_in && a === s ? 1 : 0, Y(t, t.block_start, a, i), t.block_start += a, kt(t.strm)), i ? 3 : 1) }, Tt = (t, e) => { let a, s; for (; ;) { if (t.lookahead < ct) { if (Rt(t), t.lookahead < ct && e === q) return 1; if (0 === t.lookahead) break } if (a = 0, t.lookahead >= 3 && (t.ins_h = zt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart), 0 !== a && t.strstart - a <= t.w_size - ct && (t.match_length = Ut(t, a)), t.match_length >= 3) if (s = X(t, t.strstart - t.match_start, t.match_length - 3), t.lookahead -= t.match_length, t.match_length <= t.max_lazy_match && t.lookahead >= 3) { t.match_length--; do { t.strstart++, t.ins_h = zt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart } while (0 != --t.match_length); t.strstart++ } else t.strstart += t.match_length, t.match_length = 0, t.ins_h = t.window[t.strstart], t.ins_h = zt(t, t.ins_h, t.window[t.strstart + 1]); else s = X(t, 0, t.window[t.strstart]), t.lookahead--, t.strstart++; if (s && (xt(t, !1), 0 === t.strm.avail_out)) return 1 } return t.insert = t.strstart < 2 ? t.strstart : 2, e === V ? (xt(t, !0), 0 === t.strm.avail_out ? 3 : 4) : t.sym_next && (xt(t, !1), 0 === t.strm.avail_out) ? 1 : 2 }, Lt = (t, e) => { let a, s, n; for (; ;) { if (t.lookahead < ct) { if (Rt(t), t.lookahead < ct && e === q) return 1; if (0 === t.lookahead) break } if (a = 0, t.lookahead >= 3 && (t.ins_h = zt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart), t.prev_length = t.match_length, t.prev_match = t.match_start, t.match_length = 2, 0 !== a && t.prev_length < t.max_lazy_match && t.strstart - a <= t.w_size - ct && (t.match_length = Ut(t, a), t.match_length <= 5 && (t.strategy === it || 3 === t.match_length && t.strstart - t.match_start > 4096) && (t.match_length = 2)), t.prev_length >= 3 && t.match_length <= t.prev_length) { n = t.strstart + t.lookahead - 3, s = X(t, t.strstart - 1 - t.prev_match, t.prev_length - 3), t.lookahead -= t.prev_length - 1, t.prev_length -= 2; do { ++t.strstart <= n && (t.ins_h = zt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart) } while (0 != --t.prev_length); if (t.match_available = 0, t.match_length = 2, t.strstart++, s && (xt(t, !1), 0 === t.strm.avail_out)) return 1 } else if (t.match_available) { if (s = X(t, 0, t.window[t.strstart - 1]), s && xt(t, !1), t.strstart++, t.lookahead--, 0 === t.strm.avail_out) return 1 } else t.match_available = 1, t.strstart++, t.lookahead-- } return t.match_available && (s = X(t, 0, t.window[t.strstart - 1]), t.match_available = 0), t.insert = t.strstart < 2 ? t.strstart : 2, e === V ? (xt(t, !0), 0 === t.strm.avail_out ? 3 : 4) : t.sym_next && (xt(t, !1), 0 === t.strm.avail_out) ? 1 : 2 }; function Ft(t, e, a, s, n) { this.good_length = t, this.max_lazy = e, this.nice_length = a, this.max_chain = s, this.func = n } const Ot = [new Ft(0, 0, 0, 0, St), new Ft(4, 4, 8, 4, Tt), new Ft(4, 5, 16, 8, Tt), new Ft(4, 6, 32, 32, Tt), new Ft(4, 4, 16, 16, Lt), new Ft(8, 16, 32, 32, Lt), new Ft(8, 16, 128, 128, Lt), new Ft(8, 32, 128, 256, Lt), new Ft(32, 128, 258, 1024, Lt), new Ft(32, 258, 258, 4096, Lt)]; function Dt() { this.strm = null, this.status = 0, this.pending_buf = null, this.pending_buf_size = 0, this.pending_out = 0, this.pending = 0, this.wrap = 0, this.gzhead = null, this.gzindex = 0, this.method = ut, this.last_flush = -1, this.w_size = 0, this.w_bits = 0, this.w_mask = 0, this.window = null, this.window_size = 0, this.prev = null, this.head = null, this.ins_h = 0, this.hash_size = 0, this.hash_bits = 0, this.hash_mask = 0, this.hash_shift = 0, this.block_start = 0, this.match_length = 0, this.prev_match = 0, this.match_available = 0, this.strstart = 0, this.match_start = 0, this.lookahead = 0, this.prev_length = 0, this.max_chain_length = 0, this.max_lazy_match = 0, this.level = 0, this.strategy = 0, this.good_match = 0, this.nice_match = 0, this.dyn_ltree = new Uint16Array(1146), this.dyn_dtree = new Uint16Array(122), this.bl_tree = new Uint16Array(78), yt(this.dyn_ltree), yt(this.dyn_dtree), yt(this.bl_tree), this.l_desc = null, this.d_desc = null, this.bl_desc = null, this.bl_count = new Uint16Array(16), this.heap = new Uint16Array(573), yt(this.heap), this.heap_len = 0, this.heap_max = 0, this.depth = new Uint16Array(573), yt(this.depth), this.sym_buf = 0, this.lit_bufsize = 0, this.sym_next = 0, this.sym_end = 0, this.opt_len = 0, this.static_len = 0, this.matches = 0, this.insert = 0, this.bi_buf = 0, this.bi_valid = 0 } const Nt = t => { if (!t) return 1; const e = t.state; return !e || e.strm !== t || e.status !== pt && 57 !== e.status && 69 !== e.status && 73 !== e.status && 91 !== e.status && 103 !== e.status && e.status !== gt && e.status !== wt ? 1 : 0 }, It = t => { if (Nt(t)) return mt(t, at); t.total_in = t.total_out = 0, t.data_type = dt; const e = t.state; return e.pending = 0, e.pending_out = 0, e.wrap < 0 && (e.wrap = -e.wrap), e.status = 2 === e.wrap ? 57 : e.wrap ? pt : gt, t.adler = 2 === e.wrap ? 0 : 1, e.last_flush = -2, K(e), tt }, Ct = t => { const e = It(t); var a; return e === tt && ((a = t.state).window_size = 2 * a.w_size, yt(a.head), a.max_lazy_match = Ot[a.level].max_lazy, a.good_match = Ot[a.level].good_length, a.nice_match = Ot[a.level].nice_length, a.max_chain_length = Ot[a.level].max_chain, a.strstart = 0, a.block_start = 0, a.lookahead = 0, a.insert = 0, a.match_length = a.prev_length = 2, a.match_available = 0, a.ins_h = 0), e }, Bt = (t, e, a, s, n, r) => { if (!t) return at; let i = 1; if (e === rt && (e = 6), s < 0 ? (i = 0, s = -s) : s > 15 && (i = 2, s -= 16), n < 1 || n > 9 || a !== ut || s < 8 || s > 15 || e < 0 || e > 9 || r < 0 || r > ht || 8 === s && 1 !== i) return mt(t, at); 8 === s && (s = 9); const _ = new Dt; return t.state = _, _.strm = t, _.status = pt, _.wrap = i, _.gzhead = null, _.w_bits = s, _.w_size = 1 << _.w_bits, _.w_mask = _.w_size - 1, _.hash_bits = n + 7, _.hash_size = 1 << _.hash_bits, _.hash_mask = _.hash_size - 1, _.hash_shift = ~~((_.hash_bits + 3 - 1) / 3), _.window = new Uint8Array(2 * _.w_size), _.head = new Uint16Array(_.hash_size), _.prev = new Uint16Array(_.w_size), _.lit_bufsize = 1 << n + 6, _.pending_buf_size = 4 * _.lit_bufsize, _.pending_buf = new Uint8Array(_.pending_buf_size), _.sym_buf = _.lit_bufsize, _.sym_end = 3 * (_.lit_bufsize - 1), _.level = e, _.strategy = r, _.method = a, Ct(t) }; var Ht = { deflateInit: (t, e) => Bt(t, e, ut, 15, 8, ot), deflateInit2: Bt, deflateReset: Ct, deflateResetKeep: It, deflateSetHeader: (t, e) => Nt(t) || 2 !== t.state.wrap ? at : (t.state.gzhead = e, tt), deflate: (t, e) => { if (Nt(t) || e > $ || e < 0) return t ? mt(t, at) : at; const a = t.state; if (!t.output || 0 !== t.avail_in && !t.input || a.status === wt && e !== V) return mt(t, 0 === t.avail_out ? nt : at); const s = a.last_flush; if (a.last_flush = e, 0 !== a.pending) { if (kt(t), 0 === t.avail_out) return a.last_flush = -1, tt } else if (0 === t.avail_in && bt(e) <= bt(s) && e !== V) return mt(t, nt); if (a.status === wt && 0 !== t.avail_in) return mt(t, nt); if (a.status === pt && 0 === a.wrap && (a.status = gt), a.status === pt) { let e = ut + (a.w_bits - 8 << 4) << 8, s = -1; if (s = a.strategy >= _t || a.level < 2 ? 0 : a.level < 6 ? 1 : 6 === a.level ? 2 : 3, e |= s << 6, 0 !== a.strstart && (e |= 32), e += 31 - e % 31, Et(a, e), 0 !== a.strstart && (Et(a, t.adler >>> 16), Et(a, 65535 & t.adler)), t.adler = 1, a.status = gt, kt(t), 0 !== a.pending) return a.last_flush = -1, tt } if (57 === a.status) if (t.adler = 0, At(a, 31), At(a, 139), At(a, 8), a.gzhead) At(a, (a.gzhead.text ? 1 : 0) + (a.gzhead.hcrc ? 2 : 0) + (a.gzhead.extra ? 4 : 0) + (a.gzhead.name ? 8 : 0) + (a.gzhead.comment ? 16 : 0)), At(a, 255 & a.gzhead.time), At(a, a.gzhead.time >> 8 & 255), At(a, a.gzhead.time >> 16 & 255), At(a, a.gzhead.time >> 24 & 255), At(a, 9 === a.level ? 2 : a.strategy >= _t || a.level < 2 ? 4 : 0), At(a, 255 & a.gzhead.os), a.gzhead.extra && a.gzhead.extra.length && (At(a, 255 & a.gzhead.extra.length), At(a, a.gzhead.extra.length >> 8 & 255)), a.gzhead.hcrc && (t.adler = M(t.adler, a.pending_buf, a.pending, 0)), a.gzindex = 0, a.status = 69; else if (At(a, 0), At(a, 0), At(a, 0), At(a, 0), At(a, 0), At(a, 9 === a.level ? 2 : a.strategy >= _t || a.level < 2 ? 4 : 0), At(a, 3), a.status = gt, kt(t), 0 !== a.pending) return a.last_flush = -1, tt; if (69 === a.status) { if (a.gzhead.extra) { let e = a.pending, s = (65535 & a.gzhead.extra.length) - a.gzindex; for (; a.pending + s > a.pending_buf_size;) { let n = a.pending_buf_size - a.pending; if (a.pending_buf.set(a.gzhead.extra.subarray(a.gzindex, a.gzindex + n), a.pending), a.pending = a.pending_buf_size, a.gzhead.hcrc && a.pending > e && (t.adler = M(t.adler, a.pending_buf, a.pending - e, e)), a.gzindex += n, kt(t), 0 !== a.pending) return a.last_flush = -1, tt; e = 0, s -= n } let n = new Uint8Array(a.gzhead.extra); a.pending_buf.set(n.subarray(a.gzindex, a.gzindex + s), a.pending), a.pending += s, a.gzhead.hcrc && a.pending > e && (t.adler = M(t.adler, a.pending_buf, a.pending - e, e)), a.gzindex = 0 } a.status = 73 } if (73 === a.status) { if (a.gzhead.name) { let e, s = a.pending; do { if (a.pending === a.pending_buf_size) { if (a.gzhead.hcrc && a.pending > s && (t.adler = M(t.adler, a.pending_buf, a.pending - s, s)), kt(t), 0 !== a.pending) return a.last_flush = -1, tt; s = 0 } e = a.gzindex < a.gzhead.name.length ? 255 & a.gzhead.name.charCodeAt(a.gzindex++) : 0, At(a, e) } while (0 !== e); a.gzhead.hcrc && a.pending > s && (t.adler = M(t.adler, a.pending_buf, a.pending - s, s)), a.gzindex = 0 } a.status = 91 } if (91 === a.status) { if (a.gzhead.comment) { let e, s = a.pending; do { if (a.pending === a.pending_buf_size) { if (a.gzhead.hcrc && a.pending > s && (t.adler = M(t.adler, a.pending_buf, a.pending - s, s)), kt(t), 0 !== a.pending) return a.last_flush = -1, tt; s = 0 } e = a.gzindex < a.gzhead.comment.length ? 255 & a.gzhead.comment.charCodeAt(a.gzindex++) : 0, At(a, e) } while (0 !== e); a.gzhead.hcrc && a.pending > s && (t.adler = M(t.adler, a.pending_buf, a.pending - s, s)) } a.status = 103 } if (103 === a.status) { if (a.gzhead.hcrc) { if (a.pending + 2 > a.pending_buf_size && (kt(t), 0 !== a.pending)) return a.last_flush = -1, tt; At(a, 255 & t.adler), At(a, t.adler >> 8 & 255), t.adler = 0 } if (a.status = gt, kt(t), 0 !== a.pending) return a.last_flush = -1, tt } if (0 !== t.avail_in || 0 !== a.lookahead || e !== q && a.status !== wt) { let s = 0 === a.level ? St(a, e) : a.strategy === _t ? ((t, e) => { let a; for (; ;) { if (0 === t.lookahead && (Rt(t), 0 === t.lookahead)) { if (e === q) return 1; break } if (t.match_length = 0, a = X(t, 0, t.window[t.strstart]), t.lookahead--, t.strstart++, a && (xt(t, !1), 0 === t.strm.avail_out)) return 1 } return t.insert = 0, e === V ? (xt(t, !0), 0 === t.strm.avail_out ? 3 : 4) : t.sym_next && (xt(t, !1), 0 === t.strm.avail_out) ? 1 : 2 })(a, e) : a.strategy === lt ? ((t, e) => { let a, s, n, r; const i = t.window; for (; ;) { if (t.lookahead <= ft) { if (Rt(t), t.lookahead <= ft && e === q) return 1; if (0 === t.lookahead) break } if (t.match_length = 0, t.lookahead >= 3 && t.strstart > 0 && (n = t.strstart - 1, s = i[n], s === i[++n] && s === i[++n] && s === i[++n])) { r = t.strstart + ft; do { } while (s === i[++n] && s === i[++n] && s === i[++n] && s === i[++n] && s === i[++n] && s === i[++n] && s === i[++n] && s === i[++n] && n < r); t.match_length = ft - (r - n), t.match_length > t.lookahead && (t.match_length = t.lookahead) } if (t.match_length >= 3 ? (a = X(t, 1, t.match_length - 3), t.lookahead -= t.match_length, t.strstart += t.match_length, t.match_length = 0) : (a = X(t, 0, t.window[t.strstart]), t.lookahead--, t.strstart++), a && (xt(t, !1), 0 === t.strm.avail_out)) return 1 } return t.insert = 0, e === V ? (xt(t, !0), 0 === t.strm.avail_out ? 3 : 4) : t.sym_next && (xt(t, !1), 0 === t.strm.avail_out) ? 1 : 2 })(a, e) : Ot[a.level].func(a, e); if (3 !== s && 4 !== s || (a.status = wt), 1 === s || 3 === s) return 0 === t.avail_out && (a.last_flush = -1), tt; if (2 === s && (e === J ? W(a) : e !== $ && (Y(a, 0, 0, !1), e === Q && (yt(a.head), 0 === a.lookahead && (a.strstart = 0, a.block_start = 0, a.insert = 0))), kt(t), 0 === t.avail_out)) return a.last_flush = -1, tt } return e !== V ? tt : a.wrap <= 0 ? et : (2 === a.wrap ? (At(a, 255 & t.adler), At(a, t.adler >> 8 & 255), At(a, t.adler >> 16 & 255), At(a, t.adler >> 24 & 255), At(a, 255 & t.total_in), At(a, t.total_in >> 8 & 255), At(a, t.total_in >> 16 & 255), At(a, t.total_in >> 24 & 255)) : (Et(a, t.adler >>> 16), Et(a, 65535 & t.adler)), kt(t), a.wrap > 0 && (a.wrap = -a.wrap), 0 !== a.pending ? tt : et) }, deflateEnd: t => { if (Nt(t)) return at; const e = t.state.status; return t.state = null, e === gt ? mt(t, st) : tt }, deflateSetDictionary: (t, e) => { let a = e.length; if (Nt(t)) return at; const s = t.state, n = s.wrap; if (2 === n || 1 === n && s.status !== pt || s.lookahead) return at; if (1 === n && (t.adler = B(t.adler, e, a, 0)), s.wrap = 0, a >= s.w_size) { 0 === n && (yt(s.head), s.strstart = 0, s.block_start = 0, s.insert = 0); let t = new Uint8Array(s.w_size); t.set(e.subarray(a - s.w_size, a), 0), e = t, a = s.w_size } const r = t.avail_in, i = t.next_in, _ = t.input; for (t.avail_in = a, t.next_in = 0, t.input = e, Rt(s); s.lookahead >= 3;) { let t = s.strstart, e = s.lookahead - 2; do { s.ins_h = zt(s, s.ins_h, s.window[t + 3 - 1]), s.prev[t & s.w_mask] = s.head[s.ins_h], s.head[s.ins_h] = t, t++ } while (--e); s.strstart = t, s.lookahead = 2, Rt(s) } return s.strstart += s.lookahead, s.block_start = s.strstart, s.insert = s.lookahead, s.lookahead = 0, s.match_length = s.prev_length = 2, s.match_available = 0, t.next_in = i, t.input = _, t.avail_in = r, s.wrap = n, tt }, deflateInfo: "pako deflate (from Nodeca project)" }; const Mt = (t, e) => Object.prototype.hasOwnProperty.call(t, e); var Pt = function (t) { const e = Array.prototype.slice.call(arguments, 1); for (; e.length;) { const a = e.shift(); if (a) { if ("object" != typeof a) throw new TypeError(a + "must be non-object"); for (const e in a) Mt(a, e) && (t[e] = a[e]) } } return t }, jt = t => { let e = 0; for (let a = 0, s = t.length; a < s; a++)e += t[a].length; const a = new Uint8Array(e); for (let e = 0, s = 0, n = t.length; e < n; e++) { let n = t[e]; a.set(n, s), s += n.length } return a }; let Kt = !0; try { String.fromCharCode.apply(null, new Uint8Array(1)) } catch (t) { Kt = !1 } const Yt = new Uint8Array(256); for (let t = 0; t < 256; t++)Yt[t] = t >= 252 ? 6 : t >= 248 ? 5 : t >= 240 ? 4 : t >= 224 ? 3 : t >= 192 ? 2 : 1; Yt[254] = Yt[254] = 1; var Gt = t => { if ("function" == typeof TextEncoder && TextEncoder.prototype.encode) return (new TextEncoder).encode(t); let e, a, s, n, r, i = t.length, _ = 0; for (n = 0; n < i; n++)a = t.charCodeAt(n), 55296 == (64512 & a) && n + 1 < i && (s = t.charCodeAt(n + 1), 56320 == (64512 & s) && (a = 65536 + (a - 55296 << 10) + (s - 56320), n++)), _ += a < 128 ? 1 : a < 2048 ? 2 : a < 65536 ? 3 : 4; for (e = new Uint8Array(_), r = 0, n = 0; r < _; n++)a = t.charCodeAt(n), 55296 == (64512 & a) && n + 1 < i && (s = t.charCodeAt(n + 1), 56320 == (64512 & s) && (a = 65536 + (a - 55296 << 10) + (s - 56320), n++)), a < 128 ? e[r++] = a : a < 2048 ? (e[r++] = 192 | a >>> 6, e[r++] = 128 | 63 & a) : a < 65536 ? (e[r++] = 224 | a >>> 12, e[r++] = 128 | a >>> 6 & 63, e[r++] = 128 | 63 & a) : (e[r++] = 240 | a >>> 18, e[r++] = 128 | a >>> 12 & 63, e[r++] = 128 | a >>> 6 & 63, e[r++] = 128 | 63 & a); return e }; var Xt = function () { this.input = null, this.next_in = 0, this.avail_in = 0, this.total_in = 0, this.output = null, this.next_out = 0, this.avail_out = 0, this.total_out = 0, this.msg = "", this.state = null, this.data_type = 2, this.adler = 0 }; const Wt = Object.prototype.toString, { Z_NO_FLUSH: qt, Z_SYNC_FLUSH: Jt, Z_FULL_FLUSH: Qt, Z_FINISH: Vt, Z_OK: $t, Z_STREAM_END: te, Z_DEFAULT_COMPRESSION: ee, Z_DEFAULT_STRATEGY: ae, Z_DEFLATED: se } = j; function ne(t) { this.options = Pt({ level: ee, method: se, chunkSize: 16384, windowBits: 15, memLevel: 8, strategy: ae }, t || {}); let e = this.options; e.raw && e.windowBits > 0 ? e.windowBits = -e.windowBits : e.gzip && e.windowBits > 0 && e.windowBits < 16 && (e.windowBits += 16), this.err = 0, this.msg = "", this.ended = !1, this.chunks = [], this.strm = new Xt, this.strm.avail_out = 0; let a = Ht.deflateInit2(this.strm, e.level, e.method, e.windowBits, e.memLevel, e.strategy); if (a !== $t) throw new Error(P[a]); if (e.header && Ht.deflateSetHeader(this.strm, e.header), e.dictionary) { let t; if (t = "string" == typeof e.dictionary ? Gt(e.dictionary) : "[object ArrayBuffer]" === Wt.call(e.dictionary) ? new Uint8Array(e.dictionary) : e.dictionary, a = Ht.deflateSetDictionary(this.strm, t), a !== $t) throw new Error(P[a]); this._dict_set = !0 } } function re(t, e) { const a = new ne(e); if (a.push(t, !0), a.err) throw a.msg || P[a.err]; return a.result } ne.prototype.push = function (t, e) { const a = this.strm, s = this.options.chunkSize; let n, r; if (this.ended) return !1; for (r = e === ~~e ? e : !0 === e ? Vt : qt, "string" == typeof t ? a.input = Gt(t) : "[object ArrayBuffer]" === Wt.call(t) ? a.input = new Uint8Array(t) : a.input = t, a.next_in = 0, a.avail_in = a.input.length; ;)if (0 === a.avail_out && (a.output = new Uint8Array(s), a.next_out = 0, a.avail_out = s), (r === Jt || r === Qt) && a.avail_out <= 6) this.onData(a.output.subarray(0, a.next_out)), a.avail_out = 0; else { if (n = Ht.deflate(a, r), n === te) return a.next_out > 0 && this.onData(a.output.subarray(0, a.next_out)), n = Ht.deflateEnd(this.strm), this.onEnd(n), this.ended = !0, n === $t; if (0 !== a.avail_out) { if (r > 0 && a.next_out > 0) this.onData(a.output.subarray(0, a.next_out)), a.avail_out = 0; else if (0 === a.avail_in) break } else this.onData(a.output) } return !0 }, ne.prototype.onData = function (t) { this.chunks.push(t) }, ne.prototype.onEnd = function (t) { t === $t && (this.result = jt(this.chunks)), this.chunks = [], this.err = t, this.msg = this.strm.msg }; var ie = ne, _e = re, le = function (t, e) { return (e = e || {}).raw = !0, re(t, e) }, he = function (t, e) { return (e = e || {}).gzip = !0, re(t, e) }, oe = j, de = { Deflate: ie, deflate: _e, deflateRaw: le, gzip: he, constants: oe }; t.Deflate = ie, t.constants = oe, t.default = de, t.deflate = _e, t.deflateRaw = le, t.gzip = he, Object.defineProperty(t, "__esModule", { value: !0 }) }));


var rrweb = (function (exports) {
    'use strict';

    /*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */

    var __assign = function () {
        __assign = Object.assign || function __assign(t) {
            for (var s, i = 1, n = arguments.length; i < n; i++) {
                s = arguments[i];
                for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
            }
            return t;
        };
        return __assign.apply(this, arguments);
    };

    function __values(o) {
        var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
        if (m) return m.call(o);
        if (o && typeof o.length === "number") return {
            next: function () {
                if (o && i >= o.length) o = void 0;
                return { value: o && o[i++], done: !o };
            }
        };
        throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
    }

    function __read(o, n) {
        var m = typeof Symbol === "function" && o[Symbol.iterator];
        if (!m) return o;
        var i = m.call(o), r, ar = [], e;
        try {
            while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
        }
        catch (error) { e = { error: error }; }
        finally {
            try {
                if (r && !r.done && (m = i["return"])) m.call(i);
            }
            finally { if (e) throw e.error; }
        }
        return ar;
    }

    function __spread() {
        for (var ar = [], i = 0; i < arguments.length; i++)
            ar = ar.concat(__read(arguments[i]));
        return ar;
    }

    var NodeType;
    (function (NodeType) {
        NodeType[NodeType["Document"] = 0] = "Document";
        NodeType[NodeType["DocumentType"] = 1] = "DocumentType";
        NodeType[NodeType["Element"] = 2] = "Element";
        NodeType[NodeType["Text"] = 3] = "Text";
        NodeType[NodeType["CDATA"] = 4] = "CDATA";
        NodeType[NodeType["Comment"] = 5] = "Comment";
    })(NodeType || (NodeType = {}));

    var _id = 1;
    var tagNameRegex = RegExp('[^a-z1-6-_]');
    var IGNORED_NODE = -2;
    function genId() {
        return _id++;
    }
    function getValidTagName(tagName) {
        var processedTagName = tagName.toLowerCase().trim();
        if (tagNameRegex.test(processedTagName)) {
            return 'div';
        }
        return processedTagName;
    }
    function getCssRulesString(s) {
        try {
            var rules = s.rules || s.cssRules;
            return rules
                ? Array.from(rules).map(getCssRuleString).join('')
                : null;
        }
        catch (error) {
            return null;
        }
    }
    function getCssRuleString(rule) {
        return isCSSImportRule(rule)
            ? getCssRulesString(rule.styleSheet) || ''
            : rule.cssText;
    }
    function isCSSImportRule(rule) {
        return 'styleSheet' in rule;
    }
    function extractOrigin(url) {
        var origin;
        if (url.indexOf('//') > -1) {
            origin = url.split('/').slice(0, 3).join('/');
        }
        else {
            origin = url.split('/')[0];
        }
        origin = origin.split('?')[0];
        return origin;
    }
    var URL_IN_CSS_REF = /url\((?:(')([^']*)'|(")([^"]*)"|([^)]*))\)/gm;
    var RELATIVE_PATH = /^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/).*/;
    var DATA_URI = /^(data:)([\w\/\+\-]+);(charset=[\w-]+|base64|utf-?8).*,(.*)/i;
    function absoluteToStylesheet(cssText, href) {
        return (cssText || '').replace(URL_IN_CSS_REF, function (origin, quote1, path1, quote2, path2, path3) {
            var filePath = path1 || path2 || path3;
            var maybe_quote = quote1 || quote2 || '';
            if (!filePath) {
                return origin;
            }
            if (!RELATIVE_PATH.test(filePath)) {
                return "url(" + maybe_quote + filePath + maybe_quote + ")";
            }
            if (DATA_URI.test(filePath)) {
                return "url(" + maybe_quote + filePath + maybe_quote + ")";
            }
            if (filePath[0] === '/') {
                return "url(" + maybe_quote + (extractOrigin(href) + filePath) + maybe_quote + ")";
            }
            var stack = href.split('/');
            var parts = filePath.split('/');
            stack.pop();
            for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {
                var part = parts_1[_i];
                if (part === '.') {
                    continue;
                }
                else if (part === '..') {
                    stack.pop();
                }
                else {
                    stack.push(part);
                }
            }
            return "url(" + maybe_quote + stack.join('/') + maybe_quote + ")";
        });
    }
    function getAbsoluteSrcsetString(doc, attributeValue) {
        if (attributeValue.trim() === '') {
            return attributeValue;
        }
        var srcsetValues = attributeValue.split(',');
        var resultingSrcsetString = srcsetValues
            .map(function (srcItem) {
                var trimmedSrcItem = srcItem.trimLeft().trimRight();
                var urlAndSize = trimmedSrcItem.split(' ');
                if (urlAndSize.length === 2) {
                    var absUrl = absoluteToDoc(doc, urlAndSize[0]);
                    return absUrl + " " + urlAndSize[1];
                }
                else if (urlAndSize.length === 1) {
                    var absUrl = absoluteToDoc(doc, urlAndSize[0]);
                    return "" + absUrl;
                }
                return '';
            })
            .join(', ');
        return resultingSrcsetString;
    }
    function absoluteToDoc(doc, attributeValue) {
        if (!attributeValue || attributeValue.trim() === '') {
            return attributeValue;
        }
        var a = doc.createElement('a');
        a.href = attributeValue;
        return a.href;
    }
    function isSVGElement(el) {
        return el.tagName === 'svg' || el instanceof SVGElement;
    }
    function transformAttribute(doc, name, value) {
        if (name === 'src' || (name === 'href' && value)) {
            return absoluteToDoc(doc, value);
        }
        else if (name === 'srcset' && value) {
            return getAbsoluteSrcsetString(doc, value);
        }
        else if (name === 'style' && value) {
            return absoluteToStylesheet(value, location.href);
        }
        else {
            return value;
        }
    }
    function _isBlockedElement(element, blockClass, blockSelector) {
        if (typeof blockClass === 'string') {
            if (element.classList.contains(blockClass)) {
                return true;
            }
        }
        else {
            element.classList.forEach(function (className) {
                if (blockClass.test(className)) {
                    return true;
                }
            });
        }
        if (blockSelector) {
            return element.matches(blockSelector);
        }
        return false;
    }
    function serializeNode(n, doc, blockClass, blockSelector, inlineStylesheet, maskInputOptions, recordCanvas) {
        if (maskInputOptions === void 0) { maskInputOptions = {}; }
        switch (n.nodeType) {
            case n.DOCUMENT_NODE:
                return {
                    type: NodeType.Document,
                    childNodes: []
                };
            case n.DOCUMENT_TYPE_NODE:
                return {
                    type: NodeType.DocumentType,
                    name: n.name,
                    publicId: n.publicId,
                    systemId: n.systemId
                };
            case n.ELEMENT_NODE:
                var needBlock = _isBlockedElement(n, blockClass, blockSelector);
                var tagName = getValidTagName(n.tagName);
                var attributes_1 = {};
                for (var _i = 0, _a = Array.from(n.attributes); _i < _a.length; _i++) {
                    var _b = _a[_i], name = _b.name, value = _b.value;
                    attributes_1[name] = transformAttribute(doc, name, value);
                }
                if (tagName === 'link' && inlineStylesheet) {
                    var stylesheet = Array.from(doc.styleSheets).find(function (s) {
                        return s.href === n.href;
                    });
                    var cssText = getCssRulesString(stylesheet);
                    if (cssText) {
                        delete attributes_1.rel;
                        delete attributes_1.href;
                        attributes_1._cssText = absoluteToStylesheet(cssText, stylesheet.href);
                    }
                }
                if (tagName === 'style' &&
                    n.sheet &&
                    !(n.innerText ||
                        n.textContent ||
                        '').trim().length) {
                    var cssText = getCssRulesString(n.sheet);
                    if (cssText) {
                        attributes_1._cssText = absoluteToStylesheet(cssText, location.href);
                    }
                }
                if (tagName === 'input' ||
                    tagName === 'textarea' ||
                    tagName === 'select') {
                    var value = n.value;
                    if (attributes_1.type !== 'radio' &&
                        attributes_1.type !== 'checkbox' &&
                        attributes_1.type !== 'submit' &&
                        attributes_1.type !== 'button' &&
                        value) {
                        attributes_1.value =
                            maskInputOptions[attributes_1.type] ||
                                maskInputOptions[tagName]
                                ? '*'.repeat(value.length)
                                : value;
                    }
                    else if (n.checked) {
                        attributes_1.checked = n.checked;
                    }
                }
                if (tagName === 'option') {
                    var selectValue = n.parentElement;
                    if (attributes_1.value === selectValue.value) {
                        attributes_1.selected = n.selected;
                    }
                }
                if (tagName === 'canvas' && recordCanvas) {
                    attributes_1.rr_dataURL = n.toDataURL();
                }
                if (tagName === 'audio' || tagName === 'video') {
                    attributes_1.rr_mediaState = n.paused
                        ? 'paused'
                        : 'played';
                }
                if (n.scrollLeft) {
                    attributes_1.rr_scrollLeft = n.scrollLeft;
                }
                if (n.scrollTop) {
                    attributes_1.rr_scrollTop = n.scrollTop;
                }
                if (needBlock) {
                    var _c = n.getBoundingClientRect(), width = _c.width, height = _c.height;
                    attributes_1.rr_width = width + "px";
                    attributes_1.rr_height = height + "px";
                }
                return {
                    type: NodeType.Element,
                    tagName: tagName,
                    attributes: attributes_1,
                    childNodes: [],
                    isSVG: isSVGElement(n) || undefined,
                    needBlock: needBlock
                };
            case n.TEXT_NODE:
                var parentTagName = n.parentNode && n.parentNode.tagName;
                var textContent = n.textContent;
                var isStyle = parentTagName === 'STYLE' ? true : undefined;
                if (isStyle && textContent) {
                    textContent = absoluteToStylesheet(textContent, location.href);
                }
                if (parentTagName === 'SCRIPT') {
                    textContent = 'SCRIPT_PLACEHOLDER';
                }
                return {
                    type: NodeType.Text,
                    textContent: textContent || '',
                    isStyle: isStyle
                };
            case n.CDATA_SECTION_NODE:
                return {
                    type: NodeType.CDATA,
                    textContent: ''
                };
            case n.COMMENT_NODE:
                return {
                    type: NodeType.Comment,
                    textContent: n.textContent || ''
                };
            default:
                return false;
        }
    }
    function lowerIfExists(maybeAttr) {
        if (maybeAttr === undefined) {
            return '';
        }
        else {
            return maybeAttr.toLowerCase();
        }
    }
    function slimDOMExcluded(sn, slimDOMOptions) {
        if (slimDOMOptions.comment && sn.type === NodeType.Comment) {
            return true;
        }
        else if (sn.type === NodeType.Element) {
            if (slimDOMOptions.script &&
                (sn.tagName === 'script' ||
                    (sn.tagName === 'link' && sn.attributes.rel === 'preload' && sn.attributes['as'] === 'script'))) {
                return true;
            }
            else if (slimDOMOptions.headFavicon && ((sn.tagName === 'link' && sn.attributes.rel === 'shortcut icon')
                || (sn.tagName === 'meta' && (lowerIfExists(sn.attributes['name']).match(/^msapplication-tile(image|color)$/)
                    || lowerIfExists(sn.attributes['name']) === 'application-name'
                    || lowerIfExists(sn.attributes['rel']) === 'icon'
                    || lowerIfExists(sn.attributes['rel']) === 'apple-touch-icon'
                    || lowerIfExists(sn.attributes['rel']) === 'shortcut icon')))) {
                return true;
            }
            else if (sn.tagName === 'meta') {
                if (slimDOMOptions.headMetaDescKeywords && (lowerIfExists(sn.attributes['name']).match(/^description|keywords$/))) {
                    return true;
                }
                else if (slimDOMOptions.headMetaSocial && (lowerIfExists(sn.attributes['property']).match(/^(og|twitter|fb):/)
                    || lowerIfExists(sn.attributes['name']).match(/^(og|twitter):/)
                    || lowerIfExists(sn.attributes['name']) === 'pinterest')) {
                    return true;
                }
                else if (slimDOMOptions.headMetaRobots && (lowerIfExists(sn.attributes['name']) === 'robots'
                    || lowerIfExists(sn.attributes['name']) === 'googlebot'
                    || lowerIfExists(sn.attributes['name']) === 'bingbot')) {
                    return true;
                }
                else if (slimDOMOptions.headMetaHttpEquiv && (sn.attributes['http-equiv'] !== undefined)) {
                    return true;
                }
                else if (slimDOMOptions.headMetaAuthorship && (lowerIfExists(sn.attributes['name']) === 'author'
                    || lowerIfExists(sn.attributes['name']) === 'generator'
                    || lowerIfExists(sn.attributes['name']) === 'framework'
                    || lowerIfExists(sn.attributes['name']) === 'publisher'
                    || lowerIfExists(sn.attributes['name']) === 'progid'
                    || lowerIfExists(sn.attributes['property']).match(/^article:/)
                    || lowerIfExists(sn.attributes['property']).match(/^product:/))) {
                    return true;
                }
                else if (slimDOMOptions.headMetaVerification && (lowerIfExists(sn.attributes['name']) === 'google-site-verification'
                    || lowerIfExists(sn.attributes['name']) === 'yandex-verification'
                    || lowerIfExists(sn.attributes['name']) === 'csrf-token'
                    || lowerIfExists(sn.attributes['name']) === 'p:domain_verify'
                    || lowerIfExists(sn.attributes['name']) === 'verify-v1'
                    || lowerIfExists(sn.attributes['name']) === 'verification'
                    || lowerIfExists(sn.attributes['name']) === 'shopify-checkout-api-token')) {
                    return true;
                }
            }
        }
        return false;
    }
    function serializeNodeWithId(n, doc, map, blockClass, blockSelector, skipChild, inlineStylesheet, maskInputOptions, slimDOMOptions, recordCanvas, preserveWhiteSpace) {
        if (skipChild === void 0) { skipChild = false; }
        if (inlineStylesheet === void 0) { inlineStylesheet = true; }
        if (slimDOMOptions === void 0) { slimDOMOptions = {}; }
        if (preserveWhiteSpace === void 0) { preserveWhiteSpace = true; }
        var _serializedNode = serializeNode(n, doc, blockClass, blockSelector, inlineStylesheet, maskInputOptions, recordCanvas || false);
        if (!_serializedNode) {
            console.warn(n, 'not serialized');
            return null;
        }
        var id;
        if ('__sn' in n) {
            id = n.__sn.id;
        }
        else if (slimDOMExcluded(_serializedNode, slimDOMOptions) ||
            (!preserveWhiteSpace &&
                _serializedNode.type === NodeType.Text &&
                !_serializedNode.isStyle &&
                !_serializedNode.textContent.replace(/^\s+|\s+$/gm, '').length)) {
            id = IGNORED_NODE;
        }
        else {
            id = genId();
        }
        var serializedNode = Object.assign(_serializedNode, { id: id });
        n.__sn = serializedNode;
        if (id === IGNORED_NODE) {
            return null;
        }
        map[id] = n;
        var recordChild = !skipChild;
        if (serializedNode.type === NodeType.Element) {
            recordChild = recordChild && !serializedNode.needBlock;
            delete serializedNode.needBlock;
        }
        if ((serializedNode.type === NodeType.Document ||
            serializedNode.type === NodeType.Element) &&
            recordChild) {
            if ((slimDOMOptions.headWhitespace &&
                _serializedNode.type === NodeType.Element &&
                _serializedNode.tagName == 'head')) {
                preserveWhiteSpace = false;
            }
            for (var _i = 0, _a = Array.from(n.childNodes); _i < _a.length; _i++) {
                var childN = _a[_i];
                var serializedChildNode = serializeNodeWithId(childN, doc, map, blockClass, blockSelector, skipChild, inlineStylesheet, maskInputOptions, slimDOMOptions, recordCanvas, preserveWhiteSpace);
                if (serializedChildNode) {
                    serializedNode.childNodes.push(serializedChildNode);
                }
            }
        }
        return serializedNode;
    }
    function snapshot(n, blockClass, inlineStylesheet, maskAllInputsOrOptions, slimDOMSensibleOrOptions, recordCanvas, blockSelector) {
        if (blockClass === void 0) { blockClass = 'vc-panel'; }
        if (inlineStylesheet === void 0) { inlineStylesheet = true; }
        if (blockSelector === void 0) { blockSelector = null; }
        var idNodeMap = {};
        var maskInputOptions = maskAllInputsOrOptions === true
            ? {
                color: true,
                date: true,
                'datetime-local': true,
                email: true,
                month: true,
                number: true,
                range: true,
                search: true,
                tel: true,
                text: true,
                time: true,
                url: true,
                week: true,
                textarea: true,
                select: true
            }
            : maskAllInputsOrOptions === false
                ? {}
                : maskAllInputsOrOptions;
        var slimDOMOptions = (slimDOMSensibleOrOptions === true ||
            slimDOMSensibleOrOptions === 'all')
            ? {
                script: true,
                comment: true,
                headFavicon: true,
                headWhitespace: true,
                headMetaDescKeywords: slimDOMSensibleOrOptions === 'all',
                headMetaSocial: true,
                headMetaRobots: true,
                headMetaHttpEquiv: true,
                headMetaAuthorship: true,
                headMetaVerification: true
            }
            : slimDOMSensibleOrOptions === false
                ? {}
                : slimDOMSensibleOrOptions;
        return [
            serializeNodeWithId(n, n, idNodeMap, blockClass, blockSelector, false, inlineStylesheet, maskInputOptions, slimDOMOptions, recordCanvas),
            idNodeMap,
        ];
    }

    var commentre = /\/\*[^*]*\*+([^/*][^*]*\*+)*\//g;
    function parse(css, options) {
        if (options === void 0) { options = {}; }
        var lineno = 1;
        var column = 1;
        function updatePosition(str) {
            var lines = str.match(/\n/g);
            if (lines) {
                lineno += lines.length;
            }
            var i = str.lastIndexOf('\n');
            column = i === -1 ? column + str.length : str.length - i;
        }
        function position() {
            var start = { line: lineno, column: column };
            return function (node) {
                node.position = new Position(start);
                whitespace();
                return node;
            };
        }
        var Position = (function () {
            function Position(start) {
                this.start = start;
                this.end = { line: lineno, column: column };
                this.source = options.source;
            }
            return Position;
        }());
        Position.prototype.content = css;
        var errorsList = [];
        function error(msg) {
            var err = new Error(options.source + ':' + lineno + ':' + column + ': ' + msg);
            err.reason = msg;
            err.filename = options.source;
            err.line = lineno;
            err.column = column;
            err.source = css;
            if (options.silent) {
                errorsList.push(err);
            }
            else {
                throw err;
            }
        }
        function stylesheet() {
            var rulesList = rules();
            return {
                type: 'stylesheet',
                stylesheet: {
                    source: options.source,
                    rules: rulesList,
                    parsingErrors: errorsList
                }
            };
        }
        function open() {
            return match(/^{\s*/);
        }
        function close() {
            return match(/^}/);
        }
        function rules() {
            var node;
            var rules = [];
            whitespace();
            comments(rules);
            while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {
                if (node !== false) {
                    rules.push(node);
                    comments(rules);
                }
            }
            return rules;
        }
        function match(re) {
            var m = re.exec(css);
            if (!m) {
                return;
            }
            var str = m[0];
            updatePosition(str);
            css = css.slice(str.length);
            return m;
        }
        function whitespace() {
            match(/^\s*/);
        }
        function comments(rules) {
            if (rules === void 0) { rules = []; }
            var c;
            while ((c = comment())) {
                if (c !== false) {
                    rules.push(c);
                }
                c = comment();
            }
            return rules;
        }
        function comment() {
            var pos = position();
            if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {
                return;
            }
            var i = 2;
            while ('' !== css.charAt(i) &&
                ('*' !== css.charAt(i) || '/' !== css.charAt(i + 1))) {
                ++i;
            }
            i += 2;
            if ('' === css.charAt(i - 1)) {
                return error('End of comment missing');
            }
            var str = css.slice(2, i - 2);
            column += 2;
            updatePosition(str);
            css = css.slice(i);
            column += 2;
            return pos({
                type: 'comment',
                comment: str
            });
        }
        function selector() {
            var m = match(/^([^{]+)/);
            if (!m) {
                return;
            }
            return trim(m[0])
                .replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g, '')
                .replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g, function (m) {
                    return m.replace(/,/g, '\u200C');
                })
                .split(/\s*(?![^(]*\)),\s*/)
                .map(function (s) {
                    return s.replace(/\u200C/g, ',');
                });
        }
        function declaration() {
            var pos = position();
            var propMatch = match(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);
            if (!propMatch) {
                return;
            }
            var prop = trim(propMatch[0]);
            if (!match(/^:\s*/)) {
                return error("property missing ':'");
            }
            var val = match(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/);
            var ret = pos({
                type: 'declaration',
                property: prop.replace(commentre, ''),
                value: val ? trim(val[0]).replace(commentre, '') : ''
            });
            match(/^[;\s]*/);
            return ret;
        }
        function declarations() {
            var decls = [];
            if (!open()) {
                return error("missing '{'");
            }
            comments(decls);
            var decl;
            while ((decl = declaration())) {
                if (decl !== false) {
                    decls.push(decl);
                    comments(decls);
                }
                decl = declaration();
            }
            if (!close()) {
                return error("missing '}'");
            }
            return decls;
        }
        function keyframe() {
            var m;
            var vals = [];
            var pos = position();
            while ((m = match(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/))) {
                vals.push(m[1]);
                match(/^,\s*/);
            }
            if (!vals.length) {
                return;
            }
            return pos({
                type: 'keyframe',
                values: vals,
                declarations: declarations()
            });
        }
        function atkeyframes() {
            var pos = position();
            var m = match(/^@([-\w]+)?keyframes\s*/);
            if (!m) {
                return;
            }
            var vendor = m[1];
            m = match(/^([-\w]+)\s*/);
            if (!m) {
                return error('@keyframes missing name');
            }
            var name = m[1];
            if (!open()) {
                return error("@keyframes missing '{'");
            }
            var frame;
            var frames = comments();
            while ((frame = keyframe())) {
                frames.push(frame);
                frames = frames.concat(comments());
            }
            if (!close()) {
                return error("@keyframes missing '}'");
            }
            return pos({
                type: 'keyframes',
                name: name,
                vendor: vendor,
                keyframes: frames
            });
        }
        function atsupports() {
            var pos = position();
            var m = match(/^@supports *([^{]+)/);
            if (!m) {
                return;
            }
            var supports = trim(m[1]);
            if (!open()) {
                return error("@supports missing '{'");
            }
            var style = comments().concat(rules());
            if (!close()) {
                return error("@supports missing '}'");
            }
            return pos({
                type: 'supports',
                supports: supports,
                rules: style
            });
        }
        function athost() {
            var pos = position();
            var m = match(/^@host\s*/);
            if (!m) {
                return;
            }
            if (!open()) {
                return error("@host missing '{'");
            }
            var style = comments().concat(rules());
            if (!close()) {
                return error("@host missing '}'");
            }
            return pos({
                type: 'host',
                rules: style
            });
        }
        function atmedia() {
            var pos = position();
            var m = match(/^@media *([^{]+)/);
            if (!m) {
                return;
            }
            var media = trim(m[1]);
            if (!open()) {
                return error("@media missing '{'");
            }
            var style = comments().concat(rules());
            if (!close()) {
                return error("@media missing '}'");
            }
            return pos({
                type: 'media',
                media: media,
                rules: style
            });
        }
        function atcustommedia() {
            var pos = position();
            var m = match(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);
            if (!m) {
                return;
            }
            return pos({
                type: 'custom-media',
                name: trim(m[1]),
                media: trim(m[2])
            });
        }
        function atpage() {
            var pos = position();
            var m = match(/^@page */);
            if (!m) {
                return;
            }
            var sel = selector() || [];
            if (!open()) {
                return error("@page missing '{'");
            }
            var decls = comments();
            var decl;
            while ((decl = declaration())) {
                decls.push(decl);
                decls = decls.concat(comments());
            }
            if (!close()) {
                return error("@page missing '}'");
            }
            return pos({
                type: 'page',
                selectors: sel,
                declarations: decls
            });
        }
        function atdocument() {
            var pos = position();
            var m = match(/^@([-\w]+)?document *([^{]+)/);
            if (!m) {
                return;
            }
            var vendor = trim(m[1]);
            var doc = trim(m[2]);
            if (!open()) {
                return error("@document missing '{'");
            }
            var style = comments().concat(rules());
            if (!close()) {
                return error("@document missing '}'");
            }
            return pos({
                type: 'document',
                document: doc,
                vendor: vendor,
                rules: style
            });
        }
        function atfontface() {
            var pos = position();
            var m = match(/^@font-face\s*/);
            if (!m) {
                return;
            }
            if (!open()) {
                return error("@font-face missing '{'");
            }
            var decls = comments();
            var decl;
            while ((decl = declaration())) {
                decls.push(decl);
                decls = decls.concat(comments());
            }
            if (!close()) {
                return error("@font-face missing '}'");
            }
            return pos({
                type: 'font-face',
                declarations: decls
            });
        }
        var atimport = _compileAtrule('import');
        var atcharset = _compileAtrule('charset');
        var atnamespace = _compileAtrule('namespace');
        function _compileAtrule(name) {
            var re = new RegExp('^@' + name + '\\s*([^;]+);');
            return function () {
                var pos = position();
                var m = match(re);
                if (!m) {
                    return;
                }
                var ret = { type: name };
                ret[name] = m[1].trim();
                return pos(ret);
            };
        }
        function atrule() {
            if (css[0] !== '@') {
                return;
            }
            return (atkeyframes() ||
                atmedia() ||
                atcustommedia() ||
                atsupports() ||
                atimport() ||
                atcharset() ||
                atnamespace() ||
                atdocument() ||
                atpage() ||
                athost() ||
                atfontface());
        }
        function rule() {
            var pos = position();
            var sel = selector();
            if (!sel) {
                return error('selector missing');
            }
            comments();
            return pos({
                type: 'rule',
                selectors: sel,
                declarations: declarations()
            });
        }
        return addParent(stylesheet());
    }
    function trim(str) {
        return str ? str.replace(/^\s+|\s+$/g, '') : '';
    }
    function addParent(obj, parent) {
        var isNode = obj && typeof obj.type === 'string';
        var childParent = isNode ? obj : parent;
        for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {
            var k = _a[_i];
            var value = obj[k];
            if (Array.isArray(value)) {
                value.forEach(function (v) {
                    addParent(v, childParent);
                });
            }
            else if (value && typeof value === 'object') {
                addParent(value, childParent);
            }
        }
        if (isNode) {
            Object.defineProperty(obj, 'parent', {
                configurable: true,
                writable: true,
                enumerable: false,
                value: parent || null
            });
        }
        return obj;
    }

    var tagMap = {
        script: 'noscript',
        altglyph: 'altGlyph',
        altglyphdef: 'altGlyphDef',
        altglyphitem: 'altGlyphItem',
        animatecolor: 'animateColor',
        animatemotion: 'animateMotion',
        animatetransform: 'animateTransform',
        clippath: 'clipPath',
        feblend: 'feBlend',
        fecolormatrix: 'feColorMatrix',
        fecomponenttransfer: 'feComponentTransfer',
        fecomposite: 'feComposite',
        feconvolvematrix: 'feConvolveMatrix',
        fediffuselighting: 'feDiffuseLighting',
        fedisplacementmap: 'feDisplacementMap',
        fedistantlight: 'feDistantLight',
        fedropshadow: 'feDropShadow',
        feflood: 'feFlood',
        fefunca: 'feFuncA',
        fefuncb: 'feFuncB',
        fefuncg: 'feFuncG',
        fefuncr: 'feFuncR',
        fegaussianblur: 'feGaussianBlur',
        feimage: 'feImage',
        femerge: 'feMerge',
        femergenode: 'feMergeNode',
        femorphology: 'feMorphology',
        feoffset: 'feOffset',
        fepointlight: 'fePointLight',
        fespecularlighting: 'feSpecularLighting',
        fespotlight: 'feSpotLight',
        fetile: 'feTile',
        feturbulence: 'feTurbulence',
        foreignobject: 'foreignObject',
        glyphref: 'glyphRef',
        lineargradient: 'linearGradient',
        radialgradient: 'radialGradient'
    };
    function getTagName(n) {
        var tagName = tagMap[n.tagName] ? tagMap[n.tagName] : n.tagName;
        if (tagName === 'link' && n.attributes._cssText) {
            tagName = 'style';
        }
        return tagName;
    }
    var HOVER_SELECTOR = /([^\\]):hover/g;
    function addHoverClass(cssText) {
        var ast = parse(cssText, { silent: true });
        if (!ast.stylesheet) {
            return cssText;
        }
        ast.stylesheet.rules.forEach(function (rule) {
            if ('selectors' in rule) {
                (rule.selectors || []).forEach(function (selector) {
                    if (HOVER_SELECTOR.test(selector)) {
                        var newSelector = selector.replace(HOVER_SELECTOR, '$1.\\:hover');
                        cssText = cssText.replace(selector, selector + ", " + newSelector);
                    }
                });
            }
        });
        return cssText;
    }
    function buildNode(n, doc, HACK_CSS) {
        switch (n.type) {
            case NodeType.Document:
                return doc.implementation.createDocument(null, '', null);
            case NodeType.DocumentType:
                return doc.implementation.createDocumentType(n.name || 'html', n.publicId, n.systemId);
            case NodeType.Element:
                var tagName = getTagName(n);
                var node_1;
                if (n.isSVG) {
                    node_1 = doc.createElementNS('http://www.w3.org/2000/svg', tagName);
                }
                else {
                    node_1 = doc.createElement(tagName);
                }
                var _loop_1 = function (name) {
                    if (!n.attributes.hasOwnProperty(name)) {
                        return "continue";
                    }
                    var value = n.attributes[name];
                    value =
                        typeof value === 'boolean' || typeof value === 'number' ? '' : value;
                    if (!name.startsWith('rr_')) {
                        var isTextarea = tagName === 'textarea' && name === 'value';
                        var isRemoteOrDynamicCss = tagName === 'style' && name === '_cssText';
                        if (isRemoteOrDynamicCss && HACK_CSS) {
                            value = addHoverClass(value);
                        }
                        if (isTextarea || isRemoteOrDynamicCss) {
                            var child = doc.createTextNode(value);
                            for (var _i = 0, _a = Array.from(node_1.childNodes); _i < _a.length; _i++) {
                                var c = _a[_i];
                                if (c.nodeType === node_1.TEXT_NODE) {
                                    node_1.removeChild(c);
                                }
                            }
                            node_1.appendChild(child);
                            return "continue";
                        }
                        if (tagName === 'iframe' && name === 'src') {
                            return "continue";
                        }
                        try {
                            if (n.isSVG && name === 'xlink:href') {
                                node_1.setAttributeNS('http://www.w3.org/1999/xlink', name, value);
                            }
                            else if (name === 'onload' ||
                                name === 'onclick' ||
                                name.substring(0, 7) === 'onmouse') {
                                node_1.setAttribute('_' + name, value);
                            }
                            else {
                                node_1.setAttribute(name, value);
                            }
                        }
                        catch (error) {
                        }
                    }
                    else {
                        if (tagName === 'canvas' && name === 'rr_dataURL') {
                            var image_1 = document.createElement('img');
                            image_1.src = value;
                            image_1.onload = function () {
                                var ctx = node_1.getContext('2d');
                                if (ctx) {
                                    ctx.drawImage(image_1, 0, 0, image_1.width, image_1.height);
                                }
                            };
                        }
                        if (name === 'rr_width') {
                            node_1.style.width = value;
                        }
                        if (name === 'rr_height') {
                            node_1.style.height = value;
                        }
                        if (name === 'rr_mediaState') {
                            switch (value) {
                                case 'played':
                                    node_1.play();
                                case 'paused':
                                    node_1.pause();
                                    break;
                            }
                        }
                    }
                };
                for (var name in n.attributes) {
                    _loop_1(name);
                }
                return node_1;
            case NodeType.Text:
                return doc.createTextNode(n.isStyle && HACK_CSS ? addHoverClass(n.textContent) : n.textContent);
            case NodeType.CDATA:
                return doc.createCDATASection(n.textContent);
            case NodeType.Comment:
                return doc.createComment(n.textContent);
            default:
                return null;
        }
    }
    function buildNodeWithSN(n, doc, map, skipChild, HACK_CSS) {
        if (skipChild === void 0) { skipChild = false; }
        if (HACK_CSS === void 0) { HACK_CSS = true; }
        var node = buildNode(n, doc, HACK_CSS);
        if (!node) {
            return null;
        }
        if (n.type === NodeType.Document) {
            doc.close();
            doc.open();
            node = doc;
        }
        node.__sn = n;
        map[n.id] = node;
        if ((n.type === NodeType.Document || n.type === NodeType.Element) &&
            !skipChild) {
            for (var _i = 0, _a = n.childNodes; _i < _a.length; _i++) {
                var childN = _a[_i];
                var childNode = buildNodeWithSN(childN, doc, map, false, HACK_CSS);
                if (!childNode) {
                    console.warn('Failed to rebuild', childN);
                }
                else {
                    node.appendChild(childNode);
                }
            }
        }
        return node;
    }
    function visit(idNodeMap, onVisit) {
        function walk(node) {
            onVisit(node);
        }
        for (var key in idNodeMap) {
            if (idNodeMap[key]) {
                walk(idNodeMap[key]);
            }
        }
    }
    function handleScroll(node) {
        var n = node.__sn;
        if (n.type !== NodeType.Element) {
            return;
        }
        var el = node;
        for (var name in n.attributes) {
            if (!(n.attributes.hasOwnProperty(name) && name.startsWith('rr_'))) {
                continue;
            }
            var value = n.attributes[name];
            if (name === 'rr_scrollLeft') {
                el.scrollLeft = value;
            }
            if (name === 'rr_scrollTop') {
                el.scrollTop = value;
            }
        }
    }
    function rebuild(n, doc, onVisit, HACK_CSS) {
        if (HACK_CSS === void 0) { HACK_CSS = true; }
        var idNodeMap = {};
        var node = buildNodeWithSN(n, doc, idNodeMap, false, HACK_CSS);
        visit(idNodeMap, function (visitedNode) {
            if (onVisit) {
                onVisit(visitedNode);
            }
            handleScroll(visitedNode);
        });
        return [node, idNodeMap];
    }

    (function (EventType) {
        EventType[EventType["DomContentLoaded"] = 0] = "DomContentLoaded";
        EventType[EventType["Load"] = 1] = "Load";
        EventType[EventType["FullSnapshot"] = 2] = "FullSnapshot";
        EventType[EventType["IncrementalSnapshot"] = 3] = "IncrementalSnapshot";
        EventType[EventType["Meta"] = 4] = "Meta";
        EventType[EventType["Custom"] = 5] = "Custom";
    })(exports.EventType || (exports.EventType = {}));
    (function (IncrementalSource) {
        IncrementalSource[IncrementalSource["Mutation"] = 0] = "Mutation";
        IncrementalSource[IncrementalSource["MouseMove"] = 1] = "MouseMove";
        IncrementalSource[IncrementalSource["MouseInteraction"] = 2] = "MouseInteraction";
        IncrementalSource[IncrementalSource["Scroll"] = 3] = "Scroll";
        IncrementalSource[IncrementalSource["ViewportResize"] = 4] = "ViewportResize";
        IncrementalSource[IncrementalSource["Input"] = 5] = "Input";
        IncrementalSource[IncrementalSource["TouchMove"] = 6] = "TouchMove";
        IncrementalSource[IncrementalSource["MediaInteraction"] = 7] = "MediaInteraction";
        IncrementalSource[IncrementalSource["StyleSheetRule"] = 8] = "StyleSheetRule";
        IncrementalSource[IncrementalSource["CanvasMutation"] = 9] = "CanvasMutation";
        IncrementalSource[IncrementalSource["Font"] = 10] = "Font";
    })(exports.IncrementalSource || (exports.IncrementalSource = {}));
    (function (MouseInteractions) {
        MouseInteractions[MouseInteractions["MouseUp"] = 0] = "MouseUp";
        MouseInteractions[MouseInteractions["MouseDown"] = 1] = "MouseDown";
        MouseInteractions[MouseInteractions["Click"] = 2] = "Click";
        MouseInteractions[MouseInteractions["ContextMenu"] = 3] = "ContextMenu";
        MouseInteractions[MouseInteractions["DblClick"] = 4] = "DblClick";
        MouseInteractions[MouseInteractions["Focus"] = 5] = "Focus";
        MouseInteractions[MouseInteractions["Blur"] = 6] = "Blur";
        MouseInteractions[MouseInteractions["TouchStart"] = 7] = "TouchStart";
        MouseInteractions[MouseInteractions["TouchMove_Departed"] = 8] = "TouchMove_Departed";
        MouseInteractions[MouseInteractions["TouchEnd"] = 9] = "TouchEnd";
    })(exports.MouseInteractions || (exports.MouseInteractions = {}));
    var MediaInteractions;
    (function (MediaInteractions) {
        MediaInteractions[MediaInteractions["Play"] = 0] = "Play";
        MediaInteractions[MediaInteractions["Pause"] = 1] = "Pause";
    })(MediaInteractions || (MediaInteractions = {}));
    (function (ReplayerEvents) {
        ReplayerEvents["Start"] = "start";
        ReplayerEvents["Pause"] = "pause";
        ReplayerEvents["Resume"] = "resume";
        ReplayerEvents["Resize"] = "resize";
        ReplayerEvents["Finish"] = "finish";
        ReplayerEvents["FullsnapshotRebuilded"] = "fullsnapshot-rebuilded";
        ReplayerEvents["LoadStylesheetStart"] = "load-stylesheet-start";
        ReplayerEvents["LoadStylesheetEnd"] = "load-stylesheet-end";
        ReplayerEvents["SkipStart"] = "skip-start";
        ReplayerEvents["SkipEnd"] = "skip-end";
        ReplayerEvents["MouseInteraction"] = "mouse-interaction";
        ReplayerEvents["EventCast"] = "event-cast";
        ReplayerEvents["CustomEvent"] = "custom-event";
        ReplayerEvents["Flush"] = "flush";
        ReplayerEvents["StateChange"] = "state-change";
    })(exports.ReplayerEvents || (exports.ReplayerEvents = {}));

    function on(type, fn, target) {
        if (target === void 0) { target = document; }
        var options = { capture: true, passive: true };
        target.addEventListener(type, fn, options);
        return function () { return target.removeEventListener(type, fn, options); };
    }
    var mirror = {
        map: {},
        getId: function (n) {
            if (!n.__sn) {
                return -1;
            }
            return n.__sn.id;
        },
        getNode: function (id) {
            return mirror.map[id] || null;
        },
        removeNodeFromMap: function (n) {
            var id = n.__sn && n.__sn.id;
            delete mirror.map[id];
            if (n.childNodes) {
                n.childNodes.forEach(function (child) {
                    return mirror.removeNodeFromMap(child);
                });
            }
        },
        has: function (id) {
            return mirror.map.hasOwnProperty(id);
        },
    };
    function throttle(func, wait, options) {
        if (options === void 0) { options = {}; }
        var timeout = null;
        var previous = 0;
        return function (arg) {
            var now = Date.now();
            if (!previous && options.leading === false) {
                previous = now;
            }
            var remaining = wait - (now - previous);
            var context = this;
            var args = arguments;
            if (remaining <= 0 || remaining > wait) {
                if (timeout) {
                    window.clearTimeout(timeout);
                    timeout = null;
                }
                previous = now;
                func.apply(context, args);
            }
            else if (!timeout && options.trailing !== false) {
                timeout = window.setTimeout(function () {
                    previous = options.leading === false ? 0 : Date.now();
                    timeout = null;
                    func.apply(context, args);
                }, remaining);
            }
        };
    }
    function hookSetter(target, key, d, isRevoked, win) {
        if (win === void 0) { win = window; }
        var original = win.Object.getOwnPropertyDescriptor(target, key);
        win.Object.defineProperty(target, key, isRevoked
            ? d
            : {
                set: function (value) {
                    var _this = this;
                    setTimeout(function () {
                        console.log('执行了----')
                        d.set.call(_this, value);
                    }, 0);
                    if (original && original.set) {
                        original.set.call(this, value);
                    }
                },
            });
        return function () { return hookSetter(target, key, original || {}, true); };
    }
    function patch(source, name, replacement) {
        try {
            if (!(name in source)) {
                return function () { };
            }
            var original_1 = source[name];
            var wrapped = replacement(original_1);
            if (typeof wrapped === 'function') {
                wrapped.prototype = wrapped.prototype || {};
                Object.defineProperties(wrapped, {
                    __rrweb_original__: {
                        enumerable: false,
                        value: original_1,
                    },
                });
            }
            source[name] = wrapped;
            return function () {
                source[name] = original_1;
            };
        }
        catch (_a) {
            return function () { };
        }
    }
    function getWindowHeight() {
        return (window.innerHeight ||
            (document.documentElement && document.documentElement.clientHeight) ||
            (document.body && document.body.clientHeight));
    }
    function getWindowWidth() {
        return (window.innerWidth ||
            (document.documentElement && document.documentElement.clientWidth) ||
            (document.body && document.body.clientWidth));
    }
    function isBlocked(node, blockClass) {
        if (!node) {
            return false;
        }
        if (node.nodeType === node.ELEMENT_NODE) {
            var needBlock_1 = false;
            if (typeof blockClass === 'string') {
                needBlock_1 = node.classList.contains(blockClass);
            }
            else {
                node.classList.forEach(function (className) {
                    if (blockClass.test(className)) {
                        needBlock_1 = true;
                    }
                });
            }
            return needBlock_1 || isBlocked(node.parentNode, blockClass);
        }
        if (node.nodeType === node.TEXT_NODE) {
            return isBlocked(node.parentNode, blockClass);
        }
        return isBlocked(node.parentNode, blockClass);
    }
    function isAncestorRemoved(target) {
        var id = mirror.getId(target);
        if (!mirror.has(id)) {
            return true;
        }
        if (target.parentNode &&
            target.parentNode.nodeType === target.DOCUMENT_NODE) {
            return false;
        }
        if (!target.parentNode) {
            return true;
        }
        return isAncestorRemoved(target.parentNode);
    }
    function isTouchEvent(event) {
        return Boolean(event.changedTouches);
    }
    function polyfill(win) {
        if (win === void 0) { win = window; }
        if ('NodeList' in win && !win.NodeList.prototype.forEach) {
            win.NodeList.prototype.forEach = Array.prototype
                .forEach;
        }
        if ('DOMTokenList' in win && !win.DOMTokenList.prototype.forEach) {
            win.DOMTokenList.prototype.forEach = Array.prototype
                .forEach;
        }
    }
    function needCastInSyncMode(event) {
        switch (event.type) {
            case exports.EventType.DomContentLoaded:
            case exports.EventType.Load:
            case exports.EventType.Custom:
                return false;
            case exports.EventType.FullSnapshot:
            case exports.EventType.Meta:
                return true;
        }
        switch (event.data.source) {
            case exports.IncrementalSource.MouseMove:
            case exports.IncrementalSource.MouseInteraction:
            case exports.IncrementalSource.TouchMove:
            case exports.IncrementalSource.MediaInteraction:
                return false;
            case exports.IncrementalSource.ViewportResize:
            case exports.IncrementalSource.StyleSheetRule:
            case exports.IncrementalSource.Scroll:
            case exports.IncrementalSource.Input:
                return true;
        }
        return true;
    }
    var TreeIndex = (function () {
        function TreeIndex() {
            this.reset();
        }
        TreeIndex.prototype.add = function (mutation) {
            var parentTreeNode = this.indexes.get(mutation.parentId);
            var treeNode = {
                id: mutation.node.id,
                mutation: mutation,
                children: [],
                texts: [],
                attributes: [],
            };
            if (!parentTreeNode) {
                this.tree[treeNode.id] = treeNode;
            }
            else {
                treeNode.parent = parentTreeNode;
                parentTreeNode.children[treeNode.id] = treeNode;
            }
            this.indexes.set(treeNode.id, treeNode);
        };
        TreeIndex.prototype.remove = function (mutation) {
            var _this = this;
            var parentTreeNode = this.indexes.get(mutation.parentId);
            var treeNode = this.indexes.get(mutation.id);
            var deepRemoveFromMirror = function (id) {
                _this.removeIdSet.add(id);
                var node = mirror.getNode(id);
                node === null || node === void 0 ? void 0 : node.childNodes.forEach(function (childNode) {
                    if ('__sn' in childNode) {
                        deepRemoveFromMirror(childNode.__sn.id);
                    }
                });
            };
            var deepRemoveFromTreeIndex = function (node) {
                _this.removeIdSet.add(node.id);
                Object.values(node.children).forEach(function (n) { return deepRemoveFromTreeIndex(n); });
                var _treeNode = _this.indexes.get(node.id);
                if (_treeNode) {
                    var _parentTreeNode = _treeNode.parent;
                    if (_parentTreeNode) {
                        delete _treeNode.parent;
                        delete _parentTreeNode.children[_treeNode.id];
                        _this.indexes.delete(mutation.id);
                    }
                }
            };
            if (!treeNode) {
                this.removeNodeMutations.push(mutation);
                deepRemoveFromMirror(mutation.id);
            }
            else if (!parentTreeNode) {
                delete this.tree[treeNode.id];
                this.indexes.delete(treeNode.id);
                deepRemoveFromTreeIndex(treeNode);
            }
            else {
                delete treeNode.parent;
                delete parentTreeNode.children[treeNode.id];
                this.indexes.delete(mutation.id);
                deepRemoveFromTreeIndex(treeNode);
            }
        };
        TreeIndex.prototype.text = function (mutation) {
            var treeNode = this.indexes.get(mutation.id);
            if (treeNode) {
                treeNode.texts.push(mutation);
            }
            else {
                this.textMutations.push(mutation);
            }
        };
        TreeIndex.prototype.attribute = function (mutation) {
            var treeNode = this.indexes.get(mutation.id);
            if (treeNode) {
                treeNode.attributes.push(mutation);
            }
            else {
                this.attributeMutations.push(mutation);
            }
        };
        TreeIndex.prototype.scroll = function (d) {
            this.scrollMap.set(d.id, d);
        };
        TreeIndex.prototype.input = function (d) {
            this.inputMap.set(d.id, d);
        };
        TreeIndex.prototype.flush = function () {
            var e_1, _a, e_2, _b;
            var _this = this;
            var _c = this, tree = _c.tree, removeNodeMutations = _c.removeNodeMutations, textMutations = _c.textMutations, attributeMutations = _c.attributeMutations;
            var batchMutationData = {
                source: exports.IncrementalSource.Mutation,
                removes: removeNodeMutations,
                texts: textMutations,
                attributes: attributeMutations,
                adds: [],
            };
            var walk = function (treeNode, removed) {
                if (removed) {
                    _this.removeIdSet.add(treeNode.id);
                }
                batchMutationData.texts = batchMutationData.texts
                    .concat(removed ? [] : treeNode.texts)
                    .filter(function (m) { return !_this.removeIdSet.has(m.id); });
                batchMutationData.attributes = batchMutationData.attributes
                    .concat(removed ? [] : treeNode.attributes)
                    .filter(function (m) { return !_this.removeIdSet.has(m.id); });
                if (!_this.removeIdSet.has(treeNode.id) &&
                    !_this.removeIdSet.has(treeNode.mutation.parentId) &&
                    !removed) {
                    batchMutationData.adds.push(treeNode.mutation);
                    if (treeNode.children) {
                        Object.values(treeNode.children).forEach(function (n) { return walk(n, false); });
                    }
                }
                else {
                    Object.values(treeNode.children).forEach(function (n) { return walk(n, true); });
                }
            };
            Object.values(tree).forEach(function (n) { return walk(n, false); });
            try {
                for (var _d = __values(this.scrollMap.keys()), _e = _d.next(); !_e.done; _e = _d.next()) {
                    var id = _e.value;
                    if (this.removeIdSet.has(id)) {
                        this.scrollMap.delete(id);
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
                }
                finally { if (e_1) throw e_1.error; }
            }
            try {
                for (var _f = __values(this.inputMap.keys()), _g = _f.next(); !_g.done; _g = _f.next()) {
                    var id = _g.value;
                    if (this.removeIdSet.has(id)) {
                        this.inputMap.delete(id);
                    }
                }
            }
            catch (e_2_1) { e_2 = { error: e_2_1 }; }
            finally {
                try {
                    if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
                }
                finally { if (e_2) throw e_2.error; }
            }
            var scrollMap = new Map(this.scrollMap);
            var inputMap = new Map(this.inputMap);
            this.reset();
            return {
                mutationData: batchMutationData,
                scrollMap: scrollMap,
                inputMap: inputMap,
            };
        };
        TreeIndex.prototype.reset = function () {
            this.tree = [];
            this.indexes = new Map();
            this.removeNodeMutations = [];
            this.textMutations = [];
            this.attributeMutations = [];
            this.removeIdSet = new Set();
            this.scrollMap = new Map();
            this.inputMap = new Map();
        };
        return TreeIndex;
    }());
    function queueToResolveTrees(queue) {
        var e_3, _a;
        var queueNodeMap = {};
        var putIntoMap = function (m, parent) {
            var nodeInTree = {
                value: m,
                parent: parent,
                children: [],
            };
            queueNodeMap[m.node.id] = nodeInTree;
            return nodeInTree;
        };
        var queueNodeTrees = [];
        try {
            for (var queue_1 = __values(queue), queue_1_1 = queue_1.next(); !queue_1_1.done; queue_1_1 = queue_1.next()) {
                var mutation = queue_1_1.value;
                var nextId = mutation.nextId, parentId = mutation.parentId;
                if (nextId && nextId in queueNodeMap) {
                    var nextInTree = queueNodeMap[nextId];
                    if (nextInTree.parent) {
                        var idx = nextInTree.parent.children.indexOf(nextInTree);
                        nextInTree.parent.children.splice(idx, 0, putIntoMap(mutation, nextInTree.parent));
                    }
                    else {
                        var idx = queueNodeTrees.indexOf(nextInTree);
                        queueNodeTrees.splice(idx, 0, putIntoMap(mutation, null));
                    }
                    continue;
                }
                if (parentId in queueNodeMap) {
                    var parentInTree = queueNodeMap[parentId];
                    parentInTree.children.push(putIntoMap(mutation, parentInTree));
                    continue;
                }
                queueNodeTrees.push(putIntoMap(mutation, null));
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (queue_1_1 && !queue_1_1.done && (_a = queue_1.return)) _a.call(queue_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return queueNodeTrees;
    }
    function iterateResolveTree(tree, cb) {
        cb(tree.value);
        for (var i = tree.children.length - 1; i >= 0; i--) {
            iterateResolveTree(tree.children[i], cb);
        }
    }

    var utils = /*#__PURE__*/Object.freeze({
        __proto__: null,
        on: on,
        mirror: mirror,
        throttle: throttle,
        hookSetter: hookSetter,
        patch: patch,
        getWindowHeight: getWindowHeight,
        getWindowWidth: getWindowWidth,
        isBlocked: isBlocked,
        isAncestorRemoved: isAncestorRemoved,
        isTouchEvent: isTouchEvent,
        polyfill: polyfill,
        needCastInSyncMode: needCastInSyncMode,
        TreeIndex: TreeIndex,
        queueToResolveTrees: queueToResolveTrees,
        iterateResolveTree: iterateResolveTree
    });

    function isNodeInLinkedList(n) {
        return '__ln' in n;
    }
    var DoubleLinkedList = (function () {
        function DoubleLinkedList() {
            this.length = 0;
            this.head = null;
        }
        DoubleLinkedList.prototype.get = function (position) {
            if (position >= this.length) {
                throw new Error('Position outside of list range');
            }
            var current = this.head;
            for (var index = 0; index < position; index++) {
                current = (current === null || current === void 0 ? void 0 : current.next) || null;
            }
            return current;
        };
        DoubleLinkedList.prototype.addNode = function (n) {
            var node = {
                value: n,
                previous: null,
                next: null,
            };
            n.__ln = node;
            if (n.previousSibling && isNodeInLinkedList(n.previousSibling)) {
                var current = n.previousSibling.__ln.next;
                node.next = current;
                node.previous = n.previousSibling.__ln;
                n.previousSibling.__ln.next = node;
                if (current) {
                    current.previous = node;
                }
            }
            else if (n.nextSibling && isNodeInLinkedList(n.nextSibling)) {
                var current = n.nextSibling.__ln.previous;
                node.previous = current;
                node.next = n.nextSibling.__ln;
                n.nextSibling.__ln.previous = node;
                if (current) {
                    current.next = node;
                }
            }
            else {
                if (this.head) {
                    this.head.previous = node;
                }
                node.next = this.head;
                this.head = node;
            }
            this.length++;
        };
        DoubleLinkedList.prototype.removeNode = function (n) {
            var current = n.__ln;
            if (!this.head) {
                return;
            }
            if (!current.previous) {
                this.head = current.next;
                if (this.head) {
                    this.head.previous = null;
                }
            }
            else {
                current.previous.next = current.next;
                if (current.next) {
                    current.next.previous = current.previous;
                }
            }
            if (n.__ln) {
                delete n.__ln;
            }
            this.length--;
        };
        return DoubleLinkedList;
    }());
    var moveKey = function (id, parentId) { return id + "@" + parentId; };
    function isINode(n) {
        return '__sn' in n;
    }
    var MutationBuffer = (function () {
        function MutationBuffer() {
            var _this = this;
            this.frozen = false;
            this.texts = [];
            this.attributes = [];
            this.removes = [];
            this.mapRemoves = [];
            this.movedMap = {};
            this.addedSet = new Set();
            this.movedSet = new Set();
            this.droppedSet = new Set();
            this.processMutations = function (mutations) {
                //console.log('MutationObserver--元素改变事件', mutations)
                mutations.forEach(_this.processMutation);
                if (!_this.frozen) {
                    _this.emit();
                }
            };
            this.emit = function () {
                var e_1, _a, e_2, _b;
                var adds = [];
                var addList = new DoubleLinkedList();
                var getNextId = function (n) {
                    var nextId = n.nextSibling && mirror.getId(n.nextSibling);
                    if (nextId === -1 && isBlocked(n.nextSibling, _this.blockClass)) {
                        nextId = null;
                    }
                    return nextId;
                };
                var pushAdd = function (n) {
                    if (!n.parentNode) {
                        return;
                    }
                    var parentId = mirror.getId(n.parentNode);
                    var nextId = getNextId(n);
                    if (parentId === -1 || nextId === -1) {
                        return addList.addNode(n);
                    }
                    adds.push({
                        parentId: parentId,
                        nextId: nextId,
                        node: serializeNodeWithId(n, document, mirror.map, _this.blockClass, null, true, _this.inlineStylesheet, _this.maskInputOptions, undefined, _this.recordCanvas),
                    });
                };
                while (_this.mapRemoves.length) {
                    mirror.removeNodeFromMap(_this.mapRemoves.shift());
                }
                try {
                    for (var _c = __values(_this.movedSet), _d = _c.next(); !_d.done; _d = _c.next()) {
                        var n = _d.value;
                        if (isParentRemoved(_this.removes, n) &&
                            !_this.movedSet.has(n.parentNode)) {
                            continue;
                        }
                        pushAdd(n);
                    }
                }
                catch (e_1_1) { e_1 = { error: e_1_1 }; }
                finally {
                    try {
                        if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
                    }
                    finally { if (e_1) throw e_1.error; }
                }
                try {
                    for (var _e = __values(_this.addedSet), _f = _e.next(); !_f.done; _f = _e.next()) {
                        var n = _f.value;
                        if (!isAncestorInSet(_this.droppedSet, n) &&
                            !isParentRemoved(_this.removes, n)) {
                            pushAdd(n);
                        }
                        else if (isAncestorInSet(_this.movedSet, n)) {
                            pushAdd(n);
                        }
                        else {
                            _this.droppedSet.add(n);
                        }
                    }
                }
                catch (e_2_1) { e_2 = { error: e_2_1 }; }
                finally {
                    try {
                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
                    }
                    finally { if (e_2) throw e_2.error; }
                }
                var candidate = null;
                while (addList.length) {
                    var node = null;
                    if (candidate) {
                        var parentId = mirror.getId(candidate.value.parentNode);
                        var nextId = getNextId(candidate.value);
                        if (parentId !== -1 && nextId !== -1) {
                            node = candidate;
                        }
                    }
                    if (!node) {
                        for (var index = addList.length - 1; index >= 0; index--) {
                            var _node = addList.get(index);
                            var parentId = mirror.getId(_node.value.parentNode);
                            var nextId = getNextId(_node.value);
                            if (parentId !== -1 && nextId !== -1) {
                                node = _node;
                                break;
                            }
                        }
                    }
                    if (!node) {
                        break;
                    }
                    candidate = node.previous;
                    addList.removeNode(node.value);
                    pushAdd(node.value);
                }
                var payload = {
                    texts: _this.texts
                        .map(function (text) {
                            return ({
                                id: mirror.getId(text.node),
                                value: text.value,
                            });
                        })
                        .filter(function (text) { return mirror.has(text.id); }),
                    attributes: _this.attributes
                        .map(function (attribute) {
                            return ({
                                id: mirror.getId(attribute.node),
                                attributes: attribute.attributes,
                            });
                        })
                        .filter(function (attribute) { return mirror.has(attribute.id); }),
                    removes: _this.removes,
                    adds: adds,
                };
                if (!payload.texts.length &&
                    !payload.attributes.length &&
                    !payload.removes.length &&
                    !payload.adds.length) {
                    return;
                }
                _this.texts = [];
                _this.attributes = [];
                _this.removes = [];
                _this.addedSet = new Set();
                _this.movedSet = new Set();
                _this.droppedSet = new Set();
                _this.movedMap = {};
                _this.emissionCallback(payload);
            };
            this.processMutation = function (m) {
                switch (m.type) {
                    case 'characterData': {
                        var value = m.target.textContent;
                        if (!isBlocked(m.target, _this.blockClass) && value !== m.oldValue) {
                            _this.texts.push({
                                value: value,
                                node: m.target,
                            });
                        }
                        break;
                    }
                    case 'attributes': {
                        var value = m.target.getAttribute(m.attributeName);
                        if (isBlocked(m.target, _this.blockClass) || value === m.oldValue) {
                            return;
                        }
                        var item = _this.attributes.find(function (a) { return a.node === m.target; });
                        if (!item) {
                            item = {
                                node: m.target,
                                attributes: {},
                            };
                            _this.attributes.push(item);
                        }
                        item.attributes[m.attributeName] = transformAttribute(document, m.attributeName, value);
                        break;
                    }
                    case 'childList': {
                        m.addedNodes.forEach(function (n) { return _this.genAdds(n, m.target); });
                        m.removedNodes.forEach(function (n) {
                            var nodeId = mirror.getId(n);
                            var parentId = mirror.getId(m.target);
                            if (isBlocked(n, _this.blockClass) ||
                                isBlocked(m.target, _this.blockClass)) {
                                return;
                            }
                            if (_this.addedSet.has(n)) {
                                deepDelete(_this.addedSet, n);
                                _this.droppedSet.add(n);
                            }
                            else if (_this.addedSet.has(m.target) && nodeId === -1);
                            else if (isAncestorRemoved(m.target));
                            else if (_this.movedSet.has(n) &&
                                _this.movedMap[moveKey(nodeId, parentId)]) {
                                deepDelete(_this.movedSet, n);
                            }
                            else {
                                _this.removes.push({
                                    parentId: parentId,
                                    id: nodeId,
                                });
                            }
                            _this.mapRemoves.push(n);
                        });
                        break;
                    }
                }
            };
            this.genAdds = function (n, target) {
                if (isBlocked(n, _this.blockClass)) {
                    return;
                }
                if (isINode(n)) {
                    _this.movedSet.add(n);
                    var targetId = null;
                    if (target && isINode(target)) {
                        targetId = target.__sn.id;
                    }
                    if (targetId) {
                        _this.movedMap[moveKey(n.__sn.id, targetId)] = true;
                    }
                }
                else {
                    _this.addedSet.add(n);
                    _this.droppedSet.delete(n);
                }
                n.childNodes.forEach(function (childN) { return _this.genAdds(childN); });
            };
        }
        MutationBuffer.prototype.init = function (cb, blockClass, inlineStylesheet, maskInputOptions, recordCanvas) {
            this.blockClass = blockClass;
            this.inlineStylesheet = inlineStylesheet;
            this.maskInputOptions = maskInputOptions;
            this.recordCanvas = recordCanvas;
            this.emissionCallback = cb;
        };
        MutationBuffer.prototype.freeze = function () {
            this.frozen = true;
        };
        MutationBuffer.prototype.unfreeze = function () {
            this.frozen = false;
        };
        MutationBuffer.prototype.isFrozen = function () {
            return this.frozen;
        };
        return MutationBuffer;
    }());
    function deepDelete(addsSet, n) {
        addsSet.delete(n);
        n.childNodes.forEach(function (childN) { return deepDelete(addsSet, childN); });
    }
    function isParentRemoved(removes, n) {
        var parentNode = n.parentNode;
        if (!parentNode) {
            return false;
        }
        var parentId = mirror.getId(parentNode);
        if (removes.some(function (r) { return r.id === parentId; })) {
            return true;
        }
        return isParentRemoved(removes, parentNode);
    }
    function isAncestorInSet(set, n) {
        var parentNode = n.parentNode;
        if (!parentNode) {
            return false;
        }
        if (set.has(parentNode)) {
            return true;
        }
        return isAncestorInSet(set, parentNode);
    }

    var mutationBuffer = new MutationBuffer();
    function initMutationObserver(cb, blockClass, inlineStylesheet, maskInputOptions, recordCanvas) {
        mutationBuffer.init(cb, blockClass, inlineStylesheet, maskInputOptions, recordCanvas);
        var observer = new MutationObserver(mutationBuffer.processMutations.bind(mutationBuffer));
        observer.observe(document, {
            attributes: true,
            attributeOldValue: true,
            characterData: true,
            characterDataOldValue: true,
            childList: true,
            subtree: true,
        });
        console.log(' //监听MutationObserver')
         //监听scroll事件
        return observer;
    }
    function initMoveObserver(cb, sampling) {
        if (sampling.mousemove === false) {
            return function () { };
        }
        var threshold = typeof sampling.mousemove === 'number' ? sampling.mousemove : 50;
        var positions = [];
        var timeBaseline;
        var wrappedCb = throttle(function (isTouch) {
            var totalOffset = Date.now() - timeBaseline;
            cb(positions.map(function (p) {
                p.timeOffset -= totalOffset;
                return p;
            }), isTouch ? exports.IncrementalSource.TouchMove : exports.IncrementalSource.MouseMove);
            positions = [];
            timeBaseline = null;
        }, 500);
        var updatePosition = throttle(function (evt) {
            var target = evt.target;
            var _a = isTouchEvent(evt)
                ? evt.changedTouches[0]
                : evt, clientX = _a.clientX, clientY = _a.clientY;
            if (!timeBaseline) {
                timeBaseline = Date.now();
            }
            positions.push({
                x: clientX,
                y: clientY,
                id: mirror.getId(target),
                timeOffset: Date.now() - timeBaseline,
            });
            wrappedCb(isTouchEvent(evt));
        }, threshold, {
            trailing: false,
        });
        var handlers = [
            on('mousemove', updatePosition),
            on('touchmove', updatePosition),
        ];
        return function () {
            handlers.forEach(function (h) { return h(); });
        };
    }
    function initMouseInteractionObserver(cb, blockClass, sampling) {
        if (sampling.mouseInteraction === false) {
            return function () { };
        }
        var disableMap = sampling.mouseInteraction === true ||
            sampling.mouseInteraction === undefined
            ? {}
            : sampling.mouseInteraction;
        var handlers = [];
        var getHandler = function (eventKey) {
            return function (event) {
                if (isBlocked(event.target, blockClass)) {
                    return;
                }
                var id = mirror.getId(event.target);
                var _a = isTouchEvent(event)
                    ? event.changedTouches[0]
                    : event, clientX = _a.clientX, clientY = _a.clientY;
                cb({
                    type: exports.MouseInteractions[eventKey],
                    id: id,
                    x: clientX,
                    y: clientY,
                });
            };
        };
        Object.keys(exports.MouseInteractions)
            .filter(function (key) {
                return Number.isNaN(Number(key)) &&
                    !key.endsWith('_Departed') &&
                    disableMap[key] !== false;
            })
            .forEach(function (eventKey) {
                var eventName = eventKey.toLowerCase();
                var handler = getHandler(eventKey);
                handlers.push(on(eventName, handler));
            });
        return function () {
            handlers.forEach(function (h) { return h(); });
        };
    }
    //监听scroll事件
    function initScrollObserver(cb, blockClass, sampling) {
        var updatePosition = throttle(function (evt) {
            if (!evt.target || isBlocked(evt.target, blockClass)) {
                return;
            }
            var id = mirror.getId(evt.target);
            if (evt.target === document) {
                var scrollEl = (document.scrollingElement || document.documentElement);
                cb({
                    id: id,
                    x: scrollEl.scrollLeft,
                    y: scrollEl.scrollTop,
                });
            }
            else {
                cb({
                    id: id,
                    x: evt.target.scrollLeft,
                    y: evt.target.scrollTop,
                });
            }
            console.log('监听scroll事件');
        }, sampling.scroll || 100);
        return on('scroll', updatePosition);
    }

    //console.log('监听窗口大小改变事件');
    function initViewportResizeObserver(cb) {
        var updateDimension = throttle(function () {
            var height = getWindowHeight();
            var width = getWindowWidth();
            cb({
                width: Number(width),
                height: Number(height),
            });
            console.log('监听窗口大小改变事件');
        }, 200);
        return on('resize', updateDimension, window);
    }
    //console.log('表单change');
    var INPUT_TAGS = ['INPUT', 'TEXTAREA', 'SELECT'];
    var lastInputValueMap = new WeakMap();
    function initInputObserver(cb, blockClass, ignoreClass, maskInputOptions, sampling) {
        function eventHandler(event) {
            console.log('表单change');
            var target = event.target;
            if (!target ||
                !target.tagName ||
                INPUT_TAGS.indexOf(target.tagName) < 0 ||
                isBlocked(target, blockClass)) {
                return;
            }
            var type = target.type;
            if (type === 'password' ||
                target.classList.contains(ignoreClass)) {
                return;
            }
            var text = target.value;
            var isChecked = false;
            if (type === 'radio' || type === 'checkbox') {
                isChecked = target.checked;
            }
            else if (maskInputOptions[target.tagName.toLowerCase()] ||
                maskInputOptions[type]) {
                text = '*'.repeat(text.length);
            }
            cbWithDedup(target, { text: text, isChecked: isChecked });
            var name = target.name;
            if (type === 'radio' && name && isChecked) {
                document
                    .querySelectorAll("input[type=\"radio\"][name=\"" + name + "\"]")
                    .forEach(function (el) {
                        if (el !== target) {
                            cbWithDedup(el, {
                                text: el.value,
                                isChecked: !isChecked,
                            });
                        }
                    });
            }
        }
        function cbWithDedup(target, v) {
            var lastInputValue = lastInputValueMap.get(target);
            if (!lastInputValue ||
                lastInputValue.text !== v.text ||
                lastInputValue.isChecked !== v.isChecked) {
                lastInputValueMap.set(target, v);
                var id = mirror.getId(target);
                cb(__assign(__assign({}, v), { id: id }));
            }
        }
        var events = sampling.input === 'last' ? ['change'] : ['input', 'change'];
        var handlers = events.map(function (eventName) { return on(eventName, eventHandler); });
        var propertyDescriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
        var hookProperties = [
            [HTMLInputElement.prototype, 'value'],
            [HTMLInputElement.prototype, 'checked'],
            [HTMLSelectElement.prototype, 'value'],
            [HTMLTextAreaElement.prototype, 'value'],
            [HTMLSelectElement.prototype, 'selectedIndex'],
        ];
        if (propertyDescriptor && propertyDescriptor.set) {
            handlers.push.apply(handlers, __spread(hookProperties.map(function (p) {
                return hookSetter(p[0], p[1], {
                    set: function () {
                        eventHandler({ target: this });
                    },
                });
            })));
        }
        return function () {
            handlers.forEach(function (h) { return h(); });
        };
    }
    function initStyleSheetObserver(cb) {
        var insertRule = CSSStyleSheet.prototype.insertRule;
        CSSStyleSheet.prototype.insertRule = function (rule, index) {
            var id = mirror.getId(this.ownerNode);
            if (id !== -1) {
                cb({
                    id: id,
                    adds: [{ rule: rule, index: index }],
                });
            }
            return insertRule.apply(this, arguments);
        };
        var deleteRule = CSSStyleSheet.prototype.deleteRule;
        CSSStyleSheet.prototype.deleteRule = function (index) {
            var id = mirror.getId(this.ownerNode);
            if (id !== -1) {
                cb({
                    id: id,
                    removes: [{ index: index }],
                });
            }
            return deleteRule.apply(this, arguments);
        };
        return function () {
            CSSStyleSheet.prototype.insertRule = insertRule;
            CSSStyleSheet.prototype.deleteRule = deleteRule;
        };
    }
    function initMediaInteractionObserver(mediaInteractionCb, blockClass) {
        var handler = function (type) {
            return function (event) {
                var target = event.target;
                if (!target || isBlocked(target, blockClass)) {
                    return;
                }
                mediaInteractionCb({
                    type: type === 'play' ? MediaInteractions.Play : MediaInteractions.Pause,
                    id: mirror.getId(target),
                });
            };
        };
        var handlers = [on('play', handler('play')), on('pause', handler('pause'))];
        return function () {
            handlers.forEach(function (h) { return h(); });
        };
    }
    function initCanvasMutationObserver(cb, blockClass) {
        var e_1, _a;
        var props = Object.getOwnPropertyNames(CanvasRenderingContext2D.prototype);
        var handlers = [];
        var _loop_1 = function (prop) {
            try {
                if (typeof CanvasRenderingContext2D.prototype[prop] !== 'function') {
                    return "continue";
                }
                var restoreHandler = patch(CanvasRenderingContext2D.prototype, prop, function (original) {
                    return function () {
                        var _this = this;
                        var args = [];
                        for (var _i = 0; _i < arguments.length; _i++) {
                            args[_i] = arguments[_i];
                        }
                        if (!isBlocked(this.canvas, blockClass)) {
                            setTimeout(function () {
                                var recordArgs = __spread(args);
                                if (prop === 'drawImage') {
                                    if (recordArgs[0] &&
                                        recordArgs[0] instanceof HTMLCanvasElement) {
                                        recordArgs[0] = recordArgs[0].toDataURL();
                                    }
                                }
                                cb({
                                    id: mirror.getId(_this.canvas),
                                    property: prop,
                                    args: recordArgs,
                                });
                            }, 0);
                        }
                        return original.apply(this, args);
                    };
                });
                handlers.push(restoreHandler);
            }
            catch (_a) {
                var hookHandler = hookSetter(CanvasRenderingContext2D.prototype, prop, {
                    set: function (v) {
                        cb({
                            id: mirror.getId(this.canvas),
                            property: prop,
                            args: [v],
                            setter: true,
                        });
                    },
                });
                handlers.push(hookHandler);
            }
        };
        try {
            for (var props_1 = __values(props), props_1_1 = props_1.next(); !props_1_1.done; props_1_1 = props_1.next()) {
                var prop = props_1_1.value;
                _loop_1(prop);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (props_1_1 && !props_1_1.done && (_a = props_1.return)) _a.call(props_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return function () {
            handlers.forEach(function (h) { return h(); });
        };
    }
    function initFontObserver(cb) {
        var handlers = [];
        var fontMap = new WeakMap();
        var originalFontFace = FontFace;
        window.FontFace = function FontFace(family, source, descriptors) {
            var fontFace = new originalFontFace(family, source, descriptors);
            fontMap.set(fontFace, {
                family: family,
                buffer: typeof source !== 'string',
                descriptors: descriptors,
                fontSource: typeof source === 'string'
                    ? source
                    :
                    JSON.stringify(Array.from(new Uint8Array(source))),
            });
            return fontFace;
        };
        var restoreHandler = patch(document.fonts, 'add', function (original) {
            return function (fontFace) {
                setTimeout(function () {
                    var p = fontMap.get(fontFace);
                    if (p) {
                        cb(p);
                        fontMap.delete(fontFace);
                    }
                }, 0);
                return original.apply(this, [fontFace]);
            };
        });
        handlers.push(function () {
            window.FonFace = originalFontFace;
        });
        handlers.push(restoreHandler);
        return function () {
            handlers.forEach(function (h) { return h(); });
        };
    }
    function mergeHooks(o, hooks) {
        var mutationCb = o.mutationCb, mousemoveCb = o.mousemoveCb, mouseInteractionCb = o.mouseInteractionCb, scrollCb = o.scrollCb, viewportResizeCb = o.viewportResizeCb, inputCb = o.inputCb, mediaInteractionCb = o.mediaInteractionCb, styleSheetRuleCb = o.styleSheetRuleCb, canvasMutationCb = o.canvasMutationCb, fontCb = o.fontCb;
        o.mutationCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.mutation) {
                hooks.mutation.apply(hooks, __spread(p));
            }
            mutationCb.apply(void 0, __spread(p));
        };
        o.mousemoveCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.mousemove) {
                hooks.mousemove.apply(hooks, __spread(p));
            }
            mousemoveCb.apply(void 0, __spread(p));
        };
        o.mouseInteractionCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.mouseInteraction) {
                hooks.mouseInteraction.apply(hooks, __spread(p));
            }
            mouseInteractionCb.apply(void 0, __spread(p));
        };
        o.scrollCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.scroll) {
                hooks.scroll.apply(hooks, __spread(p));
            }
            scrollCb.apply(void 0, __spread(p));
        };
        o.viewportResizeCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.viewportResize) {
                hooks.viewportResize.apply(hooks, __spread(p));
            }
            viewportResizeCb.apply(void 0, __spread(p));
        };
        o.inputCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.input) {
                hooks.input.apply(hooks, __spread(p));
            }
            inputCb.apply(void 0, __spread(p));
        };
        o.mediaInteractionCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.mediaInteaction) {
                hooks.mediaInteaction.apply(hooks, __spread(p));
            }
            mediaInteractionCb.apply(void 0, __spread(p));
        };
        o.styleSheetRuleCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.styleSheetRule) {
                hooks.styleSheetRule.apply(hooks, __spread(p));
            }
            styleSheetRuleCb.apply(void 0, __spread(p));
        };
        o.canvasMutationCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.canvasMutation) {
                hooks.canvasMutation.apply(hooks, __spread(p));
            }
            canvasMutationCb.apply(void 0, __spread(p));
        };
        o.fontCb = function () {
            var p = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                p[_i] = arguments[_i];
            }
            if (hooks.font) {
                hooks.font.apply(hooks, __spread(p));
            }
            fontCb.apply(void 0, __spread(p));
        };
    }
    function initObservers(o, hooks) {
        if (hooks === void 0) { hooks = {}; }
        mergeHooks(o, hooks);
        var mutationObserver = initMutationObserver(o.mutationCb, o.blockClass, o.inlineStylesheet, o.maskInputOptions, o.recordCanvas);
        var mousemoveHandler = initMoveObserver(o.mousemoveCb, o.sampling);
        var mouseInteractionHandler = initMouseInteractionObserver(o.mouseInteractionCb, o.blockClass, o.sampling);
        var scrollHandler = initScrollObserver(o.scrollCb, o.blockClass, o.sampling);
        var viewportResizeHandler = initViewportResizeObserver(o.viewportResizeCb);
        var inputHandler = initInputObserver(o.inputCb, o.blockClass, o.ignoreClass, o.maskInputOptions, o.sampling);
        var mediaInteractionHandler = initMediaInteractionObserver(o.mediaInteractionCb, o.blockClass);
        var styleSheetObserver = initStyleSheetObserver(o.styleSheetRuleCb);
        var canvasMutationObserver = o.recordCanvas
            ? initCanvasMutationObserver(o.canvasMutationCb, o.blockClass)
            : function () { };
        var fontObserver = o.collectFonts ? initFontObserver(o.fontCb) : function () { };
        return function () {
            mutationObserver.disconnect();
            mousemoveHandler();
            mouseInteractionHandler();
            scrollHandler();
            viewportResizeHandler();
            inputHandler();
            mediaInteractionHandler();
            styleSheetObserver();
            canvasMutationObserver();
            fontObserver();
        };
    }

    function wrapEvent(e) {
        return __assign(__assign({}, e), { timestamp: Date.now() });
    }
    var wrappedEmit;
    function record(options) {
        if (options === void 0) { options = {}; }
        var emit = options.emit, checkoutEveryNms = options.checkoutEveryNms, checkoutEveryNth = options.checkoutEveryNth, _a = options.blockClass, blockClass = _a === void 0 ? 'vc-panel' : _a, _b = options.ignoreClass, ignoreClass = _b === void 0 ? 'rr-ignore' : _b, _c = options.inlineStylesheet, inlineStylesheet = _c === void 0 ? true : _c, maskAllInputs = options.maskAllInputs, _maskInputOptions = options.maskInputOptions, hooks = options.hooks, packFn = options.packFn, _d = options.sampling, sampling = _d === void 0 ? {} : _d, mousemoveWait = options.mousemoveWait, _e = options.recordCanvas, recordCanvas = _e === void 0 ? false : _e, _f = options.collectFonts, collectFonts = _f === void 0 ? false : _f;
        if (!emit) {
            throw new Error('emit function is required');
        }
        if (mousemoveWait !== undefined && sampling.mousemove === undefined) {
            sampling.mousemove = mousemoveWait;
        }
        var maskInputOptions = maskAllInputs === true
            ? {
                color: true,
                date: true,
                'datetime-local': true,
                email: true,
                month: true,
                number: true,
                range: true,
                search: true,
                tel: true,
                text: true,
                time: true,
                url: true,
                week: true,
                textarea: true,
                select: true,
            }
            : _maskInputOptions !== undefined
                ? _maskInputOptions
                : {};
        polyfill();
        var lastFullSnapshotEvent;
        var incrementalSnapshotCount = 0;
        wrappedEmit = function (e, isCheckout) {
            if (mutationBuffer.isFrozen() &&
                e.type !== exports.EventType.FullSnapshot &&
                !(e.type == exports.EventType.IncrementalSnapshot &&
                    e.data.source == exports.IncrementalSource.Mutation)) {
                mutationBuffer.emit();
                mutationBuffer.unfreeze();
            }
            emit((packFn ? packFn(e) : e), isCheckout);
            if (e.type === exports.EventType.FullSnapshot) {
                lastFullSnapshotEvent = e;
                incrementalSnapshotCount = 0;
            }
            else if (e.type === exports.EventType.IncrementalSnapshot) {
                incrementalSnapshotCount++;
                var exceedCount = checkoutEveryNth && incrementalSnapshotCount >= checkoutEveryNth;
                var exceedTime = checkoutEveryNms &&
                    e.timestamp - lastFullSnapshotEvent.timestamp > checkoutEveryNms;
                if (exceedCount || exceedTime) {
                    takeFullSnapshot(true);
                }
            }
        };
        function takeFullSnapshot(isCheckout) {
            var _a, _b, _c, _d;
            if (isCheckout === void 0) { isCheckout = false; }
            wrappedEmit(wrapEvent({
                type: exports.EventType.Meta,
                data: {
                    href: window.location.href,
                    width: getWindowWidth(),
                    height: getWindowHeight(),
                },
            }), isCheckout);
            var wasFrozen = mutationBuffer.isFrozen();
            mutationBuffer.freeze();
            var _e = __read(snapshot(document, blockClass, inlineStylesheet, maskInputOptions, false, recordCanvas), 2), node = _e[0], idNodeMap = _e[1];
            if (!node) {
                return console.warn('Failed to snapshot the document');
            }
            mirror.map = idNodeMap;
            wrappedEmit(wrapEvent({
                type: exports.EventType.FullSnapshot,
                data: {
                    node: node,
                    initialOffset: {
                        left: window.pageXOffset !== undefined
                            ? window.pageXOffset
                            : (document === null || document === void 0 ? void 0 : document.documentElement.scrollLeft) || ((_b = (_a = document === null || document === void 0 ? void 0 : document.body) === null || _a === void 0 ? void 0 : _a.parentElement) === null || _b === void 0 ? void 0 : _b.scrollLeft) || (document === null || document === void 0 ? void 0 : document.body.scrollLeft) ||
                            0,
                        top: window.pageYOffset !== undefined
                            ? window.pageYOffset
                            : (document === null || document === void 0 ? void 0 : document.documentElement.scrollTop) || ((_d = (_c = document === null || document === void 0 ? void 0 : document.body) === null || _c === void 0 ? void 0 : _c.parentElement) === null || _d === void 0 ? void 0 : _d.scrollTop) || (document === null || document === void 0 ? void 0 : document.body.scrollTop) ||
                            0,
                    },
                },
            }));
            if (!wasFrozen) {
                mutationBuffer.emit();
                mutationBuffer.unfreeze();
            }
        }
        try {
            var handlers_1 = [];
            handlers_1.push(on('DOMContentLoaded', function () {
                wrappedEmit(wrapEvent({
                    type: exports.EventType.DomContentLoaded,
                    data: {},
                }));
            }));
            var init_1 = function () {
                takeFullSnapshot();
                handlers_1.push(initObservers({
                    mutationCb: function (m) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.Mutation }, m),
                        }));
                    },
                    mousemoveCb: function (positions, source) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: {
                                source: source,
                                positions: positions,
                            },
                        }));
                    },
                    mouseInteractionCb: function (d) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.MouseInteraction }, d),
                        }));
                    },
                    scrollCb: function (p) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.Scroll }, p),
                        }));
                    },
                    viewportResizeCb: function (d) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.ViewportResize }, d),
                        }));
                    },
                    inputCb: function (v) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.Input }, v),
                        }));
                    },
                    mediaInteractionCb: function (p) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.MediaInteraction }, p),
                        }));
                    },
                    styleSheetRuleCb: function (r) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.StyleSheetRule }, r),
                        }));
                    },
                    canvasMutationCb: function (p) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.CanvasMutation }, p),
                        }));
                    },
                    fontCb: function (p) {
                        return wrappedEmit(wrapEvent({
                            type: exports.EventType.IncrementalSnapshot,
                            data: __assign({ source: exports.IncrementalSource.Font }, p),
                        }));
                    },
                    blockClass: blockClass,
                    ignoreClass: ignoreClass,
                    maskInputOptions: maskInputOptions,
                    inlineStylesheet: inlineStylesheet,
                    sampling: sampling,
                    recordCanvas: recordCanvas,
                    collectFonts: collectFonts,
                }, hooks));
            };
            if (document.readyState === 'interactive' ||
                document.readyState === 'complete') {
                init_1();
            }
            else {
                handlers_1.push(on('load', function () {
                    wrappedEmit(wrapEvent({
                        type: exports.EventType.Load,
                        data: {},
                    }));
                    init_1();
                }, window));
            }
            return function () {
                handlers_1.forEach(function (h) { return h(); });
            };
        }
        catch (error) {
            console.warn(error);
        }
    }
    record.addCustomEvent = function (tag, payload) {
        if (!wrappedEmit) {
            throw new Error('please add custom event after start recording');
        }
        wrappedEmit(wrapEvent({
            type: exports.EventType.Custom,
            data: {
                tag: tag,
                payload: payload,
            },
        }));
    };
    record.freezePage = function () {
        mutationBuffer.freeze();
    };

    //
    // An event handler can take an optional event argument
    // and should not return a value



    // An array of all currently registered event handlers for a type


    // A map of event types and their corresponding event handlers.





    /** Mitt: Tiny (~200b) functional event emitter / pubsub.
     *  @name mitt
     *  @returns {Mitt}
     */
    function mitt(all) {
        all = all || Object.create(null);

        return {
            /**
             * Register an event handler for the given type.
             *
             * @param  {String} type	Type of event to listen for, or `"*"` for all events
             * @param  {Function} handler Function to call in response to given event
             * @memberOf mitt
             */
            on: function on(type, handler) {
                (all[type] || (all[type] = [])).push(handler);
            },

            /**
             * Remove an event handler for the given type.
             *
             * @param  {String} type	Type of event to unregister `handler` from, or `"*"`
             * @param  {Function} handler Handler function to remove
             * @memberOf mitt
             */
            off: function off(type, handler) {
                if (all[type]) {
                    all[type].splice(all[type].indexOf(handler) >>> 0, 1);
                }
            },

            /**
             * Invoke all handlers for the given type.
             * If present, `"*"` handlers are invoked after type-matched handlers.
             *
             * @param {String} type  The event type to invoke
             * @param {Any} [evt]  Any value (object is recommended and powerful), passed to each handler
             * @memberOf mitt
             */
            emit: function emit(type, evt) {
                (all[type] || []).slice().map(function (handler) { handler(evt); });
                (all['*'] || []).slice().map(function (handler) { handler(type, evt); });
            }
        };
    }

    var mittProxy = /*#__PURE__*/Object.freeze({
        __proto__: null,
        'default': mitt
    });

    function polyfill$1(w, d) {
        if (w === void 0) { w = window; }
        if (d === void 0) { d = document; }
        if ('scrollBehavior' in d.documentElement.style &&
            w.__forceSmoothScrollPolyfill__ !== true) {
            return;
        }
        var Element = w.HTMLElement || w.Element;
        var SCROLL_TIME = 468;
        var original = {
            scroll: w.scroll || w.scrollTo,
            scrollBy: w.scrollBy,
            elementScroll: Element.prototype.scroll || scrollElement,
            scrollIntoView: Element.prototype.scrollIntoView,
        };
        var now = w.performance && w.performance.now
            ? w.performance.now.bind(w.performance)
            : Date.now;
        function isMicrosoftBrowser(userAgent) {
            var userAgentPatterns = ['MSIE ', 'Trident/', 'Edge/'];
            return new RegExp(userAgentPatterns.join('|')).test(userAgent);
        }
        var ROUNDING_TOLERANCE = isMicrosoftBrowser(w.navigator.userAgent) ? 1 : 0;
        function scrollElement(x, y) {
            this.scrollLeft = x;
            this.scrollTop = y;
        }
        function ease(k) {
            return 0.5 * (1 - Math.cos(Math.PI * k));
        }
        function shouldBailOut(firstArg) {
            if (firstArg === null ||
                typeof firstArg !== 'object' ||
                firstArg.behavior === undefined ||
                firstArg.behavior === 'auto' ||
                firstArg.behavior === 'instant') {
                return true;
            }
            if (typeof firstArg === 'object' && firstArg.behavior === 'smooth') {
                return false;
            }
            throw new TypeError('behavior member of ScrollOptions ' +
                firstArg.behavior +
                ' is not a valid value for enumeration ScrollBehavior.');
        }
        function hasScrollableSpace(el, axis) {
            if (axis === 'Y') {
                return el.clientHeight + ROUNDING_TOLERANCE < el.scrollHeight;
            }
            if (axis === 'X') {
                return el.clientWidth + ROUNDING_TOLERANCE < el.scrollWidth;
            }
        }
        function canOverflow(el, axis) {
            var overflowValue = w.getComputedStyle(el, null)['overflow' + axis];
            return overflowValue === 'auto' || overflowValue === 'scroll';
        }
        function isScrollable(el) {
            var isScrollableY = hasScrollableSpace(el, 'Y') && canOverflow(el, 'Y');
            var isScrollableX = hasScrollableSpace(el, 'X') && canOverflow(el, 'X');
            return isScrollableY || isScrollableX;
        }
        function findScrollableParent(el) {
            while (el !== d.body && isScrollable(el) === false) {
                el = el.parentNode || el.host;
            }
            return el;
        }
        function step(context) {
            var time = now();
            var value;
            var currentX;
            var currentY;
            var elapsed = (time - context.startTime) / SCROLL_TIME;
            elapsed = elapsed > 1 ? 1 : elapsed;
            value = ease(elapsed);
            currentX = context.startX + (context.x - context.startX) * value;
            currentY = context.startY + (context.y - context.startY) * value;
            context.method.call(context.scrollable, currentX, currentY);
            if (currentX !== context.x || currentY !== context.y) {
                w.requestAnimationFrame(step.bind(w, context));
            }
        }
        function smoothScroll(el, x, y) {
            var scrollable;
            var startX;
            var startY;
            var method;
            var startTime = now();
            if (el === d.body) {
                scrollable = w;
                startX = w.scrollX || w.pageXOffset;
                startY = w.scrollY || w.pageYOffset;
                method = original.scroll;
            }
            else {
                scrollable = el;
                startX = el.scrollLeft;
                startY = el.scrollTop;
                method = scrollElement;
            }
            step({
                scrollable: scrollable,
                method: method,
                startTime: startTime,
                startX: startX,
                startY: startY,
                x: x,
                y: y,
            });
        }
        w.scroll = w.scrollTo = function () {
            if (arguments[0] === undefined) {
                return;
            }
            if (shouldBailOut(arguments[0]) === true) {
                original.scroll.call(w, arguments[0].left !== undefined
                    ? arguments[0].left
                    : typeof arguments[0] !== 'object'
                        ? arguments[0]
                        : w.scrollX || w.pageXOffset, arguments[0].top !== undefined
                    ? arguments[0].top
                    : arguments[1] !== undefined
                        ? arguments[1]
                        : w.scrollY || w.pageYOffset);
                return;
            }
            smoothScroll.call(w, d.body, arguments[0].left !== undefined
                ? ~~arguments[0].left
                : w.scrollX || w.pageXOffset, arguments[0].top !== undefined
                ? ~~arguments[0].top
                : w.scrollY || w.pageYOffset);
        };
        w.scrollBy = function () {
            if (arguments[0] === undefined) {
                return;
            }
            if (shouldBailOut(arguments[0])) {
                original.scrollBy.call(w, arguments[0].left !== undefined
                    ? arguments[0].left
                    : typeof arguments[0] !== 'object'
                        ? arguments[0]
                        : 0, arguments[0].top !== undefined
                    ? arguments[0].top
                    : arguments[1] !== undefined
                        ? arguments[1]
                        : 0);
                return;
            }
            smoothScroll.call(w, d.body, ~~arguments[0].left + (w.scrollX || w.pageXOffset), ~~arguments[0].top + (w.scrollY || w.pageYOffset));
        };
        Element.prototype.scroll = Element.prototype.scrollTo = function () {
            if (arguments[0] === undefined) {
                return;
            }
            if (shouldBailOut(arguments[0]) === true) {
                if (typeof arguments[0] === 'number' && arguments[1] === undefined) {
                    throw new SyntaxError('Value could not be converted');
                }
                original.elementScroll.call(this, arguments[0].left !== undefined
                    ? ~~arguments[0].left
                    : typeof arguments[0] !== 'object'
                        ? ~~arguments[0]
                        : this.scrollLeft, arguments[0].top !== undefined
                    ? ~~arguments[0].top
                    : arguments[1] !== undefined
                        ? ~~arguments[1]
                        : this.scrollTop);
                return;
            }
            var left = arguments[0].left;
            var top = arguments[0].top;
            smoothScroll.call(this, this, typeof left === 'undefined' ? this.scrollLeft : ~~left, typeof top === 'undefined' ? this.scrollTop : ~~top);
        };
        Element.prototype.scrollBy = function () {
            if (arguments[0] === undefined) {
                return;
            }
            if (shouldBailOut(arguments[0]) === true) {
                original.elementScroll.call(this, arguments[0].left !== undefined
                    ? ~~arguments[0].left + this.scrollLeft
                    : ~~arguments[0] + this.scrollLeft, arguments[0].top !== undefined
                    ? ~~arguments[0].top + this.scrollTop
                    : ~~arguments[1] + this.scrollTop);
                return;
            }
            this.scroll({
                left: ~~arguments[0].left + this.scrollLeft,
                top: ~~arguments[0].top + this.scrollTop,
                behavior: arguments[0].behavior,
            });
        };
        Element.prototype.scrollIntoView = function () {
            if (shouldBailOut(arguments[0]) === true) {
                original.scrollIntoView.call(this, arguments[0] === undefined ? true : arguments[0]);
                return;
            }
            var scrollableParent = findScrollableParent(this);
            var parentRects = scrollableParent.getBoundingClientRect();
            var clientRects = this.getBoundingClientRect();
            if (scrollableParent !== d.body) {
                smoothScroll.call(this, scrollableParent, scrollableParent.scrollLeft + clientRects.left - parentRects.left, scrollableParent.scrollTop + clientRects.top - parentRects.top);
                if (w.getComputedStyle(scrollableParent).position !== 'fixed') {
                    w.scrollBy({
                        left: parentRects.left,
                        top: parentRects.top,
                        behavior: 'smooth',
                    });
                }
            }
            else {
                w.scrollBy({
                    left: clientRects.left,
                    top: clientRects.top,
                    behavior: 'smooth',
                });
            }
        };
    }

    var Timer = (function () {
        function Timer(actions, speed) {
            if (actions === void 0) { actions = []; }
            this.timeOffset = 0;
            this.raf = null;
            this.actions = actions;
            this.speed = speed;
        }
        Timer.prototype.addAction = function (action) {
            var index = this.findActionIndex(action);
            this.actions.splice(index, 0, action);
        };
        Timer.prototype.addActions = function (actions) {
            var _a;
            (_a = this.actions).push.apply(_a, __spread(actions));
        };
        Timer.prototype.start = function () {
            this.actions.sort(function (a1, a2) { return a1.delay - a2.delay; });
            this.timeOffset = 0;
            var lastTimestamp = performance.now();
            var actions = this.actions;
            var self = this;
            function check(time) {
                self.timeOffset += (time - lastTimestamp) * self.speed;
                lastTimestamp = time;
                while (actions.length) {
                    var action = actions[0];
                    if (self.timeOffset >= action.delay) {
                        actions.shift();
                        action.doAction();
                    }
                    else {
                        break;
                    }
                }
                if (actions.length > 0 || self.liveMode) {
                    self.raf = requestAnimationFrame(check);
                }
            }
            this.raf = requestAnimationFrame(check);
        };
        Timer.prototype.clear = function () {
            if (this.raf) {
                cancelAnimationFrame(this.raf);
                this.raf = null;
            }
            this.actions.length = 0;
        };
        Timer.prototype.setSpeed = function (speed) {
            this.speed = speed;
        };
        Timer.prototype.toggleLiveMode = function (mode) {
            this.liveMode = mode;
        };
        Timer.prototype.isActive = function () {
            return this.raf !== null;
        };
        Timer.prototype.findActionIndex = function (action) {
            var start = 0;
            var end = this.actions.length - 1;
            while (start <= end) {
                var mid = Math.floor((start + end) / 2);
                if (this.actions[mid].delay < action.delay) {
                    start = mid + 1;
                }
                else if (this.actions[mid].delay > action.delay) {
                    end = mid - 1;
                }
                else {
                    return mid;
                }
            }
            return start;
        };
        return Timer;
    }());
    function addDelay(event, baselineTime) {
        if (event.type === exports.EventType.IncrementalSnapshot &&
            event.data.source === exports.IncrementalSource.MouseMove) {
            var firstOffset = event.data.positions[0].timeOffset;
            var firstTimestamp = event.timestamp + firstOffset;
            event.delay = firstTimestamp - baselineTime;
            return firstTimestamp - baselineTime;
        }
        event.delay = event.timestamp - baselineTime;
        return event.delay;
    }

    /*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */
    var t; !function (t) { t[t.NotStarted = 0] = "NotStarted", t[t.Running = 1] = "Running", t[t.Stopped = 2] = "Stopped"; }(t || (t = {})); var n = { type: "xstate.init" }; function e(t) { return void 0 === t ? [] : [].concat(t) } function r(t) { return { type: "xstate.assign", assignment: t } } function i(t, n) { return "string" == typeof (t = "string" == typeof t && n && n[t] ? n[t] : t) ? { type: t } : "function" == typeof t ? { type: t.name, exec: t } : t } function o(t) { return function (n) { return t === n } } function a(t) { return "string" == typeof t ? { type: t } : t } function u(t, n) { return { value: t, context: n, actions: [], changed: !1, matches: o(t) } } function c(t, n) { void 0 === n && (n = {}); var r = { config: t, _options: n, initialState: { value: t.initial, actions: e(t.states[t.initial].entry).map((function (t) { return i(t, n.actions) })), context: t.context, matches: o(t.initial) }, transition: function (n, c) { var s, f, v = "string" == typeof n ? { value: n, context: t.context } : n, l = v.value, p = v.context, g = a(c), y = t.states[l]; if (y.on) { var d = e(y.on[g.type]), x = function (n) { if (void 0 === n) return { value: u(l, p) }; var e = "string" == typeof n ? { target: n } : n, a = e.target, c = void 0 === a ? l : a, s = e.actions, f = void 0 === s ? [] : s, v = e.cond, d = p; if ((void 0 === v ? function () { return !0 } : v)(p, g)) { var x = t.states[c], m = !1, h = [].concat(y.exit, f, x.entry).filter((function (t) { return t })).map((function (t) { return i(t, r._options.actions) })).filter((function (t) { if ("xstate.assign" === t.type) { m = !0; var n = Object.assign({}, d); return "function" == typeof t.assignment ? n = t.assignment(d, g) : Object.keys(t.assignment).forEach((function (e) { n[e] = "function" == typeof t.assignment[e] ? t.assignment[e](d, g) : t.assignment[e]; })), d = n, !1 } return !0 })); return { value: { value: c, context: d, actions: h, changed: c !== l || h.length > 0 || m, matches: o(c) } } } }; try { for (var m = function (t) { var n = "function" == typeof Symbol && t[Symbol.iterator], e = 0; return n ? n.call(t) : { next: function () { return t && e >= t.length && (t = void 0), { value: t && t[e++], done: !t } } } }(d), h = m.next(); !h.done; h = m.next()) { var S = x(h.value); if ("object" == typeof S) return S.value } } catch (t) { s = { error: t }; } finally { try { h && !h.done && (f = m.return) && f.call(m); } finally { if (s) throw s.error } } } return u(l, p) } }; return r } var s = function (t, n) { return t.actions.forEach((function (e) { var r = e.exec; return r && r(t.context, n) })) }; function f(e) { var r = e.initialState, i = t.NotStarted, u = new Set, c = { _machine: e, send: function (n) { i === t.Running && (r = e.transition(r, n), s(r, a(n)), u.forEach((function (t) { return t(r) }))); }, subscribe: function (t) { return u.add(t), t(r), { unsubscribe: function () { return u.delete(t) } } }, start: function (a) { if (a) { var u = "object" == typeof a ? a : { context: e.config.context, value: a }; r = { value: u.value, actions: [], context: u.context, matches: o(u.value) }; } return i = t.Running, s(r, n), c }, stop: function () { return i = t.Stopped, u.clear(), c }, get state() { return r }, get status() { return i } }; return c }

    function discardPriorSnapshots(events, baselineTime) {
        for (var idx = events.length - 1; idx >= 0; idx--) {
            var event = events[idx];
            if (event.type === exports.EventType.Meta) {
                if (event.timestamp <= baselineTime) {
                    return events.slice(idx);
                }
            }
        }
        return events;
    }
    function createPlayerService(context, _a) {
        var getCastFn = _a.getCastFn, emitter = _a.emitter;
        var playerMachine = c({
            id: 'player',
            context: context,
            initial: 'paused',
            states: {
                playing: {
                    on: {
                        PAUSE: {
                            target: 'paused',
                            actions: ['pause'],
                        },
                        CAST_EVENT: {
                            target: 'playing',
                            actions: 'castEvent',
                        },
                        END: {
                            target: 'paused',
                            actions: ['resetLastPlayedEvent', 'pause'],
                        },
                        ADD_EVENT: {
                            target: 'playing',
                            actions: ['addEvent'],
                        },
                    },
                },
                paused: {
                    on: {
                        PLAY: {
                            target: 'playing',
                            actions: ['recordTimeOffset', 'play'],
                        },
                        CAST_EVENT: {
                            target: 'paused',
                            actions: 'castEvent',
                        },
                        TO_LIVE: {
                            target: 'live',
                            actions: ['startLive'],
                        },
                        ADD_EVENT: {
                            target: 'paused',
                            actions: ['addEvent'],
                        },
                    },
                },
                live: {
                    on: {
                        ADD_EVENT: {
                            target: 'live',
                            actions: ['addEvent'],
                        },
                        CAST_EVENT: {
                            target: 'live',
                            actions: ['castEvent'],
                        },
                    },
                },
            },
        }, {
            actions: {
                castEvent: r({
                    lastPlayedEvent: function (ctx, event) {
                        if (event.type === 'CAST_EVENT') {
                            return event.payload.event;
                        }
                        return ctx.lastPlayedEvent;
                    },
                }),
                recordTimeOffset: r(function (ctx, event) {
                    var timeOffset = ctx.timeOffset;
                    if ('payload' in event && 'timeOffset' in event.payload) {
                        timeOffset = event.payload.timeOffset;
                    }
                    return __assign(__assign({}, ctx), { timeOffset: timeOffset, baselineTime: ctx.events[0].timestamp + timeOffset });
                }),
                play: function (ctx) {
                    var e_1, _a, e_2, _b;
                    var _c;
                    var timer = ctx.timer, events = ctx.events, baselineTime = ctx.baselineTime, lastPlayedEvent = ctx.lastPlayedEvent;
                    timer.clear();
                    try {
                        for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {
                            var event = events_1_1.value;
                            addDelay(event, baselineTime);
                        }
                    }
                    catch (e_1_1) { e_1 = { error: e_1_1 }; }
                    finally {
                        try {
                            if (events_1_1 && !events_1_1.done && (_a = events_1.return)) _a.call(events_1);
                        }
                        finally { if (e_1) throw e_1.error; }
                    }
                    var neededEvents = discardPriorSnapshots(events, baselineTime);
                    var actions = new Array();
                    var _loop_1 = function (event) {
                        var lastPlayedTimestamp = lastPlayedEvent === null || lastPlayedEvent === void 0 ? void 0 : lastPlayedEvent.timestamp;
                        if ((lastPlayedEvent === null || lastPlayedEvent === void 0 ? void 0 : lastPlayedEvent.type) === exports.EventType.IncrementalSnapshot &&
                            lastPlayedEvent.data.source === exports.IncrementalSource.MouseMove) {
                            lastPlayedTimestamp =
                                lastPlayedEvent.timestamp + ((_c = lastPlayedEvent.data.positions[0]) === null || _c === void 0 ? void 0 : _c.timeOffset);
                        }
                        if (lastPlayedTimestamp &&
                            lastPlayedTimestamp < baselineTime &&
                            (event.timestamp <= lastPlayedTimestamp ||
                                event === lastPlayedEvent)) {
                            return "continue";
                        }
                        var isSync = event.timestamp < baselineTime;
                        if (isSync && !needCastInSyncMode(event)) {
                            return "continue";
                        }
                        var castFn = getCastFn(event, isSync);
                        if (isSync) {
                            castFn();
                        }
                        else {
                            actions.push({
                                doAction: function () {
                                    castFn();
                                    emitter.emit(exports.ReplayerEvents.EventCast, event);
                                },
                                delay: event.delay,
                            });
                        }
                    };
                    try {
                        for (var neededEvents_1 = __values(neededEvents), neededEvents_1_1 = neededEvents_1.next(); !neededEvents_1_1.done; neededEvents_1_1 = neededEvents_1.next()) {
                            var event = neededEvents_1_1.value;
                            _loop_1(event);
                        }
                    }
                    catch (e_2_1) { e_2 = { error: e_2_1 }; }
                    finally {
                        try {
                            if (neededEvents_1_1 && !neededEvents_1_1.done && (_b = neededEvents_1.return)) _b.call(neededEvents_1);
                        }
                        finally { if (e_2) throw e_2.error; }
                    }
                    emitter.emit(exports.ReplayerEvents.Flush);
                    timer.addActions(actions);
                    timer.start();
                },
                pause: function (ctx) {
                    ctx.timer.clear();
                },
                resetLastPlayedEvent: r(function (ctx) {
                    return __assign(__assign({}, ctx), { lastPlayedEvent: null });
                }),
                startLive: r({
                    baselineTime: function (ctx, event) {
                        ctx.timer.toggleLiveMode(true);
                        ctx.timer.start();
                        if (event.type === 'TO_LIVE' && event.payload.baselineTime) {
                            return event.payload.baselineTime;
                        }
                        return Date.now();
                    },
                }),
                addEvent: r(function (ctx, machineEvent) {
                    var baselineTime = ctx.baselineTime, timer = ctx.timer, events = ctx.events;
                    if (machineEvent.type === 'ADD_EVENT') {
                        var event_1 = machineEvent.payload.event;
                        addDelay(event_1, baselineTime);
                        events.push(event_1);
                        var isSync = event_1.timestamp < baselineTime;
                        var castFn_1 = getCastFn(event_1, isSync);
                        if (isSync) {
                            castFn_1();
                        }
                        else {
                            timer.addAction({
                                doAction: function () {
                                    castFn_1();
                                    emitter.emit(exports.ReplayerEvents.EventCast, event_1);
                                },
                                delay: event_1.delay,
                            });
                            if (!timer.isActive()) {
                                timer.start();
                            }
                        }
                    }
                    return __assign(__assign({}, ctx), { events: events });
                }),
            },
        });
        return f(playerMachine);
    }
    function createSpeedService(context) {
        var speedMachine = c({
            id: 'speed',
            context: context,
            initial: 'normal',
            states: {
                normal: {
                    on: {
                        FAST_FORWARD: {
                            target: 'skipping',
                            actions: ['recordSpeed', 'setSpeed'],
                        },
                        SET_SPEED: {
                            target: 'normal',
                            actions: ['setSpeed'],
                        },
                    },
                },
                skipping: {
                    on: {
                        BACK_TO_NORMAL: {
                            target: 'normal',
                            actions: ['restoreSpeed'],
                        },
                        SET_SPEED: {
                            target: 'normal',
                            actions: ['setSpeed'],
                        },
                    },
                },
            },
        }, {
            actions: {
                setSpeed: function (ctx, event) {
                    if ('payload' in event) {
                        ctx.timer.setSpeed(event.payload.speed);
                    }
                },
                recordSpeed: r({
                    normalSpeed: function (ctx) { return ctx.timer.speed; },
                }),
                restoreSpeed: function (ctx) {
                    ctx.timer.setSpeed(ctx.normalSpeed);
                },
            },
        });
        return f(speedMachine);
    }

    var rules = function (blockClass) {
        return [
            "iframe, ." + blockClass + " { background: #ccc }",
            'noscript { display: none !important; }',
        ];
    };

    var SKIP_TIME_THRESHOLD = 10 * 1000;
    var SKIP_TIME_INTERVAL = 5 * 1000;
    var mitt$1 = mitt || mittProxy;
    var REPLAY_CONSOLE_PREFIX = '[replayer]';
    var defaultMouseTailConfig = {
        duration: 500,
        lineCap: 'round',
        lineWidth: 3,
        strokeStyle: 'red',
    };
    var Replayer = (function () {
        function Replayer(events, config) {
            var _this = this;
            this.mouseTail = null;
            this.tailPositions = [];
            this.emitter = mitt$1();
            this.legacy_missingNodeRetryMap = {};
            this.imageMap = new Map();
            if (!(config === null || config === void 0 ? void 0 : config.liveMode) && events.length < 2) {
                throw new Error('Replayer need at least 2 events.');
            }
            var defaultConfig = {
                speed: 1,
                root: document.body,
                loadTimeout: 0,
                skipInactive: false,
                showWarning: true,
                showDebug: false,
                blockClass: 'vc-panel',
                liveMode: false,
                insertStyleRules: [],
                triggerFocus: true,
                UNSAFE_replayCanvas: false,
                mouseTail: defaultMouseTailConfig,
            };
            this.config = Object.assign({}, defaultConfig, config);
            this.handleResize = this.handleResize.bind(this);
            this.getCastFn = this.getCastFn.bind(this);
            this.emitter.on(exports.ReplayerEvents.Resize, this.handleResize);
            this.setupDom();
            this.treeIndex = new TreeIndex();
            this.fragmentParentMap = new Map();
            this.emitter.on(exports.ReplayerEvents.Flush, function () {
                var e_1, _a, e_2, _b, e_3, _c;
                var _d = _this.treeIndex.flush(), scrollMap = _d.scrollMap, inputMap = _d.inputMap;
                try {
                    for (var _e = __values(scrollMap.values()), _f = _e.next(); !_f.done; _f = _e.next()) {
                        var d = _f.value;
                        _this.applyScroll(d);
                    }
                }
                catch (e_1_1) { e_1 = { error: e_1_1 }; }
                finally {
                    try {
                        if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
                    }
                    finally { if (e_1) throw e_1.error; }
                }
                try {
                    for (var _g = __values(inputMap.values()), _h = _g.next(); !_h.done; _h = _g.next()) {
                        var d = _h.value;
                        _this.applyInput(d);
                    }
                }
                catch (e_2_1) { e_2 = { error: e_2_1 }; }
                finally {
                    try {
                        if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
                    }
                    finally { if (e_2) throw e_2.error; }
                }
                try {
                    for (var _j = __values(_this.fragmentParentMap.entries()), _k = _j.next(); !_k.done; _k = _j.next()) {
                        var _l = __read(_k.value, 2), frag = _l[0], parent = _l[1];
                        mirror.map[parent.__sn.id] = parent;
                        if (parent.__sn.type === NodeType.Element &&
                            parent.__sn.tagName === 'textarea' &&
                            frag.textContent) {
                            parent.value = frag.textContent;
                        }
                        parent.appendChild(frag);
                    }
                }
                catch (e_3_1) { e_3 = { error: e_3_1 }; }
                finally {
                    try {
                        if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
                    }
                    finally { if (e_3) throw e_3.error; }
                }
                _this.fragmentParentMap.clear();
            });
            var timer = new Timer([], (config === null || config === void 0 ? void 0 : config.speed) || defaultConfig.speed);
            this.service = createPlayerService({
                events: events.map(function (e) {
                    if (config && config.unpackFn) {
                        return config.unpackFn(e);
                    }
                    return e;
                }),
                timer: timer,
                timeOffset: 0,
                baselineTime: 0,
                lastPlayedEvent: null,
            }, {
                getCastFn: this.getCastFn,
                emitter: this.emitter,
            });
            this.service.start();
            this.service.subscribe(function (state) {
                _this.emitter.emit(exports.ReplayerEvents.StateChange, {
                    player: state,
                });
            });
            this.speedService = createSpeedService({
                normalSpeed: -1,
                timer: timer,
            });
            this.speedService.start();
            this.speedService.subscribe(function (state) {
                _this.emitter.emit(exports.ReplayerEvents.StateChange, {
                    speed: state,
                });
            });
            var firstMeta = this.service.state.context.events.find(function (e) { return e.type === exports.EventType.Meta; });
            var firstFullsnapshot = this.service.state.context.events.find(function (e) { return e.type === exports.EventType.FullSnapshot; });
            if (firstMeta) {
                var _a = firstMeta.data, width_1 = _a.width, height_1 = _a.height;
                setTimeout(function () {
                    _this.emitter.emit(exports.ReplayerEvents.Resize, {
                        width: width_1,
                        height: height_1,
                    });
                }, 0);
            }
            if (firstFullsnapshot) {
                this.rebuildFullSnapshot(firstFullsnapshot);
            }
        }
        Object.defineProperty(Replayer.prototype, "timer", {
            get: function () {
                return this.service.state.context.timer;
            },
            enumerable: false,
            configurable: true
        });
        Replayer.prototype.on = function (event, handler) {
            this.emitter.on(event, handler);
        };
        Replayer.prototype.setConfig = function (config) {
            var _this = this;
            Object.keys(config).forEach(function (key) {
                _this.config[key] = config[key];
            });
            if (!this.config.skipInactive) {
                this.backToNormal();
            }
            if (typeof config.speed !== 'undefined') {
                this.speedService.send({
                    type: 'SET_SPEED',
                    payload: {
                        speed: config.speed,
                    },
                });
            }
        };
        Replayer.prototype.getMetaData = function () {
            var firstEvent = this.service.state.context.events[0];
            var lastEvent = this.service.state.context.events[this.service.state.context.events.length - 1];
            return {
                startTime: firstEvent.timestamp,
                endTime: lastEvent.timestamp,
                totalTime: lastEvent.timestamp - firstEvent.timestamp,
            };
        };
        Replayer.prototype.getCurrentTime = function () {
            return this.timer.timeOffset + this.getTimeOffset();
        };
        Replayer.prototype.getTimeOffset = function () {
            var _a = this.service.state.context, baselineTime = _a.baselineTime, events = _a.events;
            return baselineTime - events[0].timestamp;
        };
        Replayer.prototype.play = function (timeOffset) {
            if (timeOffset === void 0) { timeOffset = 0; }
            if (this.service.state.matches('paused')) {
                this.service.send({ type: 'PLAY', payload: { timeOffset: timeOffset } });
            }
            else {
                this.service.send({ type: 'PAUSE' });
                this.service.send({ type: 'PLAY', payload: { timeOffset: timeOffset } });
            }
            this.emitter.emit(exports.ReplayerEvents.Start);
        };
        Replayer.prototype.pause = function (timeOffset) {
            if (timeOffset === undefined && this.service.state.matches('playing')) {
                this.service.send({ type: 'PAUSE' });
            }
            if (typeof timeOffset === 'number') {
                this.play(timeOffset);
                this.service.send({ type: 'PAUSE' });
            }
            this.emitter.emit(exports.ReplayerEvents.Pause);
        };
        Replayer.prototype.resume = function (timeOffset) {
            if (timeOffset === void 0) { timeOffset = 0; }
            console.warn("The 'resume' will be departed in 1.0. Please use 'play' method which has the same interface.");
            this.play(timeOffset);
            this.emitter.emit(exports.ReplayerEvents.Resume);
        };
        Replayer.prototype.startLive = function (baselineTime) {
            this.service.send({ type: 'TO_LIVE', payload: { baselineTime: baselineTime } });
        };
        Replayer.prototype.addEvent = function (rawEvent) {
            var _this = this;
            var event = this.config.unpackFn
                ? this.config.unpackFn(rawEvent)
                : rawEvent;
            Promise.resolve().then(function () {
                return _this.service.send({ type: 'ADD_EVENT', payload: { event: event } });
            });
        };
        Replayer.prototype.enableInteract = function () {
            this.iframe.setAttribute('scrolling', 'auto');
            this.iframe.style.pointerEvents = 'auto';
        };
        Replayer.prototype.disableInteract = function () {
            this.iframe.setAttribute('scrolling', 'no');
            this.iframe.style.pointerEvents = 'none';
        };
        Replayer.prototype.setupDom = function () {
            this.wrapper = document.createElement('div');
            this.wrapper.classList.add('replayer-wrapper');
            this.config.root.appendChild(this.wrapper);
            this.mouse = document.createElement('div');
            this.mouse.classList.add('replayer-mouse');
            this.wrapper.appendChild(this.mouse);
            if (this.config.mouseTail !== false) {
                this.mouseTail = document.createElement('canvas');
                this.mouseTail.classList.add('replayer-mouse-tail');
                this.mouseTail.style.display = 'none';
                this.wrapper.appendChild(this.mouseTail);
            }
            this.iframe = document.createElement('iframe');
            var attributes = ['allow-same-origin'];
            if (this.config.UNSAFE_replayCanvas) {
                attributes.push('allow-scripts');
            }
            this.iframe.style.display = 'none';
            this.iframe.setAttribute('sandbox', attributes.join(' '));
            this.disableInteract();
            this.wrapper.appendChild(this.iframe);
            if (this.iframe.contentWindow && this.iframe.contentDocument) {
                polyfill$1(this.iframe.contentWindow, this.iframe.contentDocument);
                polyfill(this.iframe.contentWindow);
            }
        };
        Replayer.prototype.handleResize = function (dimension) {
            var e_4, _a;
            try {
                for (var _b = __values([this.mouseTail, this.iframe]), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var el = _c.value;
                    if (!el) {
                        continue;
                    }
                    el.style.display = 'inherit';
                    el.setAttribute('width', String(dimension.width));
                    el.setAttribute('height', String(dimension.height));
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_4) throw e_4.error; }
            }
        };
        Replayer.prototype.getCastFn = function (event, isSync) {
            var _this = this;
            if (isSync === void 0) { isSync = false; }
            var castFn;
            switch (event.type) {
                case exports.EventType.DomContentLoaded:
                case exports.EventType.Load:
                    break;
                case exports.EventType.Custom:
                    castFn = function () {
                        _this.emitter.emit(exports.ReplayerEvents.CustomEvent, event);
                    };
                    break;
                case exports.EventType.Meta:
                    castFn = function () {
                        return _this.emitter.emit(exports.ReplayerEvents.Resize, {
                            width: event.data.width,
                            height: event.data.height,
                        });
                    };
                    break;
                case exports.EventType.FullSnapshot:
                    castFn = function () {
                        _this.rebuildFullSnapshot(event, isSync);
                        _this.iframe.contentWindow.scrollTo(event.data.initialOffset);
                    };
                    break;
                case exports.EventType.IncrementalSnapshot:
                    castFn = function () {
                        var e_5, _a;
                        _this.applyIncremental(event, isSync);
                        if (isSync) {
                            return;
                        }
                        if (event === _this.nextUserInteractionEvent) {
                            _this.nextUserInteractionEvent = null;
                            _this.backToNormal();
                        }
                        if (_this.config.skipInactive && !_this.nextUserInteractionEvent) {
                            try {
                                for (var _b = __values(_this.service.state.context.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                                    var _event = _c.value;
                                    if (_event.timestamp <= event.timestamp) {
                                        continue;
                                    }
                                    if (_this.isUserInteraction(_event)) {
                                        if (_event.delay - event.delay >
                                            SKIP_TIME_THRESHOLD *
                                            _this.speedService.state.context.timer.speed) {
                                            _this.nextUserInteractionEvent = _event;
                                        }
                                        break;
                                    }
                                }
                            }
                            catch (e_5_1) { e_5 = { error: e_5_1 }; }
                            finally {
                                try {
                                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                                }
                                finally { if (e_5) throw e_5.error; }
                            }
                            if (_this.nextUserInteractionEvent) {
                                var skipTime = _this.nextUserInteractionEvent.delay - event.delay;
                                var payload = {
                                    speed: Math.min(Math.round(skipTime / SKIP_TIME_INTERVAL), 360),
                                };
                                _this.speedService.send({ type: 'FAST_FORWARD', payload: payload });
                                _this.emitter.emit(exports.ReplayerEvents.SkipStart, payload);
                            }
                        }
                    };
                    break;
            }
            var wrappedCastFn = function () {
                if (castFn) {
                    castFn();
                }
                _this.service.send({ type: 'CAST_EVENT', payload: { event: event } });
                if (event ===
                    _this.service.state.context.events[_this.service.state.context.events.length - 1]) {
                    var finish_1 = function () {
                        _this.backToNormal();
                        _this.service.send('END');
                        _this.emitter.emit(exports.ReplayerEvents.Finish);
                    };
                    if (event.type === exports.EventType.IncrementalSnapshot &&
                        event.data.source === exports.IncrementalSource.MouseMove &&
                        event.data.positions.length) {
                        setTimeout(function () {
                            finish_1();
                        }, Math.max(0, -event.data.positions[0].timeOffset));
                    }
                    else {
                        finish_1();
                    }
                }
            };
            return wrappedCastFn;
        };
        Replayer.prototype.rebuildFullSnapshot = function (event, isSync) {
            if (isSync === void 0) { isSync = false; }
            if (!this.iframe.contentDocument) {
                return console.warn('Looks like your replayer has been destroyed.');
            }
            if (Object.keys(this.legacy_missingNodeRetryMap).length) {
                console.warn('Found unresolved missing node map', this.legacy_missingNodeRetryMap);
            }
            this.legacy_missingNodeRetryMap = {};
            mirror.map = rebuild(event.data.node, this.iframe.contentDocument)[1];
            var styleEl = document.createElement('style');
            var _a = this.iframe.contentDocument, documentElement = _a.documentElement, head = _a.head;
            documentElement.insertBefore(styleEl, head);
            var injectStylesRules = rules(this.config.blockClass).concat(this.config.insertStyleRules);
            for (var idx = 0; idx < injectStylesRules.length; idx++) {
                styleEl.sheet.insertRule(injectStylesRules[idx], idx);
            }
            this.emitter.emit(exports.ReplayerEvents.FullsnapshotRebuilded, event);
            if (!isSync) {
                this.waitForStylesheetLoad();
            }
            if (this.config.UNSAFE_replayCanvas) {
                this.preloadAllImages();
            }
        };
        Replayer.prototype.waitForStylesheetLoad = function () {
            var _this = this;
            var _a;
            var head = (_a = this.iframe.contentDocument) === null || _a === void 0 ? void 0 : _a.head;
            if (head) {
                var unloadSheets_1 = new Set();
                var timer_1;
                var beforeLoadState_1 = this.service.state;
                var stateHandler_1 = function () {
                    beforeLoadState_1 = _this.service.state;
                };
                this.emitter.on(exports.ReplayerEvents.Start, stateHandler_1);
                this.emitter.on(exports.ReplayerEvents.Pause, stateHandler_1);
                var unsubscribe_1 = function () {
                    _this.emitter.off(exports.ReplayerEvents.Start, stateHandler_1);
                    _this.emitter.off(exports.ReplayerEvents.Pause, stateHandler_1);
                };
                head
                    .querySelectorAll('link[rel="stylesheet"]')
                    .forEach(function (css) {
                        if (!css.sheet) {
                            unloadSheets_1.add(css);
                            css.addEventListener('load', function () {
                                unloadSheets_1.delete(css);
                                if (unloadSheets_1.size === 0 && timer_1 !== -1) {
                                    if (beforeLoadState_1.matches('playing')) {
                                        _this.play(_this.getCurrentTime());
                                    }
                                    _this.emitter.emit(exports.ReplayerEvents.LoadStylesheetEnd);
                                    if (timer_1) {
                                        window.clearTimeout(timer_1);
                                    }
                                    unsubscribe_1();
                                }
                            });
                        }
                    });
                if (unloadSheets_1.size > 0) {
                    this.service.send({ type: 'PAUSE' });
                    this.emitter.emit(exports.ReplayerEvents.LoadStylesheetStart);
                    timer_1 = window.setTimeout(function () {
                        if (beforeLoadState_1.matches('playing')) {
                            _this.play(_this.getCurrentTime());
                        }
                        timer_1 = -1;
                        unsubscribe_1();
                    }, this.config.loadTimeout);
                }
            }
        };
        Replayer.prototype.preloadAllImages = function () {
            var e_6, _a;
            var _this = this;
            var beforeLoadState = this.service.state;
            var stateHandler = function () {
                beforeLoadState = _this.service.state;
            };
            this.emitter.on(exports.ReplayerEvents.Start, stateHandler);
            this.emitter.on(exports.ReplayerEvents.Pause, stateHandler);
            var unsubscribe = function () {
                _this.emitter.off(exports.ReplayerEvents.Start, stateHandler);
                _this.emitter.off(exports.ReplayerEvents.Pause, stateHandler);
            };
            var count = 0;
            var resolved = 0;
            try {
                for (var _b = __values(this.service.state.context.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var event = _c.value;
                    if (event.type === exports.EventType.IncrementalSnapshot &&
                        event.data.source === exports.IncrementalSource.CanvasMutation &&
                        event.data.property === 'drawImage' &&
                        typeof event.data.args[0] === 'string' &&
                        !this.imageMap.has(event)) {
                        count++;
                        var image = document.createElement('img');
                        image.src = event.data.args[0];
                        this.imageMap.set(event, image);
                        image.onload = function () {
                            resolved++;
                            if (resolved === count) {
                                if (beforeLoadState.matches('playing')) {
                                    _this.play(_this.getCurrentTime());
                                }
                                unsubscribe();
                            }
                        };
                    }
                }
            }
            catch (e_6_1) { e_6 = { error: e_6_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_6) throw e_6.error; }
            }
            if (count !== resolved) {
                this.service.send({ type: 'PAUSE' });
            }
        };
        Replayer.prototype.applyIncremental = function (e, isSync) {
            var _this = this;
            var _a, _b;
            var d = e.data;
            switch (d.source) {
                case exports.IncrementalSource.Mutation: {
                    if (isSync) {
                        d.adds.forEach(function (m) { return _this.treeIndex.add(m); });
                        d.texts.forEach(function (m) { return _this.treeIndex.text(m); });
                        d.attributes.forEach(function (m) { return _this.treeIndex.attribute(m); });
                        d.removes.forEach(function (m) { return _this.treeIndex.remove(m); });
                    }
                    this.applyMutation(d, isSync);
                    break;
                }
                case exports.IncrementalSource.MouseMove:
                    if (isSync) {
                        var lastPosition = d.positions[d.positions.length - 1];
                        this.moveAndHover(d, lastPosition.x, lastPosition.y, lastPosition.id);
                    }
                    else {
                        d.positions.forEach(function (p) {
                            var action = {
                                doAction: function () {
                                    _this.moveAndHover(d, p.x, p.y, p.id);
                                },
                                delay: p.timeOffset +
                                    e.timestamp -
                                    _this.service.state.context.baselineTime,
                            };
                            _this.timer.addAction(action);
                        });
                        this.timer.addAction({
                            doAction: function () { },
                            delay: e.delay - ((_a = d.positions[0]) === null || _a === void 0 ? void 0 : _a.timeOffset),
                        });
                    }
                    break;
                case exports.IncrementalSource.MouseInteraction: {
                    if (d.id === -1) {
                        break;
                    }
                    var event = new Event(exports.MouseInteractions[d.type].toLowerCase());
                    var target = mirror.getNode(d.id);
                    if (!target) {
                        return this.debugNodeNotFound(d, d.id);
                    }
                    this.emitter.emit(exports.ReplayerEvents.MouseInteraction, {
                        type: d.type,
                        target: target,
                    });
                    var triggerFocus = this.config.triggerFocus;
                    switch (d.type) {
                        case exports.MouseInteractions.Blur:
                            if ('blur' in target) {
                                target.blur();
                            }
                            break;
                        case exports.MouseInteractions.Focus:
                            if (triggerFocus && target.focus) {
                                target.focus({
                                    preventScroll: true,
                                });
                            }
                            break;
                        case exports.MouseInteractions.Click:
                        case exports.MouseInteractions.TouchStart:
                        case exports.MouseInteractions.TouchEnd:
                            if (!isSync) {
                                this.moveAndHover(d, d.x, d.y, d.id);
                                this.mouse.classList.remove('active');
                                void this.mouse.offsetWidth;
                                this.mouse.classList.add('active');
                            }
                            break;
                        default:
                            target.dispatchEvent(event);
                    }
                    break;
                }
                case exports.IncrementalSource.Scroll: {
                    if (d.id === -1) {
                        break;
                    }
                    if (isSync) {
                        this.treeIndex.scroll(d);
                        break;
                    }
                    this.applyScroll(d);
                    break;
                }
                case exports.IncrementalSource.ViewportResize:
                    this.emitter.emit(exports.ReplayerEvents.Resize, {
                        width: d.width,
                        height: d.height,
                    });
                    break;
                case exports.IncrementalSource.Input: {
                    if (d.id === -1) {
                        break;
                    }
                    if (isSync) {
                        this.treeIndex.input(d);
                        break;
                    }
                    this.applyInput(d);
                    break;
                }
                case exports.IncrementalSource.MediaInteraction: {
                    var target = mirror.getNode(d.id);
                    if (!target) {
                        return this.debugNodeNotFound(d, d.id);
                    }
                    var mediaEl_1 = target;
                    try {
                        if (d.type === MediaInteractions.Pause) {
                            mediaEl_1.pause();
                        }
                        if (d.type === MediaInteractions.Play) {
                            if (mediaEl_1.readyState >= HTMLMediaElement.HAVE_CURRENT_DATA) {
                                mediaEl_1.play();
                            }
                            else {
                                mediaEl_1.addEventListener('canplay', function () {
                                    mediaEl_1.play();
                                });
                            }
                        }
                    }
                    catch (error) {
                        if (this.config.showWarning) {
                            console.warn("Failed to replay media interactions: " + (error.message || error));
                        }
                    }
                    break;
                }
                case exports.IncrementalSource.StyleSheetRule: {
                    var target = mirror.getNode(d.id);
                    if (!target) {
                        return this.debugNodeNotFound(d, d.id);
                    }
                    var styleEl = target;
                    var parent = target.parentNode;
                    var usingVirtualParent = this.fragmentParentMap.has(parent);
                    var placeholderNode = void 0;
                    if (usingVirtualParent) {
                        var domParent = this.fragmentParentMap.get(target.parentNode);
                        placeholderNode = document.createTextNode('');
                        parent.replaceChild(placeholderNode, target);
                        domParent.appendChild(target);
                    }
                    var styleSheet_1 = styleEl.sheet;
                    if (d.adds) {
                        d.adds.forEach(function (_a) {
                            var rule = _a.rule, index = _a.index;
                            var _index = index === undefined
                                ? undefined
                                : Math.min(index, styleSheet_1.rules.length);
                            try {
                                styleSheet_1.insertRule(rule, _index);
                            }
                            catch (e) {
                            }
                        });
                    }
                    if (d.removes) {
                        d.removes.forEach(function (_a) {
                            var index = _a.index;
                            try {
                                styleSheet_1.deleteRule(index);
                            }
                            catch (e) {
                            }
                        });
                    }
                    if (usingVirtualParent && placeholderNode) {
                        parent.replaceChild(target, placeholderNode);
                    }
                    break;
                }
                case exports.IncrementalSource.CanvasMutation: {
                    if (!this.config.UNSAFE_replayCanvas) {
                        return;
                    }
                    var target = mirror.getNode(d.id);
                    if (!target) {
                        return this.debugNodeNotFound(d, d.id);
                    }
                    try {
                        var ctx = target.getContext('2d');
                        if (d.setter) {
                            ctx[d.property] = d.args[0];
                            return;
                        }
                        var original = ctx[d.property];
                        if (d.property === 'drawImage' && typeof d.args[0] === 'string') {
                            var image = this.imageMap.get(e);
                            d.args[0] = image;
                            original.apply(ctx, d.args);
                        }
                        else {
                            original.apply(ctx, d.args);
                        }
                    }
                    catch (error) {
                        this.warnCanvasMutationFailed(d, d.id, error);
                    }
                    break;
                }
                case exports.IncrementalSource.Font: {
                    try {
                        var fontFace = new FontFace(d.family, d.buffer ? new Uint8Array(JSON.parse(d.fontSource)) : d.fontSource, d.descriptors);
                        (_b = this.iframe.contentDocument) === null || _b === void 0 ? void 0 : _b.fonts.add(fontFace);
                    }
                    catch (error) {
                        if (this.config.showWarning) {
                            console.warn(error);
                        }
                    }
                    break;
                }
            }
        };
        Replayer.prototype.applyMutation = function (d, useVirtualParent) {
            var e_7, _a;
            var _this = this;
            d.removes.forEach(function (mutation) {
                var target = mirror.getNode(mutation.id);
                if (!target) {
                    return _this.warnNodeNotFound(d, mutation.id);
                }
                var parent = mirror.getNode(mutation.parentId);
                if (!parent) {
                    return _this.warnNodeNotFound(d, mutation.parentId);
                }
                mirror.removeNodeFromMap(target);
                if (parent) {
                    var realParent = _this.fragmentParentMap.get(parent);
                    if (realParent && realParent.contains(target)) {
                        realParent.removeChild(target);
                    }
                    else {
                        parent.removeChild(target);
                    }
                }
            });
            var legacy_missingNodeMap = __assign({}, this.legacy_missingNodeRetryMap);
            var queue = [];
            function nextNotInDOM(mutation) {
                var next = null;
                if (mutation.nextId) {
                    next = mirror.getNode(mutation.nextId);
                }
                if (mutation.nextId !== null &&
                    mutation.nextId !== undefined &&
                    mutation.nextId !== -1 &&
                    !next) {
                    return true;
                }
                return false;
            }
            var appendNode = function (mutation) {
                if (!_this.iframe.contentDocument) {
                    return console.warn('Looks like your replayer has been destroyed.');
                }
                var parent = mirror.getNode(mutation.parentId);
                if (!parent) {
                    return queue.push(mutation);
                }
                var parentInDocument = null;
                if (_this.iframe.contentDocument.contains) {
                    parentInDocument = _this.iframe.contentDocument.contains(parent);
                }
                else if (_this.iframe.contentDocument.body.contains) {
                    parentInDocument = _this.iframe.contentDocument.body.contains(parent);
                }
                if (useVirtualParent && parentInDocument) {
                    var virtualParent = document.createDocumentFragment();
                    mirror.map[mutation.parentId] = virtualParent;
                    _this.fragmentParentMap.set(virtualParent, parent);
                    while (parent.firstChild) {
                        virtualParent.appendChild(parent.firstChild);
                    }
                    parent = virtualParent;
                }
                var previous = null;
                var next = null;
                if (mutation.previousId) {
                    previous = mirror.getNode(mutation.previousId);
                }
                if (mutation.nextId) {
                    next = mirror.getNode(mutation.nextId);
                }
                if (nextNotInDOM(mutation)) {
                    return queue.push(mutation);
                }
                var target = buildNodeWithSN(mutation.node, _this.iframe.contentDocument, mirror.map, true);
                if (mutation.previousId === -1 || mutation.nextId === -1) {
                    legacy_missingNodeMap[mutation.node.id] = {
                        node: target,
                        mutation: mutation,
                    };
                    return;
                }
                if (previous && previous.nextSibling && previous.nextSibling.parentNode) {
                    parent.insertBefore(target, previous.nextSibling);
                }
                else if (next && next.parentNode) {
                    parent.contains(next)
                        ? parent.insertBefore(target, next)
                        : parent.insertBefore(target, null);
                }
                else {
                    parent.appendChild(target);
                }
                if (mutation.previousId || mutation.nextId) {
                    _this.legacy_resolveMissingNode(legacy_missingNodeMap, parent, target, mutation);
                }
            };
            d.adds.forEach(function (mutation) {
                appendNode(mutation);
            });
            var startTime = Date.now();
            while (queue.length) {
                var resolveTrees = queueToResolveTrees(queue);
                queue.length = 0;
                if (Date.now() - startTime > 500) {
                    this.warn('Timeout in the loop, please check the resolve tree data:', resolveTrees);
                    break;
                }
                try {
                    for (var resolveTrees_1 = (e_7 = void 0, __values(resolveTrees)), resolveTrees_1_1 = resolveTrees_1.next(); !resolveTrees_1_1.done; resolveTrees_1_1 = resolveTrees_1.next()) {
                        var tree = resolveTrees_1_1.value;
                        var parent = mirror.getNode(tree.value.parentId);
                        if (!parent) {
                            this.debug('Drop resolve tree since there is no parent for the root node.', tree);
                        }
                        else {
                            iterateResolveTree(tree, function (mutation) {
                                appendNode(mutation);
                            });
                        }
                    }
                }
                catch (e_7_1) { e_7 = { error: e_7_1 }; }
                finally {
                    try {
                        if (resolveTrees_1_1 && !resolveTrees_1_1.done && (_a = resolveTrees_1.return)) _a.call(resolveTrees_1);
                    }
                    finally { if (e_7) throw e_7.error; }
                }
            }
            if (Object.keys(legacy_missingNodeMap).length) {
                Object.assign(this.legacy_missingNodeRetryMap, legacy_missingNodeMap);
            }
            d.texts.forEach(function (mutation) {
                var target = mirror.getNode(mutation.id);
                if (!target) {
                    return _this.warnNodeNotFound(d, mutation.id);
                }
                if (_this.fragmentParentMap.has(target)) {
                    target = _this.fragmentParentMap.get(target);
                }
                target.textContent = mutation.value;
            });
            d.attributes.forEach(function (mutation) {
                var target = mirror.getNode(mutation.id);
                if (!target) {
                    return _this.warnNodeNotFound(d, mutation.id);
                }
                if (_this.fragmentParentMap.has(target)) {
                    target = _this.fragmentParentMap.get(target);
                }
                for (var attributeName in mutation.attributes) {
                    if (typeof attributeName === 'string') {
                        var value = mutation.attributes[attributeName];
                        try {
                            if (value !== null) {
                                target.setAttribute(attributeName, value);
                            }
                            else {
                                target.removeAttribute(attributeName);
                            }
                        }
                        catch (error) {
                            if (_this.config.showWarning) {
                                console.warn('An error occurred may due to the checkout feature.', error);
                            }
                        }
                    }
                }
            });
        };
        Replayer.prototype.applyScroll = function (d) {
            var target = mirror.getNode(d.id);
            if (!target) {
                return this.debugNodeNotFound(d, d.id);
            }
            if (target === this.iframe.contentDocument) {
                this.iframe.contentWindow.scrollTo({
                    top: d.y,
                    left: d.x,
                    behavior: 'smooth',
                });
            }
            else {
                try {
                    target.scrollTop = d.y;
                    target.scrollLeft = d.x;
                }
                catch (error) {
                }
            }
        };
        Replayer.prototype.applyInput = function (d) {
            var target = mirror.getNode(d.id);
            if (!target) {
                return this.debugNodeNotFound(d, d.id);
            }
            try {
                target.checked = d.isChecked;
                target.value = d.text;
            }
            catch (error) {
            }
        };
        Replayer.prototype.legacy_resolveMissingNode = function (map, parent, target, targetMutation) {
            var previousId = targetMutation.previousId, nextId = targetMutation.nextId;
            var previousInMap = previousId && map[previousId];
            var nextInMap = nextId && map[nextId];
            if (previousInMap) {
                var _a = previousInMap, node = _a.node, mutation = _a.mutation;
                parent.insertBefore(node, target);
                delete map[mutation.node.id];
                delete this.legacy_missingNodeRetryMap[mutation.node.id];
                if (mutation.previousId || mutation.nextId) {
                    this.legacy_resolveMissingNode(map, parent, node, mutation);
                }
            }
            if (nextInMap) {
                var _b = nextInMap, node = _b.node, mutation = _b.mutation;
                parent.insertBefore(node, target.nextSibling);
                delete map[mutation.node.id];
                delete this.legacy_missingNodeRetryMap[mutation.node.id];
                if (mutation.previousId || mutation.nextId) {
                    this.legacy_resolveMissingNode(map, parent, node, mutation);
                }
            }
        };
        Replayer.prototype.moveAndHover = function (d, x, y, id) {
            this.mouse.style.left = x + "px";
            this.mouse.style.top = y + "px";
            this.drawMouseTail({ x: x, y: y });
            var target = mirror.getNode(id);
            if (!target) {
                return this.debugNodeNotFound(d, id);
            }
            this.hoverElements(target);
        };
        Replayer.prototype.drawMouseTail = function (position) {
            var _this = this;
            if (!this.mouseTail) {
                return;
            }
            var _a = this.config.mouseTail === true
                ? defaultMouseTailConfig
                : Object.assign({}, defaultMouseTailConfig, this.config.mouseTail), lineCap = _a.lineCap, lineWidth = _a.lineWidth, strokeStyle = _a.strokeStyle, duration = _a.duration;
            var draw = function () {
                if (!_this.mouseTail) {
                    return;
                }
                var ctx = _this.mouseTail.getContext('2d');
                if (!ctx || !_this.tailPositions.length) {
                    return;
                }
                ctx.clearRect(0, 0, _this.mouseTail.width, _this.mouseTail.height);
                ctx.beginPath();
                ctx.lineWidth = lineWidth;
                ctx.lineCap = lineCap;
                ctx.strokeStyle = strokeStyle;
                ctx.moveTo(_this.tailPositions[0].x, _this.tailPositions[0].y);
                _this.tailPositions.forEach(function (p) { return ctx.lineTo(p.x, p.y); });
                ctx.stroke();
            };
            this.tailPositions.push(position);
            draw();
            setTimeout(function () {
                _this.tailPositions = _this.tailPositions.filter(function (p) { return p !== position; });
                draw();
            }, duration);
        };
        Replayer.prototype.hoverElements = function (el) {
            var _a;
            (_a = this.iframe.contentDocument) === null || _a === void 0 ? void 0 : _a.querySelectorAll('.\\:hover').forEach(function (hoveredEl) {
                hoveredEl.classList.remove(':hover');
            });
            var currentEl = el;
            while (currentEl) {
                if (currentEl.classList) {
                    currentEl.classList.add(':hover');
                }
                currentEl = currentEl.parentElement;
            }
        };
        Replayer.prototype.isUserInteraction = function (event) {
            if (event.type !== exports.EventType.IncrementalSnapshot) {
                return false;
            }
            return (event.data.source > exports.IncrementalSource.Mutation &&
                event.data.source <= exports.IncrementalSource.Input);
        };
        Replayer.prototype.backToNormal = function () {
            this.nextUserInteractionEvent = null;
            if (this.speedService.state.matches('normal')) {
                return;
            }
            this.speedService.send({ type: 'BACK_TO_NORMAL' });
            this.emitter.emit(exports.ReplayerEvents.SkipEnd, {
                speed: this.speedService.state.context.normalSpeed,
            });
        };
        Replayer.prototype.warnNodeNotFound = function (d, id) {
            this.warn("Node with id '" + id + "' not found in", d);
        };
        Replayer.prototype.warnCanvasMutationFailed = function (d, id, error) {
            this.warn("Has error on update canvas '" + id + "'", d, error);
        };
        Replayer.prototype.debugNodeNotFound = function (d, id) {
            this.debug(REPLAY_CONSOLE_PREFIX, "Node with id '" + id + "' not found in", d);
        };
        Replayer.prototype.warn = function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            if (!this.config.showWarning) {
                return;
            }
            console.warn.apply(console, __spread([REPLAY_CONSOLE_PREFIX], args));
        };
        Replayer.prototype.debug = function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            if (!this.config.showDebug) {
                return;
            }
            console.log.apply(console, __spread([REPLAY_CONSOLE_PREFIX], args));
        };
        return Replayer;
    }());

    var addCustomEvent = record.addCustomEvent;
    var freezePage = record.freezePage;

    exports.Replayer = Replayer;
    exports.addCustomEvent = addCustomEvent;
    exports.freezePage = freezePage;
    exports.mirror = mirror;
    exports.record = record;
    exports.utils = utils;

    Object.defineProperty(exports, '__esModule', { value: true });

    return exports;

}({}));



// json删除多余字符压缩
function formatData(str) {
    var res = JSON.stringify(str);
    //去掉空格
    //res = res.replace(/\ +/g, "");
    res = res.replace(/\\n/g, "");
    res = res.replace(/\s+/g, ' ');
    res = res.replace(/ }/g, "}");
    res = res.replace(/: /g, ":");
    res = res.replace(/, /g, ",");
    res = res.replace(/{ /g, '{');
    //console.log(res);
    return res;
}

function ajax(options) {
    //调用函数时如果options没有指定，就给它赋值{},一个空的Object
    options = options || {};
    // 请求格式GET、POST，默认为GET
    options.type = (options.type || "GET").toUpperCase();
    //响应数据格式，默认json
    options.dataType = options.dataType || "json";
    //var params = encodeURIComponent(options.data);
    var params = options.data;
    var xhr;
    //考虑兼容性
    if (window.XMLHttpRequest) {
        xhr = new XMLHttpRequest();
    } else if (window.ActiveObject) { //兼容IE6以下版本
        xhr = new ActiveXobject('Microsoft.XMLHTTP');
    }
    //启动并发送一个请求
    if (options.type == "GET") {
        xhr.open("GET", options.url + "?" + params, true);
        xhr.send(null);
    } else if (options.type == "POST") {
        xhr.open("post", options.url, true);
        //设置表单提交时的内容类型
        //Content-type数据请求的格式
        xhr.setRequestHeader("Content-type", "application/octet-stream;charset=UTF-8");
        xhr.setRequestHeader("plugin-version", ''),
        xhr.send(params);
    }
    //    设置有效时间
    setTimeout(function () {
        if (xhr.readySate != 4) {
            xhr.abort();
        }
    }, options.timeout)

    //  接收
    //  options.success成功之后的回调函数  options.error失败后的回调函数
    //  xhr.responseText,xhr.responseXML  获得字符串形式的响应数据或者XML形式的响应数据
    xhr.onreadystatechange = function () {
        if (xhr.readyState == 4) {
            var status = xhr.status;
            if (status >= 200 && status < 300 || status == 304) {
                options.success && options.success(xhr.responseText, xhr.responseXML);
            } else {
                options.error && options.error(status);
            }
        }
    }
}

//var EcbackSDK = function (config) { 
//    this.baseUrl = config.baseUrl;
//    this.config = config || {};
//}

//EcbackSDK.prototype.init = function (param, actionStart) {
//    //调用init
//    this.send(
//        {
//            nodeCode: param.nodeCode,
//            businessId: param.businessId,
//            event: "task_init",
//        },
//        function (flag) {
//            flag ? actionStart && actionStart(param) : console.log("SDK初始失败");
//        }
//    );
//}


(function ecbackSDK($) {
    var index = 0,
        c = 0,
        v = '1.0.1',
        config = window.ecConfig || undefined,
        ecEvents = [],
        endflag = false,
        ecEventslimit = [],
        fial_report_events = [],
        maxSendLength = 8,
        maxdatalength = 6,
        eventsLength = 0,
        productCode = "",
        businessId = "",
        taskEndFlag = "0",
        taskCode = "",
        startThis = this,
        nodeId = "";

    // 初始化SDK
    ecbackSDK.prototype.init = function (param, actionStart) {
        //调用init
        this.send(
            {
                nodeCode: param.nodeCode,
                businessId: param.businessId,
                event: "task_init",
            },
            function (flag) {
                flag ? actionStart && actionStart(param) : console.log("SDK初始失败");
            }
        );
    }
    ecbackSDK.prototype.updateEvents = function (back) {
        this.send({
                event: "node_record",
                nodeId: nodeId,
                data: ecEventslimit
            },
            function () {
                back && back();
            }
        );
        ecEventslimit = []
    }

    var ins = 0;
    // 录屏开始
    ecbackSDK.prototype.start = function (back) {
        var _this = this;
        var method = back;
        index = 0;
        endflag = false;
        ecEvents = [];
        ecEventslimit = [];
        maxSendLength = 2;
        startThis.endEcback = rrweb.record(
            {
                emit: function (event, isCheckout) {
                    index = index + 1;
                    // 任务总条数
                    // isCheckout 是一个标识，告诉你重新制作了快照
                    //if (isCheckout) {
                    //    console.log('isCheckout是一个标识，告诉你重新制作了快照', event)
                    //}
                    //console.log('isCheckout', isCheckout);
                    event.pageIndex = index;
                    ecEvents.push(event);
                    ecEventslimit.push(event);
                    //console.log("推送", ecEventslimit);
                    if (endflag) {
                        startThis.endEcback();
                        return;
                    }

                    if (ecEventslimit.length >= maxSendLength) {
                        //if (maxSendLength == 2 && index > maxSendLength) return;
                        //if (c >= eventsLength) return;
                        if (JSON.stringify(ecEventslimit).length < 5000) {
                            return
                        }

                        eventsLength = ecEvents.length;
                        c = eventsLength;
                        //console.log("推送", JSON.stringify(ecEventslimit).length);
                        //console.log("推送开始");
                        _this.send(
                            {
                                event: "node_record",
                                nodeId: nodeId,
                                data: ecEventslimit
                            },
                            function () {
                                method && method();
                            }
                        );

                        ecEventslimit = [];
                    } else {
                        c = eventsLength;
                    }
                },
                packFn: rrweb.pack,
                checkoutEveryNms: 1500, // 每5分钟重新制作快照
                sampling: {
                    // 不录制鼠标移动事件
                    mousemove: false,
                    // 不录制鼠标交互事件
                    mouseInteraction: false,
                    // 设置滚动事件的触发频率
                    scroll: 600, // 每 150ms 最多触发一次
                    // 设置输入事件的录制时机
                    input: 'last' // 连续输入时，只录制最终值
                },
                blockClass: 'ec-block',
                //recordCanvas: true,
            }
        );
    }
    ecbackSDK.prototype.pageinit = function (param, callBack) {
        var ecthis_ = this;
        //录制节点开始调用
        nodeId = ecthis_.produceId();

        ecthis_.send(
            {
                event: "node_start",
                nodeId: nodeId,
                nodeCode: param.nodeCode,
            },
            function (flag) {
                if (flag) {
                    // console.log("当前节点录制开始");
                    // 增加update
                    if (param.businessId) ecthis_.update({ businessId: param.businessId });
                    ecthis_.start(callBack);
                }
            }
        );
    }

    //节点结束录制时调用
    ecbackSDK.prototype.pageend = function (end, back) {
        var _this = this;
        var endData = ecEvents.slice(c, index);
        if (endflag) {
            startThis.endEcback();
            return;
        }
        endflag = true; ecEvents = []; ecEventslimit = [];
        if (Number(end.taskEndFlag) === 1) {
            this.send(
                {
                    event: "node_end",
                    nodeId: nodeId,
                    recordCount: end.recordCount,
                    nodeCode: end.nodeCode,
                    taskEndFlag: 0,
                    data: endData || []
                },
                function (flag) {
                    if (flag) {
                        console.log("当前节点已结束");
                        _this.send(
                            {
                                event: "node_end",
                                nodeId: nodeId,
                                recordCount: end.recordCount,
                                nodeCode: end.nodeCode,
                                taskEndFlag: 1,
                                data: []
                            },
                            function (flag) {
                                if (flag) {
                                    //if(String(taskEndFlag) === "1"){
                                    //    sessionStorage.removeItem('taskId1');
                                    //    sessionStorage.removeItem('taskId2');
                                    //}
                                    ecEvents = [];
                                    console.log("当前流程已结束");
                                    back && back()
                                }
                            }
                        );
                    }
                }
            );
        } else {
            this.send(
                {
                    event: "node_end",
                    nodeId: nodeId,
                    recordCount: end.recordCount,
                    nodeCode: end.nodeCode,
                    taskEndFlag: 0,
                    data: endData || []
                },
                function (flag) {
                    if (flag) {
                        //if(String(taskEndFlag) === "1"){
                        //    sessionStorage.removeItem('taskId1');
                        //    sessionStorage.removeItem('taskId2');
                        //}
                        ecEvents = [];
                        console.log("当前节点已结束");
                        back && back()
                    }
                }
            );
        }
    }

    // 节点开始结束调用
    ecbackSDK.prototype.action = function (param) {
        config = window.ecConfig || undefined;
        var _this = this;
        var startFlag = Number(param.event) === 0;
        businessId = param.businessId;
        productCode = param.productCode;
        taskEndFlag = param.taskEndFlag || '0';

        if (!config) {
            console.error('缺少配置文件');
            return
        }

        if (param.init) this.clearStorage();

        if (this.taskCode(param.productCode, param.businessId) && startFlag) this.init(
            param,
            function (_param) {
                _this.pageinit(_param, _param.actionBack)
            }
        ); else if (startFlag) this.pageinit(param, param.actionBack); else this.pageend(param, param.actionBack);
    }

    // task_update，任务消息更新，根据taskid更新其他值 --- 更新业务数据时调用
    ecbackSDK.prototype.update = function (param) {
        this.send(
            {
                businessData: param,
                event: "task_update",
            },
            function (flag) {
                flag ? console.log("update更新数据成功") : console.log("update更新数据失败");
            }
        );
    }

    // 向后台发送数据
    ecbackSDK.prototype.send = function (events, eventsBack) {
        var sendData = events;
        //生成时间戳
        sendData.timestamp = new Date().getTime();
        // 请求ID
        sendData.eventId = this.produceId();
        // 业务号
        sendData.businessId = businessId;
        //节点产生的记录总数，每次进入节点重新计数
        sendData.recordCount = c;
        // 生成唯一任务号
        sendData.taskId = taskCode;
        // 任务结束标记 0-未结束 1- 结束
        sendData.taskEndFlag = taskEndFlag;
        // 可回溯 数据
        sendData.data = events.data;

        let binaryArray = pako.gzip(JSON.stringify(events));
        console.log('>>>>>>>',binaryArray)

        if (fial_report_events.length > 0) {
            sendData.data = fial_report_events.concat(events.data);
            fial_report_events = [];
        }

        if (config.debug) {
            //maxSendLength = maxdatalength;
            console.log("推送", sendData);
            // eventsBack(true);
            // return
        }
        ajax({
            url: config.apiUrl,
            type: 'POST',
            data: binaryArray,
            dataType: 'json',
            timeout: 30000,
            contentType: "application/octet-stream;charset=UTF-8",
            success: function (d) {
                this.fial_report_events = [];
                var result = JSON.parse(d);
                //关联原有taskid
                if (result.taskId != taskCode && result.taskId != null) {
                    taskCode = result.taskId;
                    sessionStorage.setItem('taskId1', taskCode + '-' + productCode);
                    sessionStorage.setItem('taskId2', taskCode + '-' + businessId);
                }
                switch (events.event) {
                    case "task_init":
                        c = 0;
                        //console.log("SDK初始化完成,当前使用的版本为" + v);
                        break;
                    case "node_start":
                        c = 0;
                        //console.log("可回溯录制开始");
                        break;
                    case "node_record":
                        ins = ins + 1;
                        maxSendLength = maxdatalength;
                        //console.log("事件上报成功");
                        break;
                    default:
                        break;
                }

                eventsBack && eventsBack(true);
            },
            //异常处理
            error: function () {
                maxSendLength = maxdatalength;
                fial_report_events = fial_report_events.concat(sendData.data);
                console.log("上报失败,丢失数据", fial_report_events);
                if (eventsBack != undefined) eventsBack(false);
            }
        })
    }

    ecbackSDK.prototype.clearStorage = function () {
        var storages = window.sessionStorage;
        storages.removeItem('taskId1');
        storages.removeItem('taskId2');
    }

    //生成唯一taskID
    ecbackSDK.prototype.taskCode = function (productCode, businessId, clear) {
        // newCode 是否生成新值
        var storage = window.sessionStorage;
        // 为了解决有的公司产品编号和订单号都不贯串整个流程的问题；taskId1为taskCODE+产品编号；taskId2位taskcode+订单号
        var getTask1 = storage.getItem('taskId1') || "";
        var getTask2 = storage.getItem('taskId2') || "";
        var newCode = false;
        if (clear) {
            storage.removeItem('taskId1');
            storage.removeItem('taskId2');
        }
        if (getTask1 || getTask2) {
            if (getTask1.split('-')[1] == productCode) {
                taskCode = getTask1.split('-')[0];
            } else {
                taskCode = this.produceId();
                newCode = true;
            }
        }

        if (getTask1 == "" && getTask2 == "") {
            taskCode = this.produceId();
            newCode = true;
        }
        storage.setItem('taskId1', taskCode + '-' + productCode);
        storage.setItem('taskId2', taskCode + '-' + businessId);
        return newCode;
    }

    //生成唯一ID
    ecbackSDK.prototype.produceId = function () {
        var originStr = 'xxxx-xxxx-xxxx-xxxx',
            originChar = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            len = originChar.length;
        return originStr.replace(/x/g, function (match) {
            return originChar.charAt(Math.floor(Math.random() * len))
        }).replace(/-/g, '') +new Date().getTime();
    }

    $.ecbacksdk = ecbackSDK.prototype;

}(window));
