[TOC]

## 1. 使用指南

### 1.1 SDK 介绍

本 SDK 是提供给互联网保险可回溯数据收集的 H5 工具，在需要可回溯的页面中引入该 SDK 可完成数据收集功能。

### 1.2 SDK 接入

> 方案一:
> 通过 SDK 文件上传到 CDN, 在页面中增加 CDN 地址：

```html
<script src="ecback-sdk-h5.min.js"></script>
```

> 方案二:
> 在 URL 中指定具体的版本号：

```html
<script src=""></script>
```

==由于使用 `MutationObserver` ecback 不支持 IE11 以下的浏览器。==

## 2. 快速开始

#### 2.1 初始化配置说明

ecApiUrl 是提供给 SDK 使用的一个全局变量，使用 SDK 时请先配置正确的 ecApiUrl：

```
window.ecApiUrl = 'http://*************:30011'
```

-   配置说明

| 字段名 | 字段说明            |
| ------ | ------------------- |
| ecApiUrl： | 可回溯数据存储地址 |

#### 2.2 录制开始与页面节点捕获

需要在每个节点初始化以及结束时调用该方法（补充：一个节点相当于一个页面）

节点初始化调用：实例（在初始化页面完成后调用）

```
window.onload = function(){
    ecbacksdk.action({
        businessId: "EJCJIEGG001",
        productCode: "EJCJIEGG9999", //产品ID
        nodeCode: "0001", //节点编码
        event: 0, //Number类型  0表示开始  1表示结束
        actionBack:function(){
            console.log("节点录制开始")
        }
    });
}
```

节点结束时调用：实例

```
ecbacksdk.action({
    businessId: "EJCJIEGG001",
    productCode: "EJCJIEGG9999", //产品ID
    nodeCode: "0001", //节点编码
    event: 1, //Number类型  0表示开始  1表示结束
    actionBack:function(){
        console.log("当前节点已结束")
    }
});
```

-   字段说明

| 字段名      | 字段说明                                         |
| ----------- | ------------------------------------------------ |
| businessId  | 投保单、保单、订单等业务唯一识别号，如有需要带上 |
| productCode | 渠道 ID 产品 ID                                  |
| nodeCode    | 节点编码                                         |
| event       | 0-节点录屏开始，1-节点录屏结束                   |
| taskEndFlag | 0-默认值，1-任务录屏结束标记                     |
| actionBack  | 如需在 action 之后需要做什么操作                 |

#### 2.3 完善信息

整个录制流程结束后如需完善可回溯信息时调用

```
ecbacksdk.update({
  name:"陈某某",
  productName:"产品名称",
  appntName: "投保人姓名",
  appntIdNo: "投保人证件号"
  ...
});
```

补充：update 传递的参数字段名跟后端接收的字段名必须需一致。

## 3. 注意点

### 3.1 隐私

**页面中可能存在一些隐私相关的内容不希望被录制，ecback 为此做了以下支持：**

-   在 HTML 元素中添加类名 `.ecback-block` 将会避免该元素及其子元素被录制，回放时取而代之的是一个同等宽高的占位元素。
-   在 HTML 元素中添加类名 `.ecback-ignore` 将会避免录制该元素的输入事件。
-   `input[type="password"]` 类型的密码输入框默认不会录制输入事件。
