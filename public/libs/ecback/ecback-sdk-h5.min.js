var rrweb=function(f){function z(a){var b="function"===typeof Symbol&&Symbol.iterator,c=b&&a[b],g=0;if(c)return c.call(a);if(a&&"number"===typeof a.length)return{next:function(){a&&g>=a.length&&(a=void 0);return{value:a&&a[g++],done:!a}}};throw new TypeError(b?"Object is not iterable.":"Symbol.iterator is not defined.");}function w(a,b){var c="function"===typeof Symbol&&a[Symbol.iterator];if(!c)return a;var g=c.call(a),e,d=[],h;try{for(;(void 0===b||0<b--)&&!(e=g.next()).done;)d.push(e.value)}catch(q){h={error:q}}finally{try{e&&!e.done&&(c=g["return"])&&c.call(g)}finally{if(h)throw h.error;}}return d}function m(){for(var a=[],b=0;b<arguments.length;b++)a=a.concat(w(arguments[b]));return a}function T(a){a=a.toLowerCase().trim();return Ka.test(a)?"div":a}function V(a){try{var b=a.rules||a.cssRules;return b?Array.from(b).map(P).join(""):null}catch(c){return null}}function P(a){return"styleSheet"in a?V(a.styleSheet)||"":a.cssText}function Q(a,b){return(a||"").replace(La,function(a,g,e,d,h,q){e=e||h||q;g=g||d||"";if(!e)return a;if(!Ma.test(e)||Na.test(e))return"url("+g+e+g+")";if("/"===e[0])return a=-1<b.indexOf("//")?b.split("/").slice(0,3).join("/"):b.split("/")[0],a=a.split("?")[0],"url("+g+(a+e)+g+")";a=b.split("/");d=e.split("/");a.pop();for(e=0;e<d.length;e++)h=d[e],"."!==h&&(".."===h?a.pop():a.push(h));return"url("+g+a.join("/")+g+")"})}function R(a,b){return""===b.trim()?b:b.split(",").map(function(b){b=b.trimLeft().trimRight().split(" ");if(2===b.length){var c=L(a,b[0]);return c+" "+b[1]}return 1===b.length?(c=L(a,b[0]),""+c):""}).join(", ")}function L(a,b){if(!b||""===b.trim())return b;var c=a.createElement("a");c.href=b;return c.href}function N(a,b,c){return"src"===b||"href"===b&&c?L(a,c):"srcset"===b&&c?R(a,c):"style"===b&&c?Q(c,location.href):c}function W(a,b,c){if("string"===typeof b){if(a.classList.contains(b))return!0}else a.classList.forEach(function(a){if(b.test(a))return!0});return c?a.matches(c):!1}function fa(a,b,c,g,e,d,h){void 0===d&&(d={});switch(a.nodeType){case a.DOCUMENT_NODE:return{type:B.Document,childNodes:[]};case a.DOCUMENT_TYPE_NODE:return{type:B.DocumentType,name:a.name,publicId:a.publicId,systemId:a.systemId};case a.ELEMENT_NODE:c=W(a,c,g);g=T(a.tagName);for(var q={},r=0,k=Array.from(a.attributes);r<k.length;r++){var f=k[r],X=f.name,f=f.value;q[X]=N(b,X,f)}"link"===g&&e&&(b=Array.from(b.styleSheets).find(function(b){return b.href===a.href}),e=V(b))&&(delete q.rel,delete q.href,q._cssText=Q(e,b.href));"style"===g&&a.sheet&&!(a.innerText||a.textContent||"").trim().length&&(e=V(a.sheet))&&(q._cssText=Q(e,location.href));if("input"===g||"textarea"===g||"select"===g)f=a.value,"radio"!==q.type&&"checkbox"!==q.type&&"submit"!==q.type&&"button"!==q.type&&f?q.value=d[q.type]||d[g]?"*".repeat(f.length):f:a.checked&&(q.checked=a.checked);"option"===g&&q.value===a.parentElement.value&&(q.selected=a.selected);"canvas"===g&&h&&(q.rr_dataURL=a.toDataURL());if("audio"===g||"video"===g)q.rr_mediaState=a.paused?"paused":"played";a.scrollLeft&&(q.rr_scrollLeft=a.scrollLeft);a.scrollTop&&(q.rr_scrollTop=a.scrollTop);c&&(d=a.getBoundingClientRect(),h=d.height,q.rr_width=d.width+"px",q.rr_height=h+"px");return{type:B.Element,tagName:g,attributes:q,childNodes:[],isSVG:"svg"===a.tagName||a instanceof SVGElement||void 0,needBlock:c};case a.TEXT_NODE:return d=a.parentNode&&a.parentNode.tagName,h=a.textContent,(c="STYLE"===d?!0:void 0)&&h&&(h=Q(h,location.href)),"SCRIPT"===d&&(h="SCRIPT_PLACEHOLDER"),{type:B.Text,textContent:h||"",isStyle:c};case a.CDATA_SECTION_NODE:return{type:B.CDATA,textContent:""};case a.COMMENT_NODE:return{type:B.Comment,textContent:a.textContent||""};default:return!1}}function u(a){return void 0===a?"":a.toLowerCase()}function Y(a,b,c,g,e,d,h,q,r,f,M){void 0===d&&(d=!1);void 0===h&&(h=!0);void 0===r&&(r={});void 0===M&&(M=!0);var k=fa(a,b,g,e,h,q,f||!1);if(!k)return console.warn(a,"not serialized"),null;var t,n;"__sn"in a?n=a.__sn.id:(n=r,n=n.comment&&k.type===B.Comment||k.type===B.Element&&(n.script&&("script"===k.tagName||"link"===k.tagName&&"preload"===k.attributes.rel&&"script"===k.attributes.as)||n.headFavicon&&("link"===k.tagName&&"shortcut icon"===k.attributes.rel||"meta"===k.tagName&&(u(k.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===u(k.attributes.name)||"icon"===u(k.attributes.rel)||"apple-touch-icon"===u(k.attributes.rel)||"shortcut icon"===u(k.attributes.rel)))||"meta"===k.tagName&&(n.headMetaDescKeywords&&u(k.attributes.name).match(/^description|keywords$/)||n.headMetaSocial&&(u(k.attributes.property).match(/^(og|twitter|fb):/)||u(k.attributes.name).match(/^(og|twitter):/)||"pinterest"===u(k.attributes.name))||n.headMetaRobots&&("robots"===u(k.attributes.name)||"googlebot"===u(k.attributes.name)||"bingbot"===u(k.attributes.name))||n.headMetaHttpEquiv&&void 0!==k.attributes["http-equiv"]||n.headMetaAuthorship&&("author"===u(k.attributes.name)||"generator"===u(k.attributes.name)||"framework"===u(k.attributes.name)||"publisher"===u(k.attributes.name)||"progid"===u(k.attributes.name)||u(k.attributes.property).match(/^article:/)||u(k.attributes.property).match(/^product:/))||n.headMetaVerification&&("google-site-verification"===u(k.attributes.name)||"yandex-verification"===u(k.attributes.name)||"csrf-token"===u(k.attributes.name)||"p:domain_verify"===u(k.attributes.name)||"verify-v1"===u(k.attributes.name)||"verification"===u(k.attributes.name)||"shopify-checkout-api-token"===u(k.attributes.name))))||!M&&k.type===B.Text&&!k.isStyle&&!k.textContent.replace(/^\s+|\s+$/gm,"").length?-2:Oa++);t=n;n=Object.assign(k,{id:t});a.__sn=n;if(-2===t)return null;c[t]=a;t=!d;n.type===B.Element&&(t=t&&!n.needBlock,delete n.needBlock);if((n.type===B.Document||n.type===B.Element)&&t)for(r.headWhitespace&&k.type===B.Element&&"head"==k.tagName&&(M=!1),k=0,a=Array.from(a.childNodes);k<a.length;k++)(t=Y(a[k],b,c,g,e,d,h,q,r,f,M))&&n.childNodes.push(t);return n}function I(a,b){function c(a){var b=a.match(/\n/g);b&&(p+=b.length);b=a.lastIndexOf("\n");x=-1===b?x+a.length:a.length-b}function g(){var a={line:p,column:x};return function(b){b.position=new O(a);r(/^\s*/);return b}}function e(c){var g=Error(b.source+":"+p+":"+x+": "+c);g.reason=c;g.filename=b.source;g.line=p;g.column=x;g.source=a;if(b.silent)ga.push(g);else throw g;}function d(){return r(/^{\s*/)}function h(){return r(/^}/)}function q(){var b,c=[];r(/^\s*/);for(k(c);a.length&&"}"!==a.charAt(0)&&(b=l()||m());)!1!==b&&(c.push(b),k(c));return c}function r(b){if(b=b.exec(a)){var g=b[0];c(g);a=a.slice(g.length);return b}}function k(a){void 0===a&&(a=[]);for(var b;b=f();)!1!==b&&a.push(b),f();return a}function f(){var b=g();if("/"===a.charAt(0)&&"*"===a.charAt(1)){for(var d=2;""!==a.charAt(d)&&("*"!==a.charAt(d)||"/"!==a.charAt(d+1));)++d;d+=2;if(""===a.charAt(d-1))return e("End of comment missing");var n=a.slice(2,d-2);x+=2;c(n);a=a.slice(d);x+=2;return b({type:"comment",comment:n})}}function X(){var a=r(/^([^{]+)/);if(a)return H(a[0]).replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g,"").replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,function(a){return a.replace(/,/g,"\u200c")}).split(/\s*(?![^(]*\)),\s*/).map(function(a){return a.replace(/\u200C/g,",")})}function t(){var a=g(),b=r(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);if(b){b=H(b[0]);if(!r(/^:\s*/))return e("property missing ':'");var c=r(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/),a=a({type:"declaration",property:b.replace(va,""),value:c?H(c[0]).replace(va,""):""});r(/^[;\s]*/);return a}}function n(){var a=[];if(!d())return e("missing '{'");k(a);for(var b;b=t();)!1!==b&&(a.push(b),k(a)),t();return h()?a:e("missing '}'")}function y(){for(var a,b=[],c=g();a=r(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)b.push(a[1]),r(/^,\s*/);if(b.length)return c({type:"keyframe",values:b,declarations:n()})}function E(){var a=g(),b=r(/^@([-\w]+)?keyframes\s*/);if(b){var c=b[1],b=r(/^([-\w]+)\s*/);if(!b)return e("@keyframes missing name");b=b[1];if(!d())return e("@keyframes missing '{'");for(var n,q=k();n=y();)q.push(n),q=q.concat(k());return h()?a({type:"keyframes",name:b,vendor:c,keyframes:q}):e("@keyframes missing '}'")}}function U(a){var b=new RegExp("^@"+a+"\\s*([^;]+);");return function(){var c=g(),d=r(b);if(d){var e={type:a};e[a]=d[1].trim();return c(e)}}}function l(){if("@"===a[0]){var b;if(!(b=E())){b=g();var c=r(/^@media *([^{]+)/);if(c)if(c=H(c[1]),d()){var n=k().concat(q());b=h()?b({type:"media",media:c,rules:n}):e("@media missing '}'")}else b=e("@media missing '{'");else b=void 0}b||(b=g(),b=(c=r(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/))?b({type:"custom-media",name:H(c[1]),media:H(c[2])}):void 0);b||(b=g(),(c=r(/^@supports *([^{]+)/))?(c=H(c[1]),d()?(n=k().concat(q()),b=h()?b({type:"supports",supports:c,rules:n}):e("@supports missing '}'")):b=e("@supports missing '{'")):b=void 0);if(!(b=b||v()||u()||w()))if(b=g(),n=r(/^@([-\w]+)?document *([^{]+)/))if(c=H(n[1]),n=H(n[2]),d()){var f=k().concat(q());b=h()?b({type:"document",document:n,vendor:c,rules:f}):e("@document missing '}'")}else b=e("@document missing '{'");else b=void 0;if(!b)if(b=g(),r(/^@page */))if(c=X()||[],d()){for(n=k();f=t();)n.push(f),n=n.concat(k());b=h()?b({type:"page",selectors:c,declarations:n}):e("@page missing '}'")}else b=e("@page missing '{'");else b=void 0;b||(b=g(),r(/^@host\s*/)?d()?(c=k().concat(q()),b=h()?b({type:"host",rules:c}):e("@host missing '}'")):b=e("@host missing '{'"):b=void 0);if(!b)if(b=g(),r(/^@font-face\s*/))if(d()){for(c=k();n=t();)c.push(n),c=c.concat(k());b=h()?b({type:"font-face",declarations:c}):e("@font-face missing '}'")}else b=e("@font-face missing '{'");else b=void 0;return b}}function m(){var a=g(),b=X();if(!b)return e("selector missing");k();return a({type:"rule",selectors:b,declarations:n()})}void 0===b&&(b={});var p=1,x=1,O=function(){return function(a){this.start=a;this.end={line:p,column:x};this.source=b.source}}();O.prototype.content=a;var ga=[],v=U("import"),u=U("charset"),w=U("namespace");return J(function(){var a=q();return{type:"stylesheet",stylesheet:{source:b.source,rules:a,parsingErrors:ga}}}())}function H(a){return a?a.replace(/^\s+|\s+$/g,""):""}function J(a,b){for(var c=a&&"string"===typeof a.type,g=c?a:b,e=0,d=Object.keys(a);e<d.length;e++){var h=a[d[e]];Array.isArray(h)?h.forEach(function(a){J(a,g)}):h&&"object"===typeof h&&J(h,g)}c&&Object.defineProperty(a,"parent",{configurable:!0,writable:!0,enumerable:!1,value:b||null});return a}function l(a){var b=wa[a.tagName]?wa[a.tagName]:a.tagName;"link"===b&&a.attributes._cssText&&(b="style");return b}function v(a){var b=I(a,{silent:!0});if(!b.stylesheet)return a;b.stylesheet.rules.forEach(function(b){"selectors"in b&&(b.selectors||[]).forEach(function(b){if(xa.test(b)){var c=b.replace(xa,"$1.\\:hover");a=a.replace(b,b+", "+c)}})});return a}function x(a,b,c){switch(a.type){case B.Document:return b.implementation.createDocument(null,"",null);case B.DocumentType:return b.implementation.createDocumentType(a.name||"html",a.publicId,a.systemId);case B.Element:var g=l(a),e;e=a.isSVG?b.createElementNS("http://www.w3.org/2000/svg",g):b.createElement(g);var d=function(d){if(!a.attributes.hasOwnProperty(d))return"continue";var h=a.attributes[d],h="boolean"===typeof h||"number"===typeof h?"":h;if(d.startsWith("rr_")){if("canvas"===g&&"rr_dataURL"===d){var k=document.createElement("img");k.src=h;k.onload=function(){var a=e.getContext("2d");a&&a.drawImage(k,0,0,k.width,k.height)}}"rr_width"===d&&(e.style.width=h);"rr_height"===d&&(e.style.height=h);if("rr_mediaState"===d)switch(h){case "played":e.play();case "paused":e.pause()}}else{var q="textarea"===g&&"value"===d,f="style"===g&&"_cssText"===d;f&&c&&(h=v(h));if(q||f){d=b.createTextNode(h);h=0;for(q=Array.from(e.childNodes);h<q.length;h++)f=q[h],f.nodeType===e.TEXT_NODE&&e.removeChild(f);e.appendChild(d);return"continue"}if("iframe"===g&&"src"===d)return"continue";try{a.isSVG&&"xlink:href"===d?e.setAttributeNS("http://www.w3.org/1999/xlink",d,h):"onload"===d||"onclick"===d||"onmouse"===d.substring(0,7)?e.setAttribute("_"+d,h):e.setAttribute(d,h)}catch(t){}}},h;for(h in a.attributes)d(h);return e;case B.Text:return b.createTextNode(a.isStyle&&c?v(a.textContent):a.textContent);case B.CDATA:return b.createCDATASection(a.textContent);case B.Comment:return b.createComment(a.textContent);default:return null}}function S(a,b,c,g,e){void 0===g&&(g=!1);void 0===e&&(e=!0);var d=x(a,b,e);if(!d)return null;a.type===B.Document&&(b.close(),b.open(),d=b);d.__sn=a;c[a.id]=d;if((a.type===B.Document||a.type===B.Element)&&!g)for(g=0,a=a.childNodes;g<a.length;g++){var h=a[g],q=S(h,b,c,!1,e);q?d.appendChild(q):console.warn("Failed to rebuild",h)}return d}function Pa(a,b){for(var c in a)a[c]&&b(a[c])}function Qa(a,b,c,g){void 0===g&&(g=!0);var e={};a=S(a,b,e,!1,g);Pa(e,function(a){c&&c(a);var b=a.__sn;if(b.type===B.Element)for(var d in b.attributes)if(b.attributes.hasOwnProperty(d)&&d.startsWith("rr_")){var g=b.attributes[d];"rr_scrollLeft"===d&&(a.scrollLeft=g);"rr_scrollTop"===d&&(a.scrollTop=g)}});return[a,e]}function F(a,b,c){void 0===c&&(c=document);var g={capture:!0,passive:!0};c.addEventListener(a,b,g);return function(){return c.removeEventListener(a,b,g)}}function da(a,b,c){void 0===c&&(c={});var g=null,e=0;return function(d){var h=Date.now();e||!1!==c.leading||(e=h);var q=b-(h-e),f=this,k=arguments;0>=q||q>b?(g&&(window.clearTimeout(g),g=null),e=h,a.apply(f,k)):g||!1===c.trailing||(g=window.setTimeout(function(){e=!1===c.leading?0:Date.now();g=null;a.apply(f,k)},q))}}function ia(a,b,c,g,e){void 0===e&&(e=window);var d=e.Object.getOwnPropertyDescriptor(a,b);e.Object.defineProperty(a,b,g?c:{set:function(a){var b=this;setTimeout(function(){c.set.call(b,a)},0);d&&d.set&&d.set.call(this,a)}});return function(){return ia(a,b,d||{},!0)}}function ka(a,b,c){try{if(!(b in a))return function(){};var g=a[b],e=c(g);"function"===typeof e&&(e.prototype=e.prototype||{},Object.defineProperties(e,{__rrweb_original__:{enumerable:!1,value:g}}));a[b]=e;return function(){a[b]=g}}catch(d){return function(){}}}function la(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function ma(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function G(a,b){if(!a)return!1;if(a.nodeType===a.ELEMENT_NODE){var c=!1;"string"===typeof b?c=a.classList.contains(b):a.classList.forEach(function(a){b.test(a)&&(c=!0)});return c||G(a.parentNode,b)}return G(a.parentNode,b)}function na(a){var b=p.getId(a);return p.has(b)?a.parentNode&&a.parentNode.nodeType===a.DOCUMENT_NODE?!1:a.parentNode?na(a.parentNode):!0:!0}function oa(a){void 0===a&&(a=window);"NodeList"in a&&!a.NodeList.prototype.forEach&&(a.NodeList.prototype.forEach=Array.prototype.forEach);"DOMTokenList"in a&&!a.DOMTokenList.prototype.forEach&&(a.DOMTokenList.prototype.forEach=Array.prototype.forEach)}function ya(a){switch(a.type){case f.EventType.DomContentLoaded:case f.EventType.Load:case f.EventType.Custom:return!1;case f.EventType.FullSnapshot:case f.EventType.Meta:return!0}switch(a.data.source){case f.IncrementalSource.MouseMove:case f.IncrementalSource.MouseInteraction:case f.IncrementalSource.TouchMove:case f.IncrementalSource.MediaInteraction:return!1}return!0}function za(a){var b,c,g={},e=function(a,b){var c={value:a,parent:b,children:[]};return g[a.node.id]=c},d=[];try{for(var h=z(a),q=h.next();!q.done;q=h.next()){var f=q.value,k=f.nextId,M=f.parentId;if(k&&k in g){var l=g[k];if(l.parent){var t=l.parent.children.indexOf(l);l.parent.children.splice(t,0,e(f,l.parent))}else t=d.indexOf(l),d.splice(t,0,e(f,null))}else if(M in g){var n=g[M];n.children.push(e(f,n))}else d.push(e(f,null))}}catch(y){b={error:y}}finally{try{q&&!q.done&&(c=h["return"])&&c.call(h)}finally{if(b)throw b.error;}}return d}function pa(a,b){b(a.value);for(var c=a.children.length-1;0<=c;c--)pa(a.children[c],b)}function qa(a,b){a["delete"](b);b.childNodes.forEach(function(b){return qa(a,b)})}function ra(a,b){var c=b.parentNode;if(!c)return!1;var g=p.getId(c);return a.some(function(a){return a.id===g})?!0:ra(a,c)}function sa(a,b){var c=b.parentNode;return c?a.has(c)?!0:sa(a,c):!1}function Ra(a,b,c,g,e){K.init(a,b,c,g,e);a=new MutationObserver(K.processMutations.bind(K));a.observe(document,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0});return a}function Sa(a,b){if(!1===b.mousemove)return function(){};var c="number"===typeof b.mousemove?b.mousemove:50,g=[],e,d=da(function(b){var c=Date.now()-e;a(g.map(function(a){a.timeOffset-=c;return a}),b?f.IncrementalSource.TouchMove:f.IncrementalSource.MouseMove);g=[];e=null},500),c=da(function(a){var b=a.target,c=a.changedTouches?a.changedTouches[0]:a,h=c.clientX,c=c.clientY;e||(e=Date.now());g.push({x:h,y:c,id:p.getId(b),timeOffset:Date.now()-e});d(!!a.changedTouches)},c,{trailing:!1}),h=[F("mousemove",c),F("touchmove",c)];return function(){h.forEach(function(a){return a()})}}function Ta(a,b,c){if(!1===c.mouseInteraction)return function(){};var g=!0===c.mouseInteraction||void 0===c.mouseInteraction?{}:c.mouseInteraction,e=[],d=function(c){return function(d){if(!G(d.target,b)){var g=p.getId(d.target);d=d.changedTouches?d.changedTouches[0]:d;a({type:f.MouseInteractions[c],id:g,x:d.clientX,y:d.clientY})}}};Object.keys(f.MouseInteractions).filter(function(a){return Number.isNaN(Number(a))&&!a.endsWith("_Departed")&&!1!==g[a]}).forEach(function(a){var b=a.toLowerCase();a=d(a);e.push(F(b,a))});return function(){e.forEach(function(a){return a()})}}function Ua(a,b,c){c=da(function(c){if(c.target&&!G(c.target,b)){var g=p.getId(c.target);c.target===document?(c=document.scrollingElement||document.documentElement,a({id:g,x:c.scrollLeft,y:c.scrollTop})):a({id:g,x:c.target.scrollLeft,y:c.target.scrollTop})}},c.scroll||100);return F("scroll",c)}function Va(a){var b=da(function(){var b=la(),g=ma();a({width:Number(g),height:Number(b)})},200);return F("resize",b,window)}function Wa(a,b,c,g,e){function d(a){var d=a.target;if(d&&d.tagName&&!(0>Xa.indexOf(d.tagName))&&!G(d,b)&&(a=d.type,"password"!==a&&!d.classList.contains(c))){var e=d.value,f=!1;if("radio"===a||"checkbox"===a)f=d.checked;else if(g[d.tagName.toLowerCase()]||g[a])e="*".repeat(e.length);h(d,{text:e,isChecked:f});e=d.name;"radio"===a&&e&&f&&document.querySelectorAll('input[type\x3d"radio"][name\x3d"'+e+'"]').forEach(function(a){a!==d&&h(a,{text:a.value,isChecked:!f})})}}function h(b,c){var d=Aa.get(b);d&&d.text===c.text&&d.isChecked===c.isChecked||(Aa.set(b,c),d=p.getId(b),a(A(A({},c),{id:d})))}var f=("last"===e.input?["change"]:["input","change"]).map(function(a){return F(a,d)});e=Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,"value");var r=[[HTMLInputElement.prototype,"value"],[HTMLInputElement.prototype,"checked"],[HTMLSelectElement.prototype,"value"],[HTMLTextAreaElement.prototype,"value"],[HTMLSelectElement.prototype,"selectedIndex"]];e&&e.set&&f.push.apply(f,m(r.map(function(a){return ia(a[0],a[1],{set:function(){d({target:this})}})})));return function(){f.forEach(function(a){return a()})}}function Ya(a){var b=CSSStyleSheet.prototype.insertRule;CSSStyleSheet.prototype.insertRule=function(c,e){var d=p.getId(this.ownerNode);-1!==d&&a({id:d,adds:[{rule:c,index:e}]});return b.apply(this,arguments)};var c=CSSStyleSheet.prototype.deleteRule;CSSStyleSheet.prototype.deleteRule=function(b){var g=p.getId(this.ownerNode);-1!==g&&a({id:g,removes:[{index:b}]});return c.apply(this,arguments)};return function(){CSSStyleSheet.prototype.insertRule=b;CSSStyleSheet.prototype.deleteRule=c}}function Za(a,b){var c=function(c){return function(d){(d=d.target)&&!G(d,b)&&a({type:"play"===c?Z.Play:Z.Pause,id:p.getId(d)})}},g=[F("play",c("play")),F("pause",c("pause"))];return function(){g.forEach(function(a){return a()})}}function $a(a,b){var c,g,e=Object.getOwnPropertyNames(CanvasRenderingContext2D.prototype),d=[],h=function(c){try{if("function"!==typeof CanvasRenderingContext2D.prototype[c])return"continue";var g=ka(CanvasRenderingContext2D.prototype,c,function(d){return function(){for(var g=this,n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];G(this.canvas,b)||setTimeout(function(){var b=m(n);"drawImage"===c&&b[0]&&b[0]instanceof HTMLCanvasElement&&(b[0]=b[0].toDataURL());a({id:p.getId(g.canvas),property:c,args:b})},0);return d.apply(this,n)}});d.push(g)}catch(X){g=ia(CanvasRenderingContext2D.prototype,c,{set:function(b){a({id:p.getId(this.canvas),property:c,args:[b],setter:!0})}}),d.push(g)}};try{for(var f=z(e),r=f.next();!r.done;r=f.next())h(r.value)}catch(k){c={error:k}}finally{try{r&&!r.done&&(g=f["return"])&&g.call(f)}finally{if(c)throw c.error;}}return function(){d.forEach(function(a){return a()})}}function ab(a){var b=[],c=new WeakMap,g=FontFace;window.FontFace=function(a,b,e){var d=new g(a,b,e);c.set(d,{family:a,buffer:"string"!==typeof b,descriptors:e,fontSource:"string"===typeof b?b:JSON.stringify(Array.from(new Uint8Array(b)))});return d};var e=ka(document.fonts,"add",function(b){return function(d){setTimeout(function(){var b=c.get(d);b&&(a(b),c["delete"](d))},0);return b.apply(this,[d])}});b.push(function(){window.FonFace=g});b.push(e);return function(){b.forEach(function(a){return a()})}}function bb(a,b){var c=a.mutationCb,g=a.mousemoveCb,e=a.mouseInteractionCb,d=a.scrollCb,h=a.viewportResizeCb,f=a.inputCb,r=a.mediaInteractionCb,k=a.styleSheetRuleCb,l=a.canvasMutationCb,p=a.fontCb;a.mutationCb=function(){for(var a=[],d=0;d<arguments.length;d++)a[d]=arguments[d];b.mutation&&b.mutation.apply(b,m(a));c.apply(void 0,m(a))};a.mousemoveCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.mousemove&&b.mousemove.apply(b,m(a));g.apply(void 0,m(a))};a.mouseInteractionCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.mouseInteraction&&b.mouseInteraction.apply(b,m(a));e.apply(void 0,m(a))};a.scrollCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.scroll&&b.scroll.apply(b,m(a));d.apply(void 0,m(a))};a.viewportResizeCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.viewportResize&&b.viewportResize.apply(b,m(a));h.apply(void 0,m(a))};a.inputCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.input&&b.input.apply(b,m(a));f.apply(void 0,m(a))};a.mediaInteractionCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.mediaInteaction&&b.mediaInteaction.apply(b,m(a));r.apply(void 0,m(a))};a.styleSheetRuleCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.styleSheetRule&&b.styleSheetRule.apply(b,m(a));k.apply(void 0,m(a))};a.canvasMutationCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.canvasMutation&&b.canvasMutation.apply(b,m(a));l.apply(void 0,m(a))};a.fontCb=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];b.font&&b.font.apply(b,m(a));p.apply(void 0,m(a))}}function cb(a,b){void 0===b&&(b={});bb(a,b);var c=Ra(a.mutationCb,a.blockClass,a.inlineStylesheet,a.maskInputOptions,a.recordCanvas),g=Sa(a.mousemoveCb,a.sampling),e=Ta(a.mouseInteractionCb,a.blockClass,a.sampling),d=Ua(a.scrollCb,a.blockClass,a.sampling),h=Va(a.viewportResizeCb),f=Wa(a.inputCb,a.blockClass,a.ignoreClass,a.maskInputOptions,a.sampling),r=Za(a.mediaInteractionCb,a.blockClass),k=Ya(a.styleSheetRuleCb),l=a.recordCanvas?$a(a.canvasMutationCb,a.blockClass):function(){},m=a.collectFonts?ab(a.fontCb):function(){};return function(){c.disconnect();g();e();d();h();f();r();k();l();m()}}function C(a){return A(A({},a),{timestamp:Date.now()})}function ea(a){function b(a){var b,c,d,g;void 0===a&&(a=!1);D(C({type:f.EventType.Meta,data:{href:window.location.href,width:ma(),height:la()}}),a);a=K.isFrozen();K.freeze();var e;e=h;var n=r,k=void 0;void 0===e&&(e="rr-block");void 0===n&&(n=!0);void 0===k&&(k=null);var q={};e=[Y(document,document,q,e,k,!1,n,!0===x?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===x?{}:x,{},E),q];n=w(e,2);e=n[0];n=n[1];if(!e)return console.warn("Failed to snapshot the document");p.map=n;D(C({type:f.EventType.FullSnapshot,data:{node:e,initialOffset:{left:void 0!==window.pageXOffset?window.pageXOffset:(null===document||void 0===document?void 0:document.documentElement.scrollLeft)||(null===(c=null===(b=null===document||void 0===document?void 0:document.body)||void 0===b?void 0:b.parentElement)||void 0===c?void 0:c.scrollLeft)||(null===document||void 0===document?void 0:document.body.scrollLeft)||0,top:void 0!==window.pageYOffset?window.pageYOffset:(null===document||void 0===document?void 0:document.documentElement.scrollTop)||(null===(g=null===(d=null===document||void 0===document?void 0:document.body)||void 0===d?void 0:d.parentElement)||void 0===g?void 0:g.scrollTop)||(null===document||void 0===document?void 0:document.body.scrollTop)||0}}}));a||(K.emit(),K.unfreeze())}void 0===a&&(a={});var c=a.emit,g=a.checkoutEveryNms,e=a.checkoutEveryNth,d=a.blockClass,h=void 0===d?"rr-block":d,d=a.ignoreClass,q=void 0===d?"rr-ignore":d,d=a.inlineStylesheet,r=void 0===d?!0:d,d=a.maskAllInputs,k=a.maskInputOptions,l=a.hooks,m=a.packFn,t=a.sampling,n=void 0===t?{}:t,t=a.mousemoveWait,y=a.recordCanvas,E=void 0===y?!1:y;a=a.collectFonts;var U=void 0===a?!1:a;if(!c)throw Error("emit function is required");void 0!==t&&void 0===n.mousemove&&(n.mousemove=t);var x=!0===d?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:void 0!==k?k:{};oa();var ha,v=0;D=function(a,d){!K.isFrozen()||a.type===f.EventType.FullSnapshot||a.type==f.EventType.IncrementalSnapshot&&a.data.source==f.IncrementalSource.Mutation||(K.emit(),K.unfreeze());c(m?m(a):a,d);if(a.type===f.EventType.FullSnapshot)ha=a,v=0;else if(a.type===f.EventType.IncrementalSnapshot){v++;var n=g&&a.timestamp-ha.timestamp>g;(e&&v>=e||n)&&b(!0)}};try{var u=[];u.push(F("DOMContentLoaded",function(){D(C({type:f.EventType.DomContentLoaded,data:{}}))}));var O=function(){b();u.push(cb({mutationCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.Mutation},a)}))},mousemoveCb:function(a,b){return D(C({type:f.EventType.IncrementalSnapshot,data:{source:b,positions:a}}))},mouseInteractionCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.MouseInteraction},a)}))},scrollCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.Scroll},a)}))},viewportResizeCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.ViewportResize},a)}))},inputCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.Input},a)}))},mediaInteractionCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.MediaInteraction},a)}))},styleSheetRuleCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.StyleSheetRule},a)}))},canvasMutationCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.CanvasMutation},a)}))},fontCb:function(a){return D(C({type:f.EventType.IncrementalSnapshot,data:A({source:f.IncrementalSource.Font},a)}))},blockClass:h,ignoreClass:q,maskInputOptions:x,inlineStylesheet:r,sampling:n,recordCanvas:E,collectFonts:U},l))};"interactive"===document.readyState||"complete"===document.readyState?O():u.push(F("load",function(){D(C({type:f.EventType.Load,data:{}}));O()},window));return function(){u.forEach(function(a){return a()})}}catch(ga){console.warn(ga)}}function Ba(a){a=a||Object.create(null);return{on:function(b,c){(a[b]||(a[b]=[])).push(c)},off:function(b,c){a[b]&&a[b].splice(a[b].indexOf(c)>>>0,1)},emit:function(b,c){(a[b]||[]).slice().map(function(a){a(c)});(a["*"]||[]).slice().map(function(a){a(b,c)})}}}function db(a,b){function c(a,b){this.scrollLeft=a;this.scrollTop=b}function g(a){if(null===a||"object"!==typeof a||void 0===a.behavior||"auto"===a.behavior||"instant"===a.behavior)return!0;if("object"===typeof a&&"smooth"===a.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+a.behavior+" is not a valid value for enumeration ScrollBehavior.");}function e(a,b){if("Y"===b)return a.clientHeight+t<a.scrollHeight;if("X"===b)return a.clientWidth+t<a.scrollWidth}function d(b,c){var d=a.getComputedStyle(b,null)["overflow"+c];return"auto"===d||"scroll"===d}function h(a){var b=e(a,"Y")&&d(a,"Y");a=e(a,"X")&&d(a,"X");return b||a}function f(b){var c,d;d=(m()-b.startTime)/468;c=.5*(1-Math.cos(Math.PI*(1<d?1:d)));d=b.startX+(b.x-b.startX)*c;c=b.startY+(b.y-b.startY)*c;b.method.call(b.scrollable,d,c);d===b.x&&c===b.y||a.requestAnimationFrame(f.bind(a,b))}function r(d,g,e){var h,n,k,q=m();d===b.body?(h=a,n=a.scrollX||a.pageXOffset,d=a.scrollY||a.pageYOffset,k=l.scroll):(h=d,n=d.scrollLeft,d=d.scrollTop,k=c);f({scrollable:h,method:k,startTime:q,startX:n,startY:d,x:g,y:e})}void 0===a&&(a=window);void 0===b&&(b=document);if(!("scrollBehavior"in b.documentElement.style&&!0!==a.__forceSmoothScrollPolyfill__)){var k=a.HTMLElement||a.Element,l={scroll:a.scroll||a.scrollTo,scrollBy:a.scrollBy,elementScroll:k.prototype.scroll||c,scrollIntoView:k.prototype.scrollIntoView},m=a.performance&&a.performance.now?a.performance.now.bind(a.performance):Date.now,t=/MSIE |Trident\/|Edge\//.test(a.navigator.userAgent)?1:0;a.scroll=a.scrollTo=function(c,d){void 0!==c&&(!0===g(c)?l.scroll.call(a,void 0!==c.left?c.left:"object"!==typeof c?c:a.scrollX||a.pageXOffset,void 0!==c.top?c.top:void 0!==d?d:a.scrollY||a.pageYOffset):r.call(a,b.body,void 0!==c.left?~~c.left:a.scrollX||a.pageXOffset,void 0!==c.top?~~c.top:a.scrollY||a.pageYOffset))};a.scrollBy=function(c,d){void 0!==c&&(g(c)?l.scrollBy.call(a,void 0!==c.left?c.left:"object"!==typeof c?c:0,void 0!==c.top?c.top:void 0!==d?d:0):r.call(a,b.body,~~c.left+(a.scrollX||a.pageXOffset),~~c.top+(a.scrollY||a.pageYOffset)))};k.prototype.scroll=k.prototype.scrollTo=function(a,b){if(void 0!==a)if(!0===g(a)){if("number"===typeof a&&void 0===b)throw new SyntaxError("Value could not be converted");l.elementScroll.call(this,void 0!==a.left?~~a.left:"object"!==typeof a?~~a:this.scrollLeft,void 0!==a.top?~~a.top:void 0!==b?~~b:this.scrollTop)}else{var c=a.left,d=a.top;r.call(this,this,"undefined"===typeof c?this.scrollLeft:~~c,"undefined"===typeof d?this.scrollTop:~~d)}};k.prototype.scrollBy=function(a,b){void 0!==a&&(!0===g(a)?l.elementScroll.call(this,void 0!==a.left?~~a.left+this.scrollLeft:~~a+this.scrollLeft,void 0!==a.top?~~a.top+this.scrollTop:~~b+this.scrollTop):this.scroll({left:~~a.left+this.scrollLeft,top:~~a.top+this.scrollTop,behavior:a.behavior}))};k.prototype.scrollIntoView=function(c){if(!0===g(c))l.scrollIntoView.call(this,void 0===c?!0:c);else{for(c=this;c!==b.body&&!1===h(c);)c=c.parentNode||c.host;var d=c.getBoundingClientRect(),e=this.getBoundingClientRect();c!==b.body?(r.call(this,c,c.scrollLeft+e.left-d.left,c.scrollTop+e.top-d.top),"fixed"!==a.getComputedStyle(c).position&&a.scrollBy({left:d.left,top:d.top,behavior:"smooth"})):a.scrollBy({left:e.left,top:e.top,behavior:"smooth"})}}}}function Ca(a,b){if(a.type===f.EventType.IncrementalSnapshot&&a.data.source===f.IncrementalSource.MouseMove){var c=a.timestamp+a.data.positions[0].timeOffset;a.delay=c-b;return c-b}a.delay=a.timestamp-b;return a.delay}function Da(a){return void 0===a?[]:[].concat(a)}function aa(a){return{type:"xstate.assign",assignment:a}}function Ea(a,b){return"string"==typeof(a="string"==typeof a&&b&&b[a]?b[a]:a)?{type:a}:"function"==typeof a?{type:a.name,exec:a}:a}function ja(a){return function(b){return a===b}}function Fa(a,b){return{value:a,context:b,actions:[],changed:!1,matches:ja(a)}}function Ga(a,b){void 0===b&&(b={});var c={config:a,_options:b,initialState:{value:a.initial,actions:Da(a.states[a.initial].entry).map(function(a){return Ea(a,b.actions)}),context:a.context,matches:ja(a.initial)},transition:function(b,e){var d,g,f="string"==typeof b?{value:b,context:a.context}:b,r=f.value,k=f.context,l="string"==typeof e?{type:e}:e,m=a.states[r];if(m.on){var f=Da(m.on[l.type]),t=function(b){if(void 0===b)return{value:Fa(r,k)};var d="string"==typeof b?{target:b}:b;b=d.target;b=void 0===b?r:b;var g=d.actions,g=void 0===g?[]:g,d=d.cond,e=k;if((void 0===d?function(){return!0}:d)(k,l)){var h=!1,d=[].concat(m.exit,g,a.states[b].entry).filter(function(a){return a}).map(function(a){return Ea(a,c._options.actions)}).filter(function(a){if("xstate.assign"===a.type){h=!0;var b=Object.assign({},e);return"function"==typeof a.assignment?b=a.assignment(e,l):Object.keys(a.assignment).forEach(function(c){b[c]="function"==typeof a.assignment[c]?a.assignment[c](e,l):a.assignment[c]}),e=b,!1}return!0});return{value:{value:b,context:e,actions:d,changed:b!==r||0<d.length||h,matches:ja(b)}}}};try{for(var n=function(a){var b="function"==typeof Symbol&&a[Symbol.iterator],c=0;return b?b.call(a):{next:function(){return a&&c>=a.length&&(a=void 0),{value:a&&a[c++],done:!a}}}}(f),y=n.next();!y.done;y=n.next()){var E=t(y.value);if("object"==typeof E)return E.value}}catch(U){d={error:U}}finally{try{y&&!y.done&&(g=n["return"])&&g.call(n)}finally{if(d)throw d.error;}}}return Fa(r,k)}};return c}function Ha(a){var b=a.initialState,c=ba.NotStarted,g=new Set,e={_machine:a,send:function(d){c===ba.Running&&(b=a.transition(b,d),Ia(b,"string"==typeof d?{type:d}:d),g.forEach(function(a){return a(b)}))},subscribe:function(a){return g.add(a),a(b),{unsubscribe:function(){return g["delete"](a)}}},start:function(d){d&&(d="object"==typeof d?d:{context:a.config.context,value:d},b={value:d.value,actions:[],context:d.context,matches:ja(d.value)});return c=ba.Running,Ia(b,eb),e},stop:function(){return c=ba.Stopped,g.clear(),e},get state(){return b},get status(){return c}};return e}function fb(a,b){for(var c=a.length-1;0<=c;c--){var g=a[c];if(g.type===f.EventType.Meta&&g.timestamp<=b)return a.slice(c)}return a}function gb(a,b){var c=b.getCastFn,g=b.emitter,e=Ga({id:"player",context:a,initial:"paused",states:{playing:{on:{PAUSE:{target:"paused",actions:["pause"]},CAST_EVENT:{target:"playing",actions:"castEvent"},END:{target:"paused",actions:["resetLastPlayedEvent","pause"]},ADD_EVENT:{target:"playing",actions:["addEvent"]}}},paused:{on:{PLAY:{target:"playing",actions:["recordTimeOffset","play"]},CAST_EVENT:{target:"paused",actions:"castEvent"},TO_LIVE:{target:"live",actions:["startLive"]},ADD_EVENT:{target:"paused",actions:["addEvent"]}}},live:{on:{ADD_EVENT:{target:"live",actions:["addEvent"]},CAST_EVENT:{target:"live",actions:["castEvent"]}}}}},{actions:{castEvent:aa({lastPlayedEvent:function(a,b){return"CAST_EVENT"===b.type?b.payload.event:a.lastPlayedEvent}}),recordTimeOffset:aa(function(a,b){var c=a.timeOffset;"payload"in b&&"timeOffset"in b.payload&&(c=b.payload.timeOffset);return A(A({},a),{timeOffset:c,baselineTime:a.events[0].timestamp+c})}),play:function(a){var b,d,e,k,l,m=a.timer,t=a.events,n=a.baselineTime,y=a.lastPlayedEvent;m.clear();try{for(var E=z(t),p=E.next();!p.done;p=E.next()){var x=p.value;Ca(x,n)}}catch(O){b={error:O}}finally{try{p&&!p.done&&(d=E["return"])&&d.call(E)}finally{if(b)throw b.error;}}a=fb(t,n);var u=[];b=function(a){var b=null===y||void 0===y?void 0:y.timestamp;(null===y||void 0===y?void 0:y.type)===f.EventType.IncrementalSnapshot&&y.data.source===f.IncrementalSource.MouseMove&&(b=y.timestamp+(null===(l=y.data.positions[0])||void 0===l?void 0:l.timeOffset));if(b&&b<n&&(a.timestamp<=b||a===y)||(b=a.timestamp<n)&&!ya(a))return"continue";var d=c(a,b);b?d():u.push({doAction:function(){d();g.emit(f.ReplayerEvents.EventCast,a)},delay:a.delay})};try{for(var v=z(a),w=v.next();!w.done;w=v.next())x=w.value,b(x)}catch(O){e={error:O}}finally{try{w&&!w.done&&(k=v["return"])&&k.call(v)}finally{if(e)throw e.error;}}g.emit(f.ReplayerEvents.Flush);m.addActions(u);m.start()},pause:function(a){a.timer.clear()},resetLastPlayedEvent:aa(function(a){return A(A({},a),{lastPlayedEvent:null})}),startLive:aa({baselineTime:function(a,b){a.timer.toggleLiveMode(!0);a.timer.start();return"TO_LIVE"===b.type&&b.payload.baselineTime?b.payload.baselineTime:Date.now()}}),addEvent:aa(function(a,b){var d=a.baselineTime,e=a.timer,h=a.events;if("ADD_EVENT"===b.type){var l=b.payload.event;Ca(l,d);h.push(l);var d=l.timestamp<d,m=c(l,d);d?m():(e.addAction({doAction:function(){m();g.emit(f.ReplayerEvents.EventCast,l)},delay:l.delay}),e.isActive()||e.start())}return A(A({},a),{events:h})})}});return Ha(e)}function hb(a){a=Ga({id:"speed",context:a,initial:"normal",states:{normal:{on:{FAST_FORWARD:{target:"skipping",actions:["recordSpeed","setSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}},skipping:{on:{BACK_TO_NORMAL:{target:"normal",actions:["restoreSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}}}},{actions:{setSpeed:function(a,c){"payload"in c&&a.timer.setSpeed(c.payload.speed)},recordSpeed:aa({normalSpeed:function(a){return a.timer.speed}}),restoreSpeed:function(a){a.timer.setSpeed(a.normalSpeed)}}});return Ha(a)}var A=function(){A=Object.assign||function(a){for(var b,c=1,g=arguments.length;c<g;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a};return A.apply(this,arguments)},B;(function(a){a[a.Document=0]="Document";a[a.DocumentType=1]="DocumentType";a[a.Element=2]="Element";a[a.Text=3]="Text";a[a.CDATA=4]="CDATA";a[a.Comment=5]="Comment"})(B||(B={}));var Oa=1,Ka=/[^a-z1-6-_]/,La=/url\((?:(')([^']*)'|(")([^"]*)"|([^)]*))\)/gm,Ma=/^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/).*/,Na=/^(data:)([\w\/\+\-]+);(charset=[\w-]+|base64|utf-?8).*,(.*)/i,va=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,wa={script:"noscript",altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"},xa=/([^\\]):hover/g;(function(a){a[a.DomContentLoaded=0]="DomContentLoaded";a[a.Load=1]="Load";a[a.FullSnapshot=2]="FullSnapshot";a[a.IncrementalSnapshot=3]="IncrementalSnapshot";a[a.Meta=4]="Meta";a[a.Custom=5]="Custom"})(f.EventType||(f.EventType={}));(function(a){a[a.Mutation=0]="Mutation";a[a.MouseMove=1]="MouseMove";a[a.MouseInteraction=2]="MouseInteraction";a[a.Scroll=3]="Scroll";a[a.ViewportResize=4]="ViewportResize";a[a.Input=5]="Input";a[a.TouchMove=6]="TouchMove";a[a.MediaInteraction=7]="MediaInteraction";a[a.StyleSheetRule=8]="StyleSheetRule";a[a.CanvasMutation=9]="CanvasMutation";a[a.Font=10]="Font"})(f.IncrementalSource||(f.IncrementalSource={}));(function(a){a[a.MouseUp=0]="MouseUp";a[a.MouseDown=1]="MouseDown";a[a.Click=2]="Click";a[a.ContextMenu=3]="ContextMenu";a[a.DblClick=4]="DblClick";a[a.Focus=5]="Focus";a[a.Blur=6]="Blur";a[a.TouchStart=7]="TouchStart";a[a.TouchMove_Departed=8]="TouchMove_Departed";a[a.TouchEnd=9]="TouchEnd"})(f.MouseInteractions||(f.MouseInteractions={}));var Z;(function(a){a[a.Play=0]="Play";a[a.Pause=1]="Pause"})(Z||(Z={}));(function(a){a.Start="start";a.Pause="pause";a.Resume="resume";a.Resize="resize";a.Finish="finish";a.FullsnapshotRebuilded="fullsnapshot-rebuilded";a.LoadStylesheetStart="load-stylesheet-start";a.LoadStylesheetEnd="load-stylesheet-end";a.SkipStart="skip-start";a.SkipEnd="skip-end";a.MouseInteraction="mouse-interaction";a.EventCast="event-cast";a.CustomEvent="custom-event";a.Flush="flush";a.StateChange="state-change"})(f.ReplayerEvents||(f.ReplayerEvents={}));var p={map:{},getId:function(a){return a.__sn?a.__sn.id:-1},getNode:function(a){return p.map[a]||null},removeNodeFromMap:function(a){delete p.map[a.__sn&&a.__sn.id];a.childNodes&&a.childNodes.forEach(function(a){return p.removeNodeFromMap(a)})},has:function(a){return p.map.hasOwnProperty(a)}},Ja=function(){function a(){this.reset()}a.prototype.add=function(a){var b=this.indexes.get(a.parentId);a={id:a.node.id,mutation:a,children:[],texts:[],attributes:[]};b?(a.parent=b,b.children[a.id]=a):this.tree[a.id]=a;this.indexes.set(a.id,a)};a.prototype.remove=function(a){var b=this,g=this.indexes.get(a.parentId),e=this.indexes.get(a.id),d=function(a){b.removeIdSet.add(a);a=p.getNode(a);null===a||void 0===a?void 0:a.childNodes.forEach(function(a){"__sn"in a&&d(a.__sn.id)})},h=function(c){b.removeIdSet.add(c.id);Object.values(c.children).forEach(function(a){return h(a)});if(c=b.indexes.get(c.id)){var d=c.parent;d&&(delete c.parent,delete d.children[c.id],b.indexes["delete"](a.id))}};e?(g?(delete e.parent,delete g.children[e.id],this.indexes["delete"](a.id)):(delete this.tree[e.id],this.indexes["delete"](e.id)),h(e)):(this.removeNodeMutations.push(a),d(a.id))};a.prototype.text=function(a){var b=this.indexes.get(a.id);b?b.texts.push(a):this.textMutations.push(a)};a.prototype.attribute=function(a){var b=this.indexes.get(a.id);b?b.attributes.push(a):this.attributeMutations.push(a)};a.prototype.scroll=function(a){this.scrollMap.set(a.id,a)};a.prototype.input=function(a){this.inputMap.set(a.id,a)};a.prototype.flush=function(){var a,c,g,e,d=this,h={source:f.IncrementalSource.Mutation,removes:this.removeNodeMutations,texts:this.textMutations,attributes:this.attributeMutations,adds:[]},l=function(a,b){b&&d.removeIdSet.add(a.id);h.texts=h.texts.concat(b?[]:a.texts).filter(function(a){return!d.removeIdSet.has(a.id)});h.attributes=h.attributes.concat(b?[]:a.attributes).filter(function(a){return!d.removeIdSet.has(a.id)});d.removeIdSet.has(a.id)||d.removeIdSet.has(a.mutation.parentId)||b?Object.values(a.children).forEach(function(a){return l(a,!0)}):(h.adds.push(a.mutation),a.children&&Object.values(a.children).forEach(function(a){return l(a,!1)}))};Object.values(this.tree).forEach(function(a){return l(a,!1)});try{for(var r=z(this.scrollMap.keys()),k=r.next();!k.done;k=r.next()){var m=k.value;this.removeIdSet.has(m)&&this.scrollMap["delete"](m)}}catch(n){a={error:n}}finally{try{k&&!k.done&&(c=r["return"])&&c.call(r)}finally{if(a)throw a.error;}}try{for(var p=z(this.inputMap.keys()),t=p.next();!t.done;t=p.next())m=t.value,this.removeIdSet.has(m)&&this.inputMap["delete"](m)}catch(n){g={error:n}}finally{try{t&&!t.done&&(e=p["return"])&&e.call(p)}finally{if(g)throw g.error;}}a=new Map(this.scrollMap);c=new Map(this.inputMap);this.reset();return{mutationData:h,scrollMap:a,inputMap:c}};a.prototype.reset=function(){this.tree=[];this.indexes=new Map;this.removeNodeMutations=[];this.textMutations=[];this.attributeMutations=[];this.removeIdSet=new Set;this.scrollMap=new Map;this.inputMap=new Map};return a}(),ib=Object.freeze({__proto__:null,on:F,mirror:p,throttle:da,hookSetter:ia,patch:ka,getWindowHeight:la,getWindowWidth:ma,isBlocked:G,isAncestorRemoved:na,isTouchEvent:function(a){return!!a.changedTouches},polyfill:oa,needCastInSyncMode:ya,TreeIndex:Ja,queueToResolveTrees:za,iterateResolveTree:pa}),jb=function(){function a(){this.length=0;this.head=null}a.prototype.get=function(a){if(a>=this.length)throw Error("Position outside of list range");for(var b=this.head,g=0;g<a;g++)b=(null===b||void 0===b?void 0:b.next)||null;return b};a.prototype.addNode=function(a){var b={value:a,previous:null,next:null};a.__ln=b;if(a.previousSibling&&"__ln"in a.previousSibling){var g=a.previousSibling.__ln.next;b.next=g;b.previous=a.previousSibling.__ln;a.previousSibling.__ln.next=b;g&&(g.previous=b)}else a.nextSibling&&"__ln"in a.nextSibling?(g=a.nextSibling.__ln.previous,b.previous=g,b.next=a.nextSibling.__ln,a.nextSibling.__ln.previous=b,g&&(g.next=b)):(this.head&&(this.head.previous=b),b.next=this.head,this.head=b);this.length++};a.prototype.removeNode=function(a){var b=a.__ln;if(this.head){if(b.previous){if(b.previous.next=b.next)b.next.previous=b.previous}else if(this.head=b.next)this.head.previous=null;a.__ln&&delete a.__ln;this.length--}};return a}(),K=new (function(){function a(){var a=this;this.frozen=!1;this.texts=[];this.attributes=[];this.removes=[];this.mapRemoves=[];this.movedMap={};this.addedSet=new Set;this.movedSet=new Set;this.droppedSet=new Set;this.processMutations=function(b){b.forEach(a.processMutation);a.frozen||a.emit()};this.emit=function(){for(var b,g,e,d,h=[],f=new jb,r=function(b){var c=b.nextSibling&&p.getId(b.nextSibling);-1===c&&G(b.nextSibling,a.blockClass)&&(c=null);return c},k=function(b){if(b.parentNode){var c=p.getId(b.parentNode),d=r(b);if(-1===c||-1===d)return f.addNode(b);h.push({parentId:c,nextId:d,node:Y(b,document,p.map,a.blockClass,null,!0,a.inlineStylesheet,a.maskInputOptions,void 0,a.recordCanvas)})}};a.mapRemoves.length;)p.removeNodeFromMap(a.mapRemoves.shift());try{for(var l=z(a.movedSet),m=l.next();!m.done;m=l.next()){var t=m.value;ra(a.removes,t)&&!a.movedSet.has(t.parentNode)||k(t)}}catch(E){b={error:E}}finally{try{m&&!m.done&&(g=l["return"])&&g.call(l)}finally{if(b)throw b.error;}}try{for(var n=z(a.addedSet),y=n.next();!y.done;y=n.next())t=y.value,sa(a.droppedSet,t)||ra(a.removes,t)?sa(a.movedSet,t)?k(t):a.droppedSet.add(t):k(t)}catch(E){e={error:E}}finally{try{y&&!y.done&&(d=n["return"])&&d.call(n)}finally{if(e)throw e.error;}}for(d=null;f.length;){b=null;d&&(g=p.getId(d.value.parentNode),e=r(d.value),-1!==g&&-1!==e&&(b=d));if(!b)for(d=f.length-1;0<=d;d--)if(l=f.get(d),g=p.getId(l.value.parentNode),e=r(l.value),-1!==g&&-1!==e){b=l;break}if(!b)break;d=b.previous;f.removeNode(b.value);k(b.value)}k={texts:a.texts.map(function(a){return{id:p.getId(a.node),value:a.value}}).filter(function(a){return p.has(a.id)}),attributes:a.attributes.map(function(a){return{id:p.getId(a.node),attributes:a.attributes}}).filter(function(a){return p.has(a.id)}),removes:a.removes,adds:h};if(k.texts.length||k.attributes.length||k.removes.length||k.adds.length)a.texts=[],a.attributes=[],a.removes=[],a.addedSet=new Set,a.movedSet=new Set,a.droppedSet=new Set,a.movedMap={},a.emissionCallback(k)};this.processMutation=function(b){switch(b.type){case "characterData":var c=b.target.textContent;G(b.target,a.blockClass)||c===b.oldValue||a.texts.push({value:c,node:b.target});break;case "attributes":c=b.target.getAttribute(b.attributeName);if(G(b.target,a.blockClass)||c===b.oldValue)break;var e=a.attributes.find(function(a){return a.node===b.target});e||(e={node:b.target,attributes:{}},a.attributes.push(e));e.attributes[b.attributeName]=N(document,b.attributeName,c);break;case "childList":b.addedNodes.forEach(function(c){return a.genAdds(c,b.target)}),b.removedNodes.forEach(function(c){var d=p.getId(c),e=p.getId(b.target);G(c,a.blockClass)||G(b.target,a.blockClass)||(a.addedSet.has(c)?(qa(a.addedSet,c),a.droppedSet.add(c)):a.addedSet.has(b.target)&&-1===d||na(b.target)||(a.movedSet.has(c)&&a.movedMap[d+"@"+e]?qa(a.movedSet,c):a.removes.push({parentId:e,id:d})),a.mapRemoves.push(c))})}};this.genAdds=function(b,g){if(!G(b,a.blockClass)){if("__sn"in b){a.movedSet.add(b);var c=null;g&&"__sn"in g&&(c=g.__sn.id);c&&(a.movedMap[b.__sn.id+"@"+c]=!0)}else a.addedSet.add(b),a.droppedSet["delete"](b);b.childNodes.forEach(function(b){return a.genAdds(b)})}}}a.prototype.init=function(a,c,g,e,d){this.blockClass=c;this.inlineStylesheet=g;this.maskInputOptions=e;this.recordCanvas=d;this.emissionCallback=a};a.prototype.freeze=function(){this.frozen=!0};a.prototype.unfreeze=function(){this.frozen=!1};a.prototype.isFrozen=function(){return this.frozen};return a}()),Xa=["INPUT","TEXTAREA","SELECT"],Aa=new WeakMap,D;ea.addCustomEvent=function(a,b){if(!D)throw Error("please add custom event after start recording");D(C({type:f.EventType.Custom,data:{tag:a,payload:b}}))};ea.freezePage=function(){K.freeze()};var ta=Object.freeze({__proto__:null,"default":Ba}),kb=function(){function a(a,c){void 0===a&&(a=[]);this.timeOffset=0;this.raf=null;this.actions=a;this.speed=c}a.prototype.addAction=function(a){var b=this.findActionIndex(a);this.actions.splice(b,0,a)};a.prototype.addActions=function(a){var b;(b=this.actions).push.apply(b,m(a))};a.prototype.start=function(){function a(b){e.timeOffset+=(b-c)*e.speed;for(c=b;g.length;)if(b=g[0],e.timeOffset>=b.delay)g.shift(),b.doAction();else break;if(0<g.length||e.liveMode)e.raf=requestAnimationFrame(a)}this.actions.sort(function(a,b){return a.delay-b.delay});this.timeOffset=0;var c=performance.now(),g=this.actions,e=this;this.raf=requestAnimationFrame(a)};a.prototype.clear=function(){this.raf&&(cancelAnimationFrame(this.raf),this.raf=null);this.actions.length=0};a.prototype.setSpeed=function(a){this.speed=a};a.prototype.toggleLiveMode=function(a){this.liveMode=a};a.prototype.isActive=function(){return null!==this.raf};a.prototype.findActionIndex=function(a){for(var b=0,g=this.actions.length-1;b<=g;){var e=Math.floor((b+g)/2);if(this.actions[e].delay<a.delay)b=e+1;else if(this.actions[e].delay>a.delay)g=e-1;else return e}return b};return a}(),ba;!function(a){a[a.NotStarted=0]="NotStarted";a[a.Running=1]="Running";a[a.Stopped=2]="Stopped"}(ba||(ba={}));var eb={type:"xstate.init"},Ia=function(a,b){return a.actions.forEach(function(c){return(c=c.exec)&&c(a.context,b)})},lb=Ba||ta,ua={duration:500,lineCap:"round",lineWidth:3,strokeStyle:"red"},ta=function(){function a(a,c){var b=this;this.mouseTail=null;this.tailPositions=[];this.emitter=lb();this.legacy_missingNodeRetryMap={};this.imageMap=new Map;if((null===c||void 0===c||!c.liveMode)&&2>a.length)throw Error("Replayer need at least 2 events.");var e={speed:1,root:document.body,loadTimeout:0,skipInactive:!1,showWarning:!0,showDebug:!1,blockClass:"rr-block",liveMode:!1,insertStyleRules:[],triggerFocus:!0,UNSAFE_replayCanvas:!1,mouseTail:ua};this.config=Object.assign({},e,c);this.handleResize=this.handleResize.bind(this);this.getCastFn=this.getCastFn.bind(this);this.emitter.on(f.ReplayerEvents.Resize,this.handleResize);this.setupDom();this.treeIndex=new Ja;this.fragmentParentMap=new Map;this.emitter.on(f.ReplayerEvents.Flush,function(){var a,c,d,e,g,f,h=b.treeIndex.flush(),l=h.scrollMap,h=h.inputMap;try{for(var m=z(l.values()),q=m.next();!q.done;q=m.next()){var x=q.value;b.applyScroll(x)}}catch(ca){a={error:ca}}finally{try{q&&!q.done&&(c=m["return"])&&c.call(m)}finally{if(a)throw a.error;}}try{for(var v=z(h.values()),u=v.next();!u.done;u=v.next())x=u.value,b.applyInput(x)}catch(ca){d={error:ca}}finally{try{u&&!u.done&&(e=v["return"])&&e.call(v)}finally{if(d)throw d.error;}}try{for(var S=z(b.fragmentParentMap.entries()),A=S.next();!A.done;A=S.next()){var D=w(A.value,2),F=D[0],C=D[1];p.map[C.__sn.id]=C;C.__sn.type===B.Element&&"textarea"===C.__sn.tagName&&F.textContent&&(C.value=F.textContent);C.appendChild(F)}}catch(ca){g={error:ca}}finally{try{A&&!A.done&&(f=S["return"])&&f.call(S)}finally{if(g)throw g.error;}}b.fragmentParentMap.clear()});e=new kb([],(null===c||void 0===c?void 0:c.speed)||e.speed);this.service=gb({events:a.map(function(a){return c&&c.unpackFn?c.unpackFn(a):a}),timer:e,timeOffset:0,baselineTime:0,lastPlayedEvent:null},{getCastFn:this.getCastFn,emitter:this.emitter});this.service.start();this.service.subscribe(function(a){b.emitter.emit(f.ReplayerEvents.StateChange,{player:a})});this.speedService=hb({normalSpeed:-1,timer:e});this.speedService.start();this.speedService.subscribe(function(a){b.emitter.emit(f.ReplayerEvents.StateChange,{speed:a})});var d=this.service.state.context.events.find(function(a){return a.type===f.EventType.Meta}),e=this.service.state.context.events.find(function(a){return a.type===f.EventType.FullSnapshot});if(d){var d=d.data,h=d.width,l=d.height;setTimeout(function(){b.emitter.emit(f.ReplayerEvents.Resize,{width:h,height:l})},0)}e&&this.rebuildFullSnapshot(e)}Object.defineProperty(a.prototype,"timer",{get:function(){return this.service.state.context.timer},enumerable:!1,configurable:!0});a.prototype.on=function(a,c){this.emitter.on(a,c)};a.prototype.setConfig=function(a){var b=this;Object.keys(a).forEach(function(c){b.config[c]=a[c]});this.config.skipInactive||this.backToNormal();"undefined"!==typeof a.speed&&this.speedService.send({type:"SET_SPEED",payload:{speed:a.speed}})};a.prototype.getMetaData=function(){var a=this.service.state.context.events[0],c=this.service.state.context.events[this.service.state.context.events.length-1];return{startTime:a.timestamp,endTime:c.timestamp,totalTime:c.timestamp-a.timestamp}};a.prototype.getCurrentTime=function(){return this.timer.timeOffset+this.getTimeOffset()};a.prototype.getTimeOffset=function(){var a=this.service.state.context;return a.baselineTime-a.events[0].timestamp};a.prototype.play=function(a){void 0===a&&(a=0);this.service.state.matches("paused")||this.service.send({type:"PAUSE"});this.service.send({type:"PLAY",payload:{timeOffset:a}});this.emitter.emit(f.ReplayerEvents.Start)};a.prototype.pause=function(a){void 0===a&&this.service.state.matches("playing")&&this.service.send({type:"PAUSE"});"number"===typeof a&&(this.play(a),this.service.send({type:"PAUSE"}));this.emitter.emit(f.ReplayerEvents.Pause)};a.prototype.resume=function(a){void 0===a&&(a=0);console.warn("The 'resume' will be departed in 1.0. Please use 'play' method which has the same interface.");this.play(a);this.emitter.emit(f.ReplayerEvents.Resume)};a.prototype.startLive=function(a){this.service.send({type:"TO_LIVE",payload:{baselineTime:a}})};a.prototype.addEvent=function(a){var b=this,g=this.config.unpackFn?this.config.unpackFn(a):a;Promise.resolve().then(function(){return b.service.send({type:"ADD_EVENT",payload:{event:g}})})};a.prototype.enableInteract=function(){this.iframe.setAttribute("scrolling","auto");this.iframe.style.pointerEvents="auto"};a.prototype.disableInteract=function(){this.iframe.setAttribute("scrolling","no");this.iframe.style.pointerEvents="none"};a.prototype.setupDom=function(){this.wrapper=document.createElement("div");this.wrapper.classList.add("replayer-wrapper");this.config.root.appendChild(this.wrapper);this.mouse=document.createElement("div");this.mouse.classList.add("replayer-mouse");this.wrapper.appendChild(this.mouse);!1!==this.config.mouseTail&&(this.mouseTail=document.createElement("canvas"),this.mouseTail.classList.add("replayer-mouse-tail"),this.mouseTail.style.display="none",this.wrapper.appendChild(this.mouseTail));this.iframe=document.createElement("iframe");var a=["allow-same-origin"];this.config.UNSAFE_replayCanvas&&a.push("allow-scripts");this.iframe.style.display="none";this.iframe.setAttribute("sandbox",a.join(" "));this.disableInteract();this.wrapper.appendChild(this.iframe);this.iframe.contentWindow&&this.iframe.contentDocument&&(db(this.iframe.contentWindow,this.iframe.contentDocument),oa(this.iframe.contentWindow))};a.prototype.handleResize=function(a){var b,g;try{for(var e=z([this.mouseTail,this.iframe]),d=e.next();!d.done;d=e.next()){var f=d.value;f&&(f.style.display="inherit",f.setAttribute("width",String(a.width)),f.setAttribute("height",String(a.height)))}}catch(q){b={error:q}}finally{try{d&&!d.done&&(g=e["return"])&&g.call(e)}finally{if(b)throw b.error;}}};a.prototype.getCastFn=function(a,c){var b=this;void 0===c&&(c=!1);var e;switch(a.type){case f.EventType.Custom:e=function(){b.emitter.emit(f.ReplayerEvents.CustomEvent,a)};break;case f.EventType.Meta:e=function(){return b.emitter.emit(f.ReplayerEvents.Resize,{width:a.data.width,height:a.data.height})};break;case f.EventType.FullSnapshot:e=function(){b.rebuildFullSnapshot(a,c);b.iframe.contentWindow.scrollTo(a.data.initialOffset)};break;case f.EventType.IncrementalSnapshot:e=function(){var d,e;b.applyIncremental(a,c);if(!c&&(a===b.nextUserInteractionEvent&&(b.nextUserInteractionEvent=null,b.backToNormal()),b.config.skipInactive&&!b.nextUserInteractionEvent)){try{for(var g=z(b.service.state.context.events),l=g.next();!l.done;l=g.next()){var k=l.value;if(!(k.timestamp<=a.timestamp)&&b.isUserInteraction(k)){k.delay-a.delay>1E4*b.speedService.state.context.timer.speed&&(b.nextUserInteractionEvent=k);break}}}catch(M){d={error:M}}finally{try{l&&!l.done&&(e=g["return"])&&e.call(g)}finally{if(d)throw d.error;}}b.nextUserInteractionEvent&&(d={speed:Math.min(Math.round((b.nextUserInteractionEvent.delay-a.delay)/5E3),360)},b.speedService.send({type:"FAST_FORWARD",payload:d}),b.emitter.emit(f.ReplayerEvents.SkipStart,d))}}}return function(){e&&e();b.service.send({type:"CAST_EVENT",payload:{event:a}});if(a===b.service.state.context.events[b.service.state.context.events.length-1]){var c=function(){b.backToNormal();b.service.send("END");b.emitter.emit(f.ReplayerEvents.Finish)};a.type===f.EventType.IncrementalSnapshot&&a.data.source===f.IncrementalSource.MouseMove&&a.data.positions.length?setTimeout(function(){c()},Math.max(0,-a.data.positions[0].timeOffset)):c()}}};a.prototype.rebuildFullSnapshot=function(a,c){void 0===c&&(c=!1);if(!this.iframe.contentDocument)return console.warn("Looks like your replayer has been destroyed.");Object.keys(this.legacy_missingNodeRetryMap).length&&console.warn("Found unresolved missing node map",this.legacy_missingNodeRetryMap);this.legacy_missingNodeRetryMap={};p.map=Qa(a.data.node,this.iframe.contentDocument)[1];var b=document.createElement("style"),e=this.iframe.contentDocument;e.documentElement.insertBefore(b,e.head);for(var e=["iframe, ."+this.config.blockClass+" { background: #ccc }","noscript { display: none !important; }"].concat(this.config.insertStyleRules),d=0;d<e.length;d++)b.sheet.insertRule(e[d],d);this.emitter.emit(f.ReplayerEvents.FullsnapshotRebuilded,a);c||this.waitForStylesheetLoad();this.config.UNSAFE_replayCanvas&&this.preloadAllImages()};a.prototype.waitForStylesheetLoad=function(){var a=this,c,g=null===(c=this.iframe.contentDocument)||void 0===c?void 0:c.head;if(g){var e=new Set,d,h=this.service.state,l=function(){h=a.service.state};this.emitter.on(f.ReplayerEvents.Start,l);this.emitter.on(f.ReplayerEvents.Pause,l);var m=function(){a.emitter.off(f.ReplayerEvents.Start,l);a.emitter.off(f.ReplayerEvents.Pause,l)};g.querySelectorAll('link[rel\x3d"stylesheet"]').forEach(function(b){b.sheet||(e.add(b),b.addEventListener("load",function(){e["delete"](b);0===e.size&&-1!==d&&(h.matches("playing")&&a.play(a.getCurrentTime()),a.emitter.emit(f.ReplayerEvents.LoadStylesheetEnd),d&&window.clearTimeout(d),m())}))});0<e.size&&(this.service.send({type:"PAUSE"}),this.emitter.emit(f.ReplayerEvents.LoadStylesheetStart),d=window.setTimeout(function(){h.matches("playing")&&a.play(a.getCurrentTime());d=-1;m()},this.config.loadTimeout))}};a.prototype.preloadAllImages=function(){var a,c,g=this,e=this.service.state,d=function(){e=g.service.state};this.emitter.on(f.ReplayerEvents.Start,d);this.emitter.on(f.ReplayerEvents.Pause,d);var h=0,l=0;try{for(var m=z(this.service.state.context.events),k=m.next();!k.done;k=m.next()){var p=k.value;if(p.type===f.EventType.IncrementalSnapshot&&p.data.source===f.IncrementalSource.CanvasMutation&&"drawImage"===p.data.property&&"string"===typeof p.data.args[0]&&!this.imageMap.has(p)){h++;var x=document.createElement("img");x.src=p.data.args[0];this.imageMap.set(p,x);x.onload=function(){l++;l===h&&(e.matches("playing")&&g.play(g.getCurrentTime()),g.emitter.off(f.ReplayerEvents.Start,d),g.emitter.off(f.ReplayerEvents.Pause,d))}}}}catch(t){a={error:t}}finally{try{k&&!k.done&&(c=m["return"])&&c.call(m)}finally{if(a)throw a.error;}}h!==l&&this.service.send({type:"PAUSE"})};a.prototype.applyIncremental=function(a,c){var b=this,e,d,h=a.data;switch(h.source){case f.IncrementalSource.Mutation:c&&(h.adds.forEach(function(a){return b.treeIndex.add(a)}),h.texts.forEach(function(a){return b.treeIndex.text(a)}),h.attributes.forEach(function(a){return b.treeIndex.attribute(a)}),h.removes.forEach(function(a){return b.treeIndex.remove(a)}));this.applyMutation(h,c);break;case f.IncrementalSource.MouseMove:c?(e=h.positions[h.positions.length-1],this.moveAndHover(h,e.x,e.y,e.id)):(h.positions.forEach(function(c){b.timer.addAction({doAction:function(){b.moveAndHover(h,c.x,c.y,c.id)},delay:c.timeOffset+a.timestamp-b.service.state.context.baselineTime})}),this.timer.addAction({doAction:function(){},delay:a.delay-(null===(e=h.positions[0])||void 0===e?NaN:e.timeOffset)}));break;case f.IncrementalSource.MouseInteraction:if(-1===h.id)break;var l=new Event(f.MouseInteractions[h.type].toLowerCase());e=p.getNode(h.id);if(!e)return this.debugNodeNotFound(h,h.id);this.emitter.emit(f.ReplayerEvents.MouseInteraction,{type:h.type,target:e});var m=this.config.triggerFocus;switch(h.type){case f.MouseInteractions.Blur:"blur"in e&&e.blur();break;case f.MouseInteractions.Focus:m&&e.focus&&e.focus({preventScroll:!0});break;case f.MouseInteractions.Click:case f.MouseInteractions.TouchStart:case f.MouseInteractions.TouchEnd:c||(this.moveAndHover(h,h.x,h.y,h.id),this.mouse.classList.remove("active"),void 0,this.mouse.classList.add("active"));break;default:e.dispatchEvent(l)}break;case f.IncrementalSource.Scroll:if(-1===h.id)break;if(c){this.treeIndex.scroll(h);break}this.applyScroll(h);break;case f.IncrementalSource.ViewportResize:this.emitter.emit(f.ReplayerEvents.Resize,{width:h.width,height:h.height});break;case f.IncrementalSource.Input:if(-1===h.id)break;if(c){this.treeIndex.input(h);break}this.applyInput(h);break;case f.IncrementalSource.MediaInteraction:e=p.getNode(h.id);if(!e)return this.debugNodeNotFound(h,h.id);var k=e;try{h.type===Z.Pause&&k.pause(),h.type===Z.Play&&(k.readyState>=HTMLMediaElement.HAVE_CURRENT_DATA?k.play():k.addEventListener("canplay",function(){k.play()}))}catch(n){this.config.showWarning&&console.warn("Failed to replay media interactions: "+(n.message||n))}break;case f.IncrementalSource.StyleSheetRule:e=p.getNode(h.id);if(!e)return this.debugNodeNotFound(h,h.id);var l=e,m=e.parentNode,x=this.fragmentParentMap.has(m);d=void 0;if(x){var v=this.fragmentParentMap.get(e.parentNode);d=document.createTextNode("");m.replaceChild(d,e);v.appendChild(e)}var t=l.sheet;h.adds&&h.adds.forEach(function(a){var b=a.rule;a=a.index;a=void 0===a?void 0:Math.min(a,t.rules.length);try{t.insertRule(b,a)}catch(E){}});h.removes&&h.removes.forEach(function(a){a=a.index;try{t.deleteRule(a)}catch(y){}});x&&d&&m.replaceChild(e,d);break;case f.IncrementalSource.CanvasMutation:if(!this.config.UNSAFE_replayCanvas)break;e=p.getNode(h.id);if(!e)return this.debugNodeNotFound(h,h.id);try{l=e.getContext("2d"),h.setter?l[h.property]=h.args[0]:(m=l[h.property],"drawImage"===h.property&&"string"===typeof h.args[0]&&(x=this.imageMap.get(a),h.args[0]=x),m.apply(l,h.args))}catch(n){this.warnCanvasMutationFailed(h,h.id,n)}break;case f.IncrementalSource.Font:try{v=new FontFace(h.family,h.buffer?new Uint8Array(JSON.parse(h.fontSource)):h.fontSource,h.descriptors),null===(d=this.iframe.contentDocument)||void 0===d?void 0:d.fonts.add(v)}catch(n){this.config.showWarning&&console.warn(n)}}};a.prototype.applyMutation=function(a,c){var b,e,d=this;a.removes.forEach(function(b){var c=p.getNode(b.id);if(!c)return d.warnNodeNotFound(a,b.id);var e=p.getNode(b.parentId);if(!e)return d.warnNodeNotFound(a,b.parentId);p.removeNodeFromMap(c);e&&((b=d.fragmentParentMap.get(e))&&b.contains(c)?b.removeChild(c):e.removeChild(c))});var f=A({},this.legacy_missingNodeRetryMap),l=[],m=function(a){if(!d.iframe.contentDocument)return console.warn("Looks like your replayer has been destroyed.");var b=p.getNode(a.parentId);if(!b)return l.push(a);var e=null;d.iframe.contentDocument.contains?e=d.iframe.contentDocument.contains(b):d.iframe.contentDocument.body.contains&&(e=d.iframe.contentDocument.body.contains(b));if(c&&e){e=document.createDocumentFragment();p.map[a.parentId]=e;for(d.fragmentParentMap.set(e,b);b.firstChild;)e.appendChild(b.firstChild);b=e}var g=e=null;a.previousId&&(e=p.getNode(a.previousId));a.nextId&&(g=p.getNode(a.nextId));var h;h=null;a.nextId&&(h=p.getNode(a.nextId));h=null===a.nextId||void 0===a.nextId||-1===a.nextId||h?!1:!0;if(h)return l.push(a);h=S(a.node,d.iframe.contentDocument,p.map,!0);-1===a.previousId||-1===a.nextId?f[a.node.id]={node:h,mutation:a}:(e&&e.nextSibling&&e.nextSibling.parentNode?b.insertBefore(h,e.nextSibling):g&&g.parentNode?b.contains(g)?b.insertBefore(h,g):b.insertBefore(h,null):b.appendChild(h),(a.previousId||a.nextId)&&d.legacy_resolveMissingNode(f,b,h,a))};a.adds.forEach(function(a){m(a)});for(var k=Date.now();l.length;){var x=za(l);l.length=0;if(500<Date.now()-k){this.warn("Timeout in the loop, please check the resolve tree data:",x);break}try{for(var v=(b=void 0,z(x)),t=v.next();!t.done;t=v.next()){var n=t.value;p.getNode(n.value.parentId)?pa(n,function(a){m(a)}):this.debug("Drop resolve tree since there is no parent for the root node.",n)}}catch(y){b={error:y}}finally{try{t&&!t.done&&(e=v["return"])&&e.call(v)}finally{if(b)throw b.error;}}}Object.keys(f).length&&Object.assign(this.legacy_missingNodeRetryMap,f);a.texts.forEach(function(b){var c=p.getNode(b.id);if(!c)return d.warnNodeNotFound(a,b.id);d.fragmentParentMap.has(c)&&(c=d.fragmentParentMap.get(c));c.textContent=b.value});a.attributes.forEach(function(b){var c=p.getNode(b.id);if(!c)return d.warnNodeNotFound(a,b.id);d.fragmentParentMap.has(c)&&(c=d.fragmentParentMap.get(c));for(var e in b.attributes)if("string"===typeof e){var g=b.attributes[e];try{null!==g?c.setAttribute(e,g):c.removeAttribute(e)}catch(ha){d.config.showWarning&&console.warn("An error occurred may due to the checkout feature.",ha)}}})};a.prototype.applyScroll=function(a){var b=p.getNode(a.id);if(!b)return this.debugNodeNotFound(a,a.id);if(b===this.iframe.contentDocument)this.iframe.contentWindow.scrollTo({top:a.y,left:a.x,behavior:"smooth"});else try{b.scrollTop=a.y,b.scrollLeft=a.x}catch(g){}};a.prototype.applyInput=function(a){var b=p.getNode(a.id);if(!b)return this.debugNodeNotFound(a,a.id);try{b.checked=a.isChecked,b.value=a.text}catch(g){}};a.prototype.legacy_resolveMissingNode=function(a,c,g,e){var b=e.previousId;e=e.nextId;b=b&&a[b];e=e&&a[e];if(b){var f=b.node,b=b.mutation;c.insertBefore(f,g);delete a[b.node.id];delete this.legacy_missingNodeRetryMap[b.node.id];(b.previousId||b.nextId)&&this.legacy_resolveMissingNode(a,c,f,b)}e&&(f=e.node,b=e.mutation,c.insertBefore(f,g.nextSibling),delete a[b.node.id],delete this.legacy_missingNodeRetryMap[b.node.id],(b.previousId||b.nextId)&&this.legacy_resolveMissingNode(a,c,f,b))};a.prototype.moveAndHover=function(a,c,g,e){this.mouse.style.left=c+"px";this.mouse.style.top=g+"px";this.drawMouseTail({x:c,y:g});c=p.getNode(e);if(!c)return this.debugNodeNotFound(a,e);this.hoverElements(c)};a.prototype.drawMouseTail=function(a){var b=this;if(this.mouseTail){var g=!0===this.config.mouseTail?ua:Object.assign({},ua,this.config.mouseTail),e=g.lineCap,d=g.lineWidth,f=g.strokeStyle,g=g.duration,l=function(){if(b.mouseTail){var a=b.mouseTail.getContext("2d");a&&b.tailPositions.length&&(a.clearRect(0,0,b.mouseTail.width,b.mouseTail.height),a.beginPath(),a.lineWidth=d,a.lineCap=e,a.strokeStyle=f,a.moveTo(b.tailPositions[0].x,b.tailPositions[0].y),b.tailPositions.forEach(function(b){return a.lineTo(b.x,b.y)}),a.stroke())}};this.tailPositions.push(a);l();setTimeout(function(){b.tailPositions=b.tailPositions.filter(function(b){return b!==a});l()},g)}};a.prototype.hoverElements=function(a){var b;for(null===(b=this.iframe.contentDocument)||void 0===b?void 0:b.querySelectorAll(".\\:hover").forEach(function(a){a.classList.remove(":hover")});a;)a.classList&&a.classList.add(":hover"),a=a.parentElement};a.prototype.isUserInteraction=function(a){return a.type!==f.EventType.IncrementalSnapshot?!1:a.data.source>f.IncrementalSource.Mutation&&a.data.source<=f.IncrementalSource.Input};a.prototype.backToNormal=function(){this.nextUserInteractionEvent=null;this.speedService.state.matches("normal")||(this.speedService.send({type:"BACK_TO_NORMAL"}),this.emitter.emit(f.ReplayerEvents.SkipEnd,{speed:this.speedService.state.context.normalSpeed}))};a.prototype.warnNodeNotFound=function(a,c){this.warn("Node with id '"+c+"' not found in",a)};a.prototype.warnCanvasMutationFailed=function(a,c,g){this.warn("Has error on update canvas '"+c+"'",a,g)};a.prototype.debugNodeNotFound=function(a,c){this.debug("[replayer]","Node with id '"+c+"' not found in",a)};a.prototype.warn=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];this.config.showWarning&&console.warn.apply(console,m(["[replayer]"],a))};a.prototype.debug=function(){for(var a=[],c=0;c<arguments.length;c++)a[c]=arguments[c];this.config.showDebug&&console.log.apply(console,m(["[replayer]"],a))};return a}(),mb=ea.addCustomEvent,nb=ea.freezePage;f.Replayer=ta;f.addCustomEvent=mb;f.freezePage=nb;f.mirror=p;f.record=ea;f.utils=ib;Object.defineProperty(f,"__esModule",{value:!0});return f}({});function ecbackSDK(){function f(f,v){W=P.length;var l={};l.event=T.node_record;l.eventId=m.produceId();l.nodeId=H;l.data=v;J>=W||(J=W,m.send(l,function(){f&&f()}))}function z(f){f=JSON.stringify(f);f=f.replace(/\\n/g,"");f=f.replace(/\s+/g," ");f=f.replace(/ }/g,"}");f=f.replace(/: /g,":");f=f.replace(/, /g,",");return f=f.replace(/{ /g,"{")}var w=0,m=this,T={task_init:"task_init",task_update:"task_update",node_start:"node_start",node_end:"node_end",node_record:"node_record"},V=window.ecConfig||void 0,P=[],Q=!1,R=[],L=[],N=4,W=0,fa="",u="",Y="0",I="",H="",J=0;m.intervalID=null;m.init=function(f,v){var l={};l.eventId=m.produceId();l.businessId=f.businessId;l.event=T.task_init;m.send(l,function(l){l?void 0!=v&&v(f):console.log("SDK\u521d\u59cb\u5931\u8d25")})};m.start=function(){w=J=0;Q=!1;P=[];R=[];N=2;m.endEcback=rrweb.record({emit:function(l){w+=1;l.pageIndex=w;P.push(l);R.push(l);Q?m.endEcback():R.length>=N&&!(2==N&&w>N)&&(f(function(){},R),R=[])},recordCanvas:!0})};m.pageinit=function(f,v){var l={};l.event=T.node_start;l.eventId=m.produceId();l.nodeId=m.produceId();H=l.nodeId;l.nodeCode=f.nodeCode;m.send(l,function(l){l&&(f.businessId&&m.update({businessId:f.businessId}),m.start(),v&&v())})};m.pageend=function(f,v){Q=!0;P=[];R=[];J=0;var l={};l.event=T.node_end;l.eventId=m.produceId();l.nodeId=H;recordCount=f.recordCount;l.nodeCode=f.nodeCode;l.taskEndFlag=f.taskEndFlag;l.data=P.slice(J,W)||[];m.send(l,function(f){void 0!=v&&v(f);f&&(P=[])})};m.clearInterval=function(){null!=m.intervalID&&(clearInterval(m.intervalID),m.endEcback(),m.intervalID=null)};m.action=function(f,v){if(V){f.init&&m.clearStorage();u=f.businessId;var l=m.taskCode(f.productCode,f.businessId);fa=f.productCode;Y=f.taskEndFlag?f.taskEndFlag:"0";var w=0==Number(f.event)?m.pageinit:m.pageend;l&&0===Number(f.event)?m.init(f,function(f){w(f,v)}):w(f,v)}else console.error("\u7f3a\u5c11\u914d\u7f6e\u6587\u4ef6")};m.update=function(f){var l={};l.event=T.task_update;l.eventId=m.produceId();l.businessData=f;m.send(l,function(f){})};m.send=function(f,v){f.timestamp=(new Date).getTime();f.recordCount=J;f.taskId=I;f.taskEndFlag=Y;f.data=f.data;0<L.length&&(f.data=L.concat(f.data),L=[]);ajax({url:V.apiUrl,type:"POST",data:z(f),dataType:"json",timeout:3E4,contentType:"application/json",success:function(l){m.fial_report_events=[];l=JSON.parse(l);l.taskId!=I&&(I=l.taskId,sessionStorage.setItem("taskId1",I+"-"+fa),sessionStorage.setItem("taskId2",I+"-"+u));switch(f.event){case "task_init":J=0;break;case "node_start":J=0;break;case "node_record":N=6}v&&v(!0)},error:function(l){N=6;L=L.concat(f.data);console.log("\u4e0a\u62a5\u5931\u8d25,\u4e22\u5931\u6570\u636e",L);void 0!=v&&v(!1)}})};m.clearStorage=function(){var f=window.sessionStorage;f.removeItem("taskId1");f.removeItem("taskId2")};m.taskCode=function(f,v,u){var l=window.sessionStorage,w=l.getItem("taskId1")||"",x=l.getItem("taskId2")||"",z=!1;u&&(l.removeItem("taskId1"),l.removeItem("taskId2"));if(w||x)w.split("-")[1]==f?I=w.split("-")[0]:(I=m.produceId(),z=!0);""==w&&""==x&&(I=m.produceId(),z=!0);l.setItem("taskId1",I+"-"+f);l.setItem("taskId2",I+"-"+v);return z};m.produceId=function(){return"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".replace(/x/g,function(f){return"0123456789abcdef".charAt(Math.floor(16*Math.random()))}).replace(/-/g,"")};m.PlatformFun=function(){var f=navigator.platform,m=navigator.userAgent.toLowerCase(),u=0==f.indexOf("Win"),w=0==f.indexOf("Mac");"X11"==f||f.indexOf("Linux");f=null!=navigator.userAgent.match(/iPad/i)?!0:!1;return u||w||xll||f?"H5":"micromessenger"==m.match(/MicroMessenger/i)?"WXH5":"Mobile"}}function ajax(f){f=f||{};f.type=(f.type||"GET").toUpperCase();f.dataType=f.dataType||"json";var z=f.data,w;window.XMLHttpRequest?w=new XMLHttpRequest:window.ActiveObject&&(w=new ActiveXobject("Microsoft.XMLHTTP"));"GET"==f.type?(w.open("GET",f.url+"?"+z,!0),w.send(null)):"POST"==f.type&&(w.open("post",f.url,!0),w.setRequestHeader("Content-type","application/json"),w.send(z));setTimeout(function(){4!=w.readySate&&w.abort()},f.timeout);w.onreadystatechange=function(){if(4==w.readyState){var m=w.status;200<=m&&300>m||304==m?f.success&&f.success(w.responseText,w.responseXML):f.error&&f.error(m)}}}function formatParams(f){var z=[],w;for(w in f)z.push(encodeURIComponent(w)+"\x3d"+encodeURIComponent(f[w]));z.push(("v\x3d"+Math.random()).replace(".",""));return z.join("\x26")}window.ecbacksdk=new ecbackSDK;