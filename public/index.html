<!DOCTYPE html>
<html lang="en" >
	<head>
		<meta charset="utf-8">
		<!-- 禁止移动端缩放 ---iPhoneX 安全区域 viewport-fit=cover" safe-area-inset-*样式生效-->
		<meta
			name="viewport"
			content="width=device-width,
			initial-scale=1.0,
			maximum-scale=1.0,
			minimum-scale=1.0,
			user-scalable=no,
			viewport-fit=cover"
		/>
        <meta name="referrer" content="origin">
        
		<!-- 用于设定禁止浏览器从本地机的缓存中调阅页面内容 -->
		<meta http-equiv="Pragma" content="no-cache">
		<!-- Cache-Control指定请求和响应遵循的缓存机制。在请求消息或响应消息中设置Cache-Control并不会修改另一个消息处理过程中的缓存处理过程。 -->
		<meta http-equiv="Cache-Control" content="no-cache">
		<!-- 可以用于设定网页的到期时间 -->
		<meta http-equiv="Expires" content="0">
		<meta name="format-detection" content="telephone=no"/>
		<meta name="referrer" content="no-referrer-when-downgrade">
		<link rel="icon" href="<%= BASE_URL %>./favicon.png">
		<title>智能理赔</title>
        <script src="<%= BASE_URL %>./libs/jquery-2.1.0.js"></script>
        <script src="<%= BASE_URL %>./libs/newAanysign/anysignWebInterface.js"></script>
        <script src="<%= BASE_URL %>./libs/newAanysign/anysign_web_html2.js"></script>
        <script src="<%= BASE_URL %>./libs/TXIVHSDK_Web_Cloud_V5.1.10_Release.js"></script>
        <!-- 腾讯语音识别 -->
	    <script type="text/javascript" src="<%= BASE_URL %>./libs/TXASR/speechrecognizer.js"></script>
        <script src="<%= BASE_URL %>./libs/uuid.min.js"></script>
        <script src="<%= BASE_URL %>./libs/qrcode.js"></script>
		<script src='https://web.sdk.qcloud.com/trtc/webrtc/v5/dist/trtc.js'></script>
		<style>
            body{
                position: relative;
                background-color: #fff;
            }

            #loading{
                position: fixed;
                z-index: 99999;
                display: block;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background-color: #fff;
            
            }

            #caseBlanche {
                position: relative;
                width : 3.4rem;
                height : 3.4rem;
                margin: 0 auto;
                margin-top: 120px;
                background-color : #fff;
            }

            #test {
                position : absolute;
                top : 6px;
                left : 4px;
                width : 6px;
                height : 6px;
                background-color : #6076FF;
                border-radius : 50%;
            }

            #rond {
                width : 3.4rem;
                height  : 3.4rem;
                border : 1px solid #ECEBEB;
                border-radius : 50%;
                animation : rond 2s infinite;
                animation : rond 2s infinite;
            }
            @keyframes rond {
                0% {transform : rotate(0deg);}
                100% {transform : rotate(360deg);}
            }

            @keyframes rond {
                0% {transform : rotate(0deg);}
                100% {transform : rotate(360deg);}
            }

            #caseBlanche #load {
                position : absolute;
                top : 0.9rem;
                left :0.9rem;
                width:1.7rem;
                height:1.7rem;
                overflow: hidden;
                font-family : calibri;
                color : #fff;
                text-align : center;
                border-radius: 40px;
            }

            .loading-logo{
                width:100%;
                height:100%;
                overflow: hidden;
                border-radius: 50px;
            }

            .loading-title{
                margin-top: 0.6rem;
                font-size: 0.8125rem;
                font-weight: bold;
                text-align: center;
            }
        </style>
        <script>

            ////禁止微信缩放字体
            //(function () {
            //    function handleFontSize() {
            //        // 设置网页字体为默认大小
            //        window.WeixinJSBridge.invoke('setFontSizeCallback', {
            //            fontSize: 0
            //        });
            //        // 重写设置网页字体大小的事件
            //        window.WeixinJSBridge.on('menu:setfont', function () {
            //            window.WeixinJSBridge.invoke('setFontSizeCallback', {
            //                fontSize: 0
            //            });
            //        });
            //    }
            //    if (typeof WeixinJSBridge === 'object' && typeof window.WeixinJSBridge.invoke === 'function') {
            //        handleFontSize();
            //    } else if (document.addEventListener) {
            //        document.addEventListener('WeixinJSBridgeReady', handleFontSize, false);
            //    } else if (document.attachEvent) {
            //        document.attachEvent('WeixinJSBridgeReady', handleFontSize);
            //        document.attachEvent('onWeixinJSBridgeReady', handleFontSize);
            //    }
            //})();

    </script>
	</head>

	<body>
		<div id="loading">
            <div id="caseBlanche">
                <div id="rond">
                <div id="test"></div>
                </div>
                <div id="load">
                <img class="loading-logo" src="./favicon.png">
                </div>
            </div>
            <div class="loading-title">页面加载中...</div>
        </div>
		<div id="app"></div>
	</body>
</html>
