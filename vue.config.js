/*
 * <AUTHOR> a大师兄
 * @Date         : 2021-01-08 13:19:08
 * @LastEditors  : a大师兄
 * @LastEditTime : 2023-06-16 16:37:28
 * @Description  : vue.config.js
 */

var path = require('path');
var moment = require('moment');
//自动添加样式前缀
const autoprefixer = require('autoprefixer');
//打包成压缩包
const FileManagerPlugin = require('filemanager-webpack-plugin');
//rem适配
const pxtorem = require('postcss-pxtorem');
//自定义包名
const outputDirName = `js-shb-${process.env.VUE_APP_UNIT}-${moment(new Date()).format('YYYYMMDDHHmmss')}`
const isProduction = process.env.VUE_APP_UNIT === 'prd';

module.exports = {
	//lintOnSave：{ type:Boolean default:true } 问你是否使用eslint
	lintOnSave: true,
	// 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
	productionSourceMap: !isProduction,
	//打包app时放开该配
	publicPath: process.env.VUE_APP_PATH_URL,
	//app入口页
	pages: {
		index: {
			entry: 'src/main.js',
			template: 'public/index.html',
			filename: 'index.html',
			title: '活动签到系统',
		},
	},
	devServer: {
		port: 3000,
		proxy: {
			//配置代理
			'/api': {
				target: 'http://19c06o3764.imwork.net', //请求本地 需要后台项目
				changeOrigin: true, //允许跨域
				pathRewrite: {
					'^/api': ''
				}
			},
			// 腾讯地图
			"/place": {
				target: "https://apis.map.qq.com",
				changeOrigin: true,
				ws: true,
				pathRewrite: {
					'^/place': '' //重写接口
				}
			},
		}
	},

	configureWebpack: config => {
		if (!isProduction) {
			return
		}
		const PrerenderSPAPlugin = require('prerender-spa-plugin');
		const Renderer = PrerenderSPAPlugin.PuppeteerRenderer;
		const MomentLocalesPlugin = require('moment-locales-webpack-plugin')
		config.plugins = (config.plugins || []).concat([
			// 或者：剥离除 “en”、“es-us” 和 “ru” 以外的所有语言环境
			new MomentLocalesPlugin({
				localesToKeep: ['zh-cn']
			}),
			//初始化 filemanager-webpack-plugin 插件实例
			new FileManagerPlugin({
				events: {
					onEnd: {
						archive: [ //然后我们选择dist文件夹将之打包成dist.zip并放在根目录
							{
								source: `./dist`,
								destination: `./distZip/${outputDirName}.zip`
							},
						],
					}
				}

			}),
			// 预渲染配置
			//new PrerenderSPAPlugin({
			//	// 这个目录只能有一级，如果目录层次大于一级，在生成的时候不会有任何错误提示，在预渲染的时候只会卡着不动
			//	staticDir: path.join(__dirname, 'dist'),
			//	outputDir: path.join(__dirname, 'dist'),
			//	indexPath: path.join(__dirname, 'dist', 'index.html'),
			//	// 对应自己的路由文件，比如a有参数，就需要写成 /a/param1。
			//	routes: ['/plist'],
			//	postProcess(context) {
			//		// Remove /index.html from the output path if the dir name ends with a .html file extension.
			//		// For example: /dist/dir/special.html/index.html -> /dist/dir/special.html
			//		console.log('outputPath>>>>>', context.route)
			//		context.outputPath = path.join(__dirname, 'dist', `${context.route}.html`)

			//		return context
			//	},
			//	renderer: new Renderer({
			//		inject: {
			//			foo: 'bar'
			//		},
			//		headless: true,
			//		renderAfterTime: 10000,
			//		renderAfterDocumentEvent: 'render-event'
			//	})
			//})
		]);
		//生产环境取消 console.log
		config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
	},
	chainWebpack: config => {
		config.plugins.delete('prefetch');
		/* 设置别名 */
		config.resolve.alias
			.set('@', path.resolve('src'))
			.set('@views', path.resolve('./src/views'))
			.set('assets', path.resolve(__dirname, './src/assets'))
			.set('@com', path.resolve(__dirname, './src/components'))
			.set('@product', path.resolve(__dirname, './src/views/product'))
			.set('@p-com', path.resolve(__dirname, './src/views/product/components'))
			.set('@public', path.resolve(__dirname, './public'));
		// 生产环境，开启js\css压缩
		if (process.env.NODE_ENV === 'production') {
			const CompressionPlugin = require('compression-webpack-plugin')
			config.plugin('compressionPlugin').use(new CompressionPlugin({
				test: /\.js$|.\css|.\less/, // 匹配文件名
				filename: '[path].gz[query]',
				threshold: 5240, // 对超过10k的数据压缩
				algorithm: 'gzip',
				minRatio: 0.8,
				deleteOriginalAssets: false // 删除源文件
			}))
		}
	},
	css: {
		extract: true,
		sourceMap: false,
		modules: false,
		loaderOptions: {
			less: {
				lessOptions: {
					modifyVars: {
						// 覆盖样式变量
						hack: `true; @import "${path.join(__dirname, './src/style/theme.less')}"`,
					},
				},
			},
		},
	},
};